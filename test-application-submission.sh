#!/bin/bash

# Test script to verify application submission functionality
# This simulates the frontend making requests to the backend

API_BASE_URL="http://localhost:3000/api"

echo "🧪 Testing Application Submission Flow..."
echo ""

# Step 1: Register a test user
echo "1️⃣ Registering test user..."
REGISTER_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123",
    "firstName": "Test",
    "lastName": "User"
  }')

echo "Registration response: $REGISTER_RESPONSE"

# Extract token from response
TOKEN=$(echo "$REGISTER_RESPONSE" | grep -o '"token":"[^"]*' | cut -d'"' -f4)

if [ -n "$TOKEN" ]; then
  echo "✅ User registered successfully"
  echo "🔑 Token: ${TOKEN:0:20}..."

  # Step 2: Submit an application
  echo ""
  echo "2️⃣ Submitting application..."

  TIMESTAMP=$(date +%s)
  APPLICATION_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/ai/application/submit" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $TOKEN" \
    -d "{
      \"applicationId\": \"test_app_${TIMESTAMP}\",
      \"method\": \"manual\",
      \"applicationData\": {
        \"listingId\": \"test_listing_${TIMESTAMP}\",
        \"message\": \"I am very interested in this property and would like to schedule a viewing.\",
        \"subject\": \"Application for Beautiful Apartment\",
        \"propertyTitle\": \"Beautiful 2-bedroom apartment in Amsterdam\"
      }
    }")

  echo "Application response: $APPLICATION_RESPONSE"

  # Check if application was successful
  if echo "$APPLICATION_RESPONSE" | grep -q '"success":true'; then
    echo ""
    echo "✅ Application submitted successfully!"
    echo "🎉 SUCCESS: Application submission is working correctly!"
    echo "✨ The database save errors have been resolved!"
    exit 0
  else
    echo ""
    echo "❌ Application submission failed"
    exit 1
  fi
else
  echo "❌ User registration failed - no token received"
  exit 1
fi
