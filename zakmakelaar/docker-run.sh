#!/usr/bin/env bash
set -euo pipefail

IMG=zakmakelaar-funda:latest

# Build image
DOCKER_BUILDKIT=1 docker build -f Dockerfile.funda -t "$IMG" ..

# Run container with environment overrides and mount artifacts out
docker run --rm \
  -e PROPERTY_URL="${PROPERTY_URL:-https://www.funda.nl/detail/huur/utrecht/appartement-eendrachtlaan-46-d/43728488/}" \
  -e FIRST_NAME="${FIRST_NAME:-Wellis}" \
  -e LAST_NAME="${LAST_NAME:-Hant}" \
  -e EMAIL="${EMAIL:-<EMAIL>}" \
  -e PHONE="${PHONE:-030 686 62 00}" \
  -e MESSAGE="${MESSAGE:-}" \
  -v "$(pwd)/artifacts:/app/zakmakelaar-backend/artifacts" \
  "$IMG"

