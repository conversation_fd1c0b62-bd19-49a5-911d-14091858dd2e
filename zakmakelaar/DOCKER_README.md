# Docker Setup for ZakMakelaar Backend

This guide explains how to run the ZakMakelaar backend using Docker.

## Prerequisites

- Docker Desktop installed and running
- Docker Compose (included with Docker Desktop)

## Quick Start

1. **Copy environment file:**
   ```bash
   cp .env.docker .env
   ```

2. **Update environment variables:**
   Edit `.env` file and add your API keys:
   - `SENDGRID_API_KEY` - for email notifications
   - `TWILIO_ACCOUNT_SID` and `TWILIO_AUTH_TOKEN` - for WhatsApp notifications
   - `OPENAI_API_KEY` - for AI features
   - `JWT_SECRET` - change to a secure random string

3. **Start all services:**
   ```bash
   docker-compose up -d
   ```

4. **Check service status:**
   ```bash
   docker-compose ps
   ```

## Services

The Docker setup includes:

- **Backend API** - Node.js Express server (port 3000)
- **MongoDB** - Database (port 27017)
- **Redis** - Cache (port 6379)

## Useful Commands

### Start services
```bash
docker-compose up -d
```

### Stop services
```bash
docker-compose down
```

### View logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f mongodb
docker-compose logs -f redis
```

### Rebuild backend after code changes
```bash
docker-compose build backend
docker-compose up -d backend
```

### Access MongoDB shell
```bash
docker exec -it zakmakelaar-mongodb mongosh -u admin -p password123 --authenticationDatabase admin
```

### Access Redis CLI
```bash
docker exec -it zakmakelaar-redis redis-cli
```

### Clean up everything (including data)
```bash
docker-compose down -v
docker system prune -f
```

## Health Checks

The backend includes health checks. Check status with:
```bash
curl http://localhost:3000/health
```

## Data Persistence

- MongoDB data is persisted in Docker volume `mongodb_data`
- Redis data is persisted in Docker volume `redis_data`
- Application uploads are mounted to `./uploads`
- Application logs are mounted to `./logs`

## Production Considerations

1. **Security:**
   - Change default MongoDB credentials
   - Use strong JWT secret
   - Configure proper CORS origins
   - Use environment-specific API keys

2. **Performance:**
   - Adjust MongoDB and Redis memory limits
   - Configure proper logging levels
   - Set up monitoring and alerting

3. **Backup:**
   - Regular MongoDB backups
   - Backup environment configuration

## Troubleshooting

### Backend won't start
- Check logs: `docker-compose logs backend`
- Verify environment variables in `.env`
- Ensure MongoDB and Redis are running

### Database connection issues
- Verify MongoDB is accessible: `docker-compose logs mongodb`
- Check MongoDB credentials in `.env`

### Memory issues
- Increase Docker Desktop memory allocation
- Monitor container resource usage: `docker stats`

### Port conflicts
- Change ports in `docker-compose.yml` if needed
- Ensure no other services are using ports 3000, 27017, 6379
