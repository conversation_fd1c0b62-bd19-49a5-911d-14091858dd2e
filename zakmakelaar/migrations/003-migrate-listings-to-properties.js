/**
 * Migration script to convert existing Listing records to the enhanced Property model
 * 
 * This script migrates data from the legacy Listing model to the new enhanced Property model
 * with unified schema support. It preserves all original data and adds the necessary metadata.
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Listing = require('../src/models/Listing');
const EnhancedProperty = require('../src/models/EnhancedProperty');
const { createMinimalProperty } = require('../src/schemas/unifiedPropertySchema');

async function migrateListingsToProperties() {
  try {
    console.log('Starting migration of Listings to enhanced Properties...');
    
    // Connect to MongoDB if not already connected
    if (mongoose.connection.readyState === 0) {
      const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/zakmakelaar';
      await mongoose.connect(uri);
      console.log('Connected to MongoDB');
    }
    
    // Get all listings
    const listings = await Listing.find({});
    console.log(`Found ${listings.length} listings to migrate`);
    
    // Process listings in batches to avoid memory issues
    const batchSize = 100;
    const totalBatches = Math.ceil(listings.length / batchSize);
    
    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const start = batchIndex * batchSize;
      const end = Math.min(start + batchSize, listings.length);
      const batch = listings.slice(start, end);
      
      console.log(`Processing batch ${batchIndex + 1}/${totalBatches} (${batch.length} listings)`);
      
      for (const listing of batch) {
        try {
          // Check if this listing has already been migrated
          const existingProperty = await EnhancedProperty.findOne({
            originalListingId: listing._id
          });
          
          if (existingProperty) {
            console.log(`Listing ${listing._id} already migrated, skipping`);
            skippedCount++;
            continue;
          }
          
          // Create unified schema data from listing
          const unifiedData = createMinimalProperty({
            title: listing.title,
            description: listing.description,
            price: listing.price,
            location: listing.location,
            source: listing.source,
            url: listing.url,
            propertyType: listing.propertyType || 'woning',
            size: listing.size,
            rooms: listing.rooms,
            bedrooms: listing.bedrooms,
            year: listing.year,
            interior: listing.interior,
            images: listing.images || [],
            dateAdded: listing.dateAdded || listing.timestamp
          });
          
          // Extract numeric values where possible
          let numericSize = null;
          if (listing.size) {
            const sizeMatch = listing.size.match(/\\d+/);
            if (sizeMatch) {
              numericSize = parseInt(sizeMatch[0]);
            }
          }
          
          let numericRooms = null;
          if (listing.rooms) {
            const roomsMatch = listing.rooms.match(/\\d+/);
            if (roomsMatch) {
              numericRooms = parseInt(roomsMatch[0]);
            }
          }
          
          let numericBedrooms = null;
          if (listing.bedrooms) {
            const bedroomsMatch = listing.bedrooms.match(/\\d+/);
            if (bedroomsMatch) {
              numericBedrooms = parseInt(bedroomsMatch[0]);
            }
          }
          
          // Extract price as number if possible
          let numericPrice = null;
          if (listing.price) {
            const priceMatch = listing.price.match(/\\d+/g);
            if (priceMatch) {
              numericPrice = parseInt(priceMatch.join(''));
            }
          }
          
          // Create new property from unified data
          const property = await EnhancedProperty.createFromUnifiedSchema(unifiedData);
          
          // Set additional fields
          property.isLegacyMigrated = true;
          property.originalListingId = listing._id;
          property.status = 'active'; // Assume active status for migrated listings
          
          // Set source metadata
          property.sourceMetadata = {
            website: listing.source,
            externalId: null, // Not available in legacy listings
            scraperId: null, // Not available in legacy listings
            scrapedAt: listing.dateAdded || listing.timestamp || new Date(),
            lastUpdated: new Date(),
            version: 1
          };
          
          // Store raw data for reference
          property.rawData = {
            original: listing.toObject(),
            processed: unifiedData,
            metadata: { migrated: true, migratedAt: new Date() }
          };
          
          // Set data quality indicators
          const requiredFields = ['title', 'price', 'location', 'url', 'source'];
          const optionalFields = ['description', 'size', 'rooms', 'bedrooms', 'propertyType', 'year', 'interior', 'images'];
          
          const requiredFieldsPresent = requiredFields.filter(field => listing[field]).length;
          const optionalFieldsPresent = optionalFields.filter(field => {
            if (field === 'images') {
              return listing[field] && listing[field].length > 0;
            }
            return listing[field];
          }).length;
          
          const completeness = Math.round(
            ((requiredFieldsPresent / requiredFields.length) * 0.7 + 
             (optionalFieldsPresent / optionalFields.length) * 0.3) * 100
          );
          
          property.dataQuality = {
            completeness,
            accuracy: 70, // Assume moderate accuracy for migrated data
            lastValidated: new Date(),
            validationErrors: [],
            validationWarnings: []
          };
          
          // Set processing metadata
          property.processingMetadata = {
            transformationVersion: '1.0.0-migration',
            processingTime: 0,
            processingDate: new Date(),
            errors: [],
            warnings: []
          };
          
          // Save the property
          await property.save();
          migratedCount++;
          
          if (migratedCount % 10 === 0) {
            console.log(`Migrated ${migratedCount} listings so far`);
          }
        } catch (error) {
          console.error(`Error migrating listing ${listing._id}:`, error);
          errorCount++;
        }
      }
    }
    
    console.log('Migration summary:');
    console.log(`- Total listings: ${listings.length}`);
    console.log(`- Successfully migrated: ${migratedCount}`);
    console.log(`- Skipped (already migrated): ${skippedCount}`);
    console.log(`- Errors: ${errorCount}`);
    
    console.log('Migration completed successfully');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateListingsToProperties()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateListingsToProperties };