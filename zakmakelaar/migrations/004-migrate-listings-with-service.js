/**
 * Migration script using the MigrationService to convert Listing records to the enhanced Property model
 * 
 * This script provides a more robust migration process with validation, batch processing,
 * and rollback functionality.
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models and services
const Listing = require('../src/models/Listing');
const EnhancedProperty = require('../src/models/EnhancedProperty');
const { MigrationService } = require('../src/services/migrationService');
const { SchemaTransformer } = require('../src/services/schemaTransformer');
const { ValidationEngine } = require('../src/services/validationEngine');
const { FieldMappingRegistry } = require('../src/services/fieldMappingRegistry');
const { MappingConfigLoader } = require('../src/services/mappingConfigLoader');
const { getSchema } = require('../src/schemas/unifiedPropertySchema');

async function migrateListingsWithService() {
  try {
    console.log('Starting migration of Listings to enhanced Properties using MigrationService...');
    
    // Connect to MongoDB if not already connected
    if (mongoose.connection.readyState === 0) {
      const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/zakmakelaar';
      await mongoose.connect(uri);
      console.log('Connected to MongoDB');
    }
    
    // Create field mapping registry and load mappings
    const mappingRegistry = new FieldMappingRegistry();
    const mappingLoader = new MappingConfigLoader(mappingRegistry);
    
    try {
      await mappingLoader.loadMappings();
      console.log('Loaded field mappings');
    } catch (error) {
      console.warn('Could not load field mappings, using default conversion:', error.message);
    }
    
    // Create schema transformer and validation engine
    let transformer = null;
    let validator = null;
    
    try {
      transformer = new SchemaTransformer(mappingRegistry);
      validator = new ValidationEngine(getSchema());
      console.log('Created schema transformer and validation engine');
    } catch (error) {
      console.warn('Could not create transformer or validator, using default conversion:', error.message);
    }
    
    // Create migration service
    const migrationService = new MigrationService(Listing, EnhancedProperty, {
      batchSize: 50,
      preserveOriginal: true,
      validationLevel: 'standard', // 'none', 'standard', 'strict'
      logProgress: true,
      transformationVersion: '1.0.0-migration-service',
      transformer,
      validator
    });
    
    // Run migration
    console.log('Starting migration process...');
    const migrationStats = await migrationService.migrateExistingData();
    
    console.log('Migration completed successfully');
    console.log('Migration statistics:', JSON.stringify(migrationStats, null, 2));
    
    return migrationStats;
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Function to roll back migration if needed
async function rollbackMigration() {
  try {
    console.log('Starting rollback of migrated properties...');
    
    // Connect to MongoDB if not already connected
    if (mongoose.connection.readyState === 0) {
      const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/zakmakelaar';
      await mongoose.connect(uri);
      console.log('Connected to MongoDB');
    }
    
    // Create migration service
    const migrationService = new MigrationService(Listing, EnhancedProperty, {
      batchSize: 50,
      logProgress: true
    });
    
    // Run rollback
    console.log('Starting rollback process...');
    const rollbackStats = await migrationService.rollbackMigration();
    
    console.log('Rollback completed successfully');
    console.log('Rollback statistics:', JSON.stringify(rollbackStats, null, 2));
    
    return rollbackStats;
  } catch (error) {
    console.error('Rollback failed:', error);
    throw error;
  }
}

// Run migration if called directly
if (require.main === module) {
  // Check for rollback flag
  const shouldRollback = process.argv.includes('--rollback');
  
  if (shouldRollback) {
    rollbackMigration()
      .then(() => {
        console.log('Rollback script completed');
        process.exit(0);
      })
      .catch((error) => {
        console.error('Rollback script failed:', error);
        process.exit(1);
      });
  } else {
    migrateListingsWithService()
      .then(() => {
        console.log('Migration script completed');
        process.exit(0);
      })
      .catch((error) => {
        console.error('Migration script failed:', error);
        process.exit(1);
      });
  }
}

module.exports = { migrateListingsWithService, rollbackMigration };