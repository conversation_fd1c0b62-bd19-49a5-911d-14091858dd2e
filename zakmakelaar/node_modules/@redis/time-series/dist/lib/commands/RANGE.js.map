{"version": 3, "file": "RANGE.js", "sourceRoot": "", "sources": ["../../../lib/commands/RANGE.ts"], "names": [], "mappings": ";;;AAEA,uCAA0G;AAI7F,QAAA,4BAA4B,GAAG;IAC1C,GAAG,EAAE,GAAG;IACR,MAAM,EAAE,GAAG;IACX,GAAG,EAAE,GAAG;CACT,CAAC;AAsBF,SAAgB,mBAAmB,CACjC,MAAqB,EACrB,aAAwB,EACxB,WAAsB,EACtB,OAAwB;IAExB,MAAM,CAAC,IAAI,CACT,IAAA,oCAA0B,EAAC,aAAa,CAAC,EACzC,IAAA,oCAA0B,EAAC,WAAW,CAAC,CACxC,CAAC;IAEF,IAAI,OAAO,EAAE,MAAM,EAAE,CAAC;QACpB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxB,CAAC;IAED,IAAI,OAAO,EAAE,YAAY,EAAE,CAAC;QAC1B,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5B,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,IAAA,oCAA0B,EAAC,SAAS,CAAC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,IAAI,OAAO,EAAE,eAAe,EAAE,CAAC;QAC7B,MAAM,CAAC,IAAI,CACT,iBAAiB,EACjB,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,EACtC,OAAO,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,CACvC,CAAC;IACJ,CAAC;IAED,IAAI,OAAO,EAAE,KAAK,KAAK,SAAS,EAAE,CAAC;QACjC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,OAAO,EAAE,WAAW,EAAE,CAAC;QACzB,IAAI,OAAO,EAAE,KAAK,KAAK,SAAS,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAA,oCAA0B,EAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,CAAC,IAAI,CACT,aAAa,EACb,OAAO,CAAC,WAAW,CAAC,IAAI,EACxB,IAAA,oCAA0B,EAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAC3D,CAAC;QAEF,IAAI,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CACT,iBAAiB,EACjB,OAAO,CAAC,WAAW,CAAC,eAAe,CACpC,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;YAC9B,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;AACH,CAAC;AAxDD,kDAwDC;AAED,SAAgB,uBAAuB,CACrC,MAAqB,EACrB,GAAkB,EAClB,aAAwB,EACxB,WAAsB,EACtB,OAAwB;IAExB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACpB,mBAAmB,CAAC,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;AACnE,CAAC;AATD,0DASC;AAED,kBAAe;IACb,YAAY,EAAE,IAAI;IAClB;;;OAGG;IACH,YAAY,CAAC,GAAG,IAAgD;QAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAEvB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxB,uBAAuB,CAAC,GAAG,IAAI,CAAC,CAAC;IACnC,CAAC;IACD,cAAc,EAAE;QACd,CAAC,CAAC,KAAkC;YAClC,OAAO,+BAAqB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;QACD,CAAC,CAAC,KAAsB;YACtB,OAAO,+BAAqB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;KACF;CACyB,CAAC"}