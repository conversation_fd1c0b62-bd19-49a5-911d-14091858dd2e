{"version": 3, "file": "ALTER.js", "sourceRoot": "", "sources": ["../../../lib/commands/ALTER.ts"], "names": [], "mappings": ";;AAGA,uCAA2I;AAK3I,kBAAe;IACb,YAAY,EAAE,KAAK;IACnB;;;;;OAKG;IACH,YAAY,CAAC,MAAqB,EAAE,GAAkB,EAAE,OAAwB;QAC9E,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACxB,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEpB,IAAA,gCAAsB,EAAC,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;QAEnD,IAAA,gCAAsB,EAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;QAEpD,IAAA,8BAAoB,EAAC,MAAM,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAExD,IAAA,6BAAmB,EAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAE7C,IAAA,6BAAmB,EAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IACD,cAAc,EAAE,SAAqD;CAC3C,CAAC"}