"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const MRANGE_WITHLABELS_GROUPBY_1 = __importStar(require("./MRANGE_WITHLABELS_GROUPBY"));
exports.default = {
    IS_READ_ONLY: MRANGE_WITHLABELS_GROUPBY_1.default.IS_READ_ONLY,
    /**
     * Gets samples for time series matching a filter with labels and grouping (in reverse order)
     * @param parser - The command parser
     * @param fromTimestamp - Start timestamp for range
     * @param toTimestamp - End timestamp for range
     * @param filter - Filter to match time series keys
     * @param groupBy - Group by parameters
     * @param options - Optional parameters for the command
     */
    parseCommand: (0, MRANGE_WITHLABELS_GROUPBY_1.createMRangeWithLabelsGroupByTransformArguments)('TS.MREVRANGE'),
    transformReply: MRANGE_WITHLABELS_GROUPBY_1.default.transformReply,
};
//# sourceMappingURL=MREVRANGE_WITHLABELS_GROUPBY.js.map