{"name": "@sendgrid/mail", "description": "<PERSON><PERSON>lio SendGrid NodeJS mail service", "version": "8.1.5", "author": "Twilio SendGrid <<EMAIL>> (sendgrid.com)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Swift <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "homepage": "https://sendgrid.com", "repository": {"type": "git", "url": "git://github.com/sendgrid/sendgrid-nodejs.git"}, "main": "index.js", "engines": {"node": ">=12.*"}, "publishConfig": {"access": "public"}, "dependencies": {"@sendgrid/client": "^8.1.5", "@sendgrid/helpers": "^8.0.0"}, "tags": ["http", "rest", "api", "mail", "sendgrid"], "gitHead": "2bac86884f71be3fb19f96a10c02a1fb616b81fc"}