const mongoose = require("mongoose");
const ApplicationQueue = require("./src/models/ApplicationQueue");
const ApplicationResult = require("./src/models/ApplicationResult");
require("dotenv").config();

async function backfillApplicationResults() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGO_URI);
    console.log("Connected to MongoDB");

    // Get auto-application service singleton
    const autoApplicationService = require("./src/services/autoApplicationService");

    // Find completed and failed applications that don't have results
    const completedApplications = await ApplicationQueue.find({ 
      status: { $in: ["completed", "failed"] }
    }).sort({ completedAt: -1 });

    console.log(`Found ${completedApplications.length} completed/failed applications to process`);

    let created = 0;
    let errors = 0;

    for (const app of completedApplications) {
      try {
        // Check if result already exists
        const existingResult = await ApplicationResult.findOne({ queueItemId: app._id });
        if (existingResult) {
          console.log(`- Application ${app._id}: Already has result, skipping`);
          continue;
        }

        // Create result based on application status
        let result = null;
        let error = null;

        if (app.status === "completed") {
          // Mock a successful result based on automation result if available
          result = {
            success: true,
            platform: "funda",
            verified: true,
            message: "Application submitted successfully",
            processingTime: app.automationResult?.processingTime || 5000,
            confirmationNumber: app.automationResult?.confirmationNumber,
            ...app.automationResult
          };
        } else if (app.status === "failed") {
          // Create error object from application data
          error = new Error(app.statusReason || app.errors?.[app.errors.length - 1] || "Application failed");
          error.code = "APPLICATION_FAILED";
        }

        // Use the service method to create the ApplicationResult
        const applicationResult = await autoApplicationService._createApplicationResult(app, result, error);
        
        if (applicationResult) {
          console.log(`✅ Created ApplicationResult for ${app._id} (${app.status})`);
          created++;
        } else {
          console.log(`❌ Failed to create ApplicationResult for ${app._id}`);
          errors++;
        }

      } catch (err) {
        console.log(`❌ Error processing application ${app._id}: ${err.message}`);
        errors++;
      }
    }

    // Final summary
    console.log(`\n=== SUMMARY ===`);
    console.log(`✅ Successfully created: ${created} ApplicationResults`);
    console.log(`❌ Errors: ${errors}`);
    console.log(`📊 Total processed: ${completedApplications.length}`);

    // Verify final count
    const finalCount = await ApplicationResult.countDocuments();
    console.log(`📈 Total ApplicationResults in database: ${finalCount}`);

    await mongoose.connection.close();
    console.log("\nDatabase connection closed");

  } catch (error) {
    console.error("Error in backfill script:", error);
    process.exit(1);
  }
}

backfillApplicationResults();
