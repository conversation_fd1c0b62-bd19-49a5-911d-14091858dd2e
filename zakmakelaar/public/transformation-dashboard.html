<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Transformation Pipeline Monitoring Dashboard</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background-color: #f5f5f5;
        color: #333;
      }

      .header {
        background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
        color: white;
        padding: 2rem;
        text-align: center;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
      }

      .dashboard-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .card {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .card h3 {
        margin-bottom: 1rem;
        color: #2c3e50;
      }

      .metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
      }

      .metric:last-child {
        border-bottom: none;
      }

      .metric-value {
        font-weight: bold;
        font-size: 1.1em;
      }

      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .status-good {
        background-color: #27ae60;
      }
      .status-warning {
        background-color: #f39c12;
      }
      .status-critical {
        background-color: #e74c3c;
      }
      .status-unknown {
        background-color: #95a5a6;
      }

      .alert {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 4px;
        border-left: 4px solid;
      }

      .alert-warning {
        background-color: #fff3cd;
        border-color: #f39c12;
        color: #856404;
      }

      .alert-critical {
        background-color: #f8d7da;
        border-color: #e74c3c;
        color: #721c24;
      }

      .refresh-btn {
        background: #3498db;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 1rem;
        margin-bottom: 1rem;
      }

      .refresh-btn:hover {
        background: #2980b9;
      }

      .loading {
        text-align: center;
        padding: 2rem;
        color: #666;
      }

      .timestamp {
        font-size: 0.9em;
        color: #666;
        text-align: center;
        margin-top: 1rem;
      }

      .chart-container {
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 4px;
        margin-top: 1rem;
      }
      
      .recommendation {
        padding: 0.75rem;
        margin: 0.5rem 0;
        border-radius: 4px;
        background-color: #e8f4fd;
        border-left: 4px solid #3498db;
      }
      
      .recommendation-high {
        border-color: #e74c3c;
        background-color: #fdf2f2;
      }
      
      .recommendation-medium {
        border-color: #f39c12;
        background-color: #fef9e7;
      }
      
      .recommendation-low {
        border-color: #3498db;
        background-color: #e8f4fd;
      }
      
      .recommendation-title {
        font-weight: bold;
        margin-bottom: 0.25rem;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🔄 Transformation Pipeline Monitoring Dashboard</h1>
      <p>
        Real-time monitoring of schema transformation performance and data quality
      </p>
    </div>

    <div class="container">
      <button class="refresh-btn" id="refresh-btn">🔄 Refresh Data</button>

      <div id="loading" class="loading">Loading dashboard data...</div>

      <div id="dashboard" style="display: none">
        <!-- Health Status -->
        <div class="card">
          <h3>🏥 System Health</h3>
          <div id="health-status"></div>
        </div>

        <!-- Overview Metrics -->
        <div class="dashboard-grid">
          <div class="card">
            <h3>📊 Overview</h3>
            <div id="overview-metrics"></div>
          </div>

          <div class="card">
            <h3>⚡ Performance</h3>
            <div id="performance-metrics"></div>
          </div>

          <div class="card">
            <h3>📋 Data Quality</h3>
            <div id="data-quality-metrics"></div>
          </div>
        </div>

        <!-- Alerts -->
        <div class="card">
          <h3>🚨 Alerts</h3>
          <div id="alerts-container"></div>
        </div>

        <!-- Recommendations -->
        <div class="card">
          <h3>💡 Recommendations</h3>
          <div id="recommendations-container"></div>
        </div>

        <!-- Charts -->
        <div class="dashboard-grid">
          <div class="card">
            <h3>📈 Success vs Failure</h3>
            <div id="success-chart" class="chart-container">
              Chart visualization would go here
            </div>
          </div>

          <div class="card">
            <h3>🐛 Error Distribution</h3>
            <div id="error-chart" class="chart-container">
              Chart visualization would go here
            </div>
          </div>
        </div>
      </div>

      <div class="timestamp" id="last-updated"></div>
    </div>

    <script>
      // Dashboard functionality
      document.addEventListener('DOMContentLoaded', function() {
        const refreshBtn = document.getElementById('refresh-btn');
        const loadingDiv = document.getElementById('loading');
        const dashboardDiv = document.getElementById('dashboard');
        const lastUpdatedDiv = document.getElementById('last-updated');
        
        // Initial data load
        loadDashboardData();
        
        // Refresh button click handler
        refreshBtn.addEventListener('click', loadDashboardData);
        
        // Load dashboard data from API
        async function loadDashboardData() {
          try {
            loadingDiv.style.display = 'block';
            dashboardDiv.style.display = 'none';
            
            // Fetch metrics, health, alerts, and report data
            const [metricsResponse, healthResponse, alertsResponse, reportResponse] = await Promise.all([
              fetch('/api/monitoring/transformation/metrics'),
              fetch('/api/monitoring/transformation/health'),
              fetch('/api/monitoring/transformation/alerts'),
              fetch('/api/monitoring/transformation/report')
            ]);
            
            const metricsData = await metricsResponse.json();
            const healthData = await healthResponse.json();
            const alertsData = await alertsResponse.json();
            const reportData = await reportResponse.json();
            
            // Update dashboard sections
            updateHealthStatus(healthData.data.health);
            updateOverviewMetrics(metricsData.data.metrics);
            updatePerformanceMetrics(metricsData.data.metrics, reportData.data.performance);
            updateDataQualityMetrics(reportData.data.dataQuality);
            updateAlerts(alertsData.data.alerts);
            updateRecommendations(reportData.data.recommendations);
            
            // Update timestamp
            lastUpdatedDiv.textContent = `Last updated: ${new Date().toLocaleString()}`;
            
            // Show dashboard
            loadingDiv.style.display = 'none';
            dashboardDiv.style.display = 'block';
          } catch (error) {
            console.error('Error loading dashboard data:', error);
            loadingDiv.textContent = 'Error loading dashboard data. Please try again.';
          }
        }
        
        // Update health status section
        function updateHealthStatus(health) {
          const healthStatusDiv = document.getElementById('health-status');
          
          let statusClass = 'status-unknown';
          if (health.status === 'healthy') {
            statusClass = 'status-good';
          } else if (health.status === 'degraded') {
            statusClass = 'status-warning';
          } else if (health.status === 'unhealthy') {
            statusClass = 'status-critical';
          }
          
          healthStatusDiv.innerHTML = `
            <div class="metric">
              <span>Status</span>
              <span class="metric-value">
                <span class="status-indicator ${statusClass}"></span>
                ${health.status.toUpperCase()}
              </span>
            </div>
            <div class="metric">
              <span>Success Rate</span>
              <span class="metric-value">${health.metrics.successRate}</span>
            </div>
            <div class="metric">
              <span>Avg. Transformation Time</span>
              <span class="metric-value">${health.metrics.averageTransformationTime}</span>
            </div>
            <div class="metric">
              <span>Data Completeness</span>
              <span class="metric-value">${health.metrics.dataQualityCompleteness}</span>
            </div>
            <div class="metric">
              <span>Data Accuracy</span>
              <span class="metric-value">${health.metrics.dataQualityAccuracy}</span>
            </div>
            <div class="metric">
              <span>Last Transformation</span>
              <span class="metric-value">${formatDateTime(health.metrics.lastTransformationTime)}</span>
            </div>
          `;
        }
        
        // Update overview metrics section
        function updateOverviewMetrics(metrics) {
          const overviewMetricsDiv = document.getElementById('overview-metrics');
          
          overviewMetricsDiv.innerHTML = `
            <div class="metric">
              <span>Total Transformations</span>
              <span class="metric-value">${metrics.totalTransformations}</span>
            </div>
            <div class="metric">
              <span>Successful</span>
              <span class="metric-value">${metrics.successfulTransformations}</span>
            </div>
            <div class="metric">
              <span>Failed</span>
              <span class="metric-value">${metrics.failedTransformations}</span>
            </div>
            <div class="metric">
              <span>Success Rate</span>
              <span class="metric-value">${metrics.successRate}</span>
            </div>
            <div class="metric">
              <span>Properties Transformed</span>
              <span class="metric-value">${metrics.totalPropertiesTransformed}</span>
            </div>
          `;
        }
        
        // Update performance metrics section
        function updatePerformanceMetrics(metrics, performance) {
          const performanceMetricsDiv = document.getElementById('performance-metrics');
          
          performanceMetricsDiv.innerHTML = `
            <div class="metric">
              <span>Avg. Transformation Time</span>
              <span class="metric-value">${metrics.averageTransformationTime}</span>
            </div>
            <div class="metric">
              <span>Memory Usage (Avg)</span>
              <span class="metric-value">${metrics.memoryUsageAverage}</span>
            </div>
            <div class="metric">
              <span>Memory Usage (Peak)</span>
              <span class="metric-value">${metrics.memoryUsagePeak}</span>
            </div>
          `;
        }
        
        // Update data quality metrics section
        function updateDataQualityMetrics(dataQuality) {
          const dataQualityMetricsDiv = document.getElementById('data-quality-metrics');
          
          dataQualityMetricsDiv.innerHTML = `
            <div class="metric">
              <span>Completeness</span>
              <span class="metric-value">${dataQuality.completeness}%</span>
            </div>
            <div class="metric">
              <span>Accuracy</span>
              <span class="metric-value">${dataQuality.accuracy}%</span>
            </div>
            <div class="metric">
              <span>Sample Size</span>
              <span class="metric-value">${dataQuality.sampleSize}</span>
            </div>
          `;
        }
        
        // Update alerts section
        function updateAlerts(alerts) {
          const alertsContainerDiv = document.getElementById('alerts-container');
          
          if (alerts.length === 0) {
            alertsContainerDiv.innerHTML = '<p>No active alerts</p>';
            return;
          }
          
          let alertsHtml = '';
          
          alerts.forEach(alert => {
            const alertClass = alert.level === 'critical' ? 'alert-critical' : 'alert-warning';
            
            alertsHtml += `
              <div class="alert ${alertClass}">
                <strong>${alert.type.replace(/_/g, ' ').toUpperCase()}</strong>
                <p>${alert.message}</p>
                <small>${formatDateTime(alert.timestamp)}</small>
              </div>
            `;
          });
          
          alertsContainerDiv.innerHTML = alertsHtml;
        }
        
        // Update recommendations section
        function updateRecommendations(recommendations) {
          const recommendationsContainerDiv = document.getElementById('recommendations-container');
          
          if (recommendations.length === 0) {
            recommendationsContainerDiv.innerHTML = '<p>No recommendations at this time</p>';
            return;
          }
          
          let recommendationsHtml = '';
          
          recommendations.forEach(recommendation => {
            const recommendationClass = `recommendation-${recommendation.priority}`;
            
            recommendationsHtml += `
              <div class="recommendation ${recommendationClass}">
                <div class="recommendation-title">${recommendation.category.replace(/_/g, ' ').toUpperCase()}</div>
                <p>${recommendation.message}</p>
              </div>
            `;
          });
          
          recommendationsContainerDiv.innerHTML = recommendationsHtml;
        }
        
        // Format date/time
        function formatDateTime(dateTimeStr) {
          if (!dateTimeStr) return 'N/A';
          
          try {
            const date = new Date(dateTimeStr);
            return date.toLocaleString();
          } catch (e) {
            return dateTimeStr;
          }
        }
      });
    </script>
  </body>
</html>