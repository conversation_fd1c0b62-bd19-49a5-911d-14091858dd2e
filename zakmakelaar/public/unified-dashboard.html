<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="default-src 'self' data:; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; connect-src 'self';">
  <title>🏠 Zakmakelaar - Unified Monitoring Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    :root {
      --primary-color: #2563eb;
      --secondary-color: #64748b;
      --success-color: #10b981;
      --warning-color: #f59e0b;
      --danger-color: #ef4444;
      --info-color: #06b6d4;
      --dark-color: #1e293b;
      --light-bg: #f8fafc;
      --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    body {
      background-color: var(--light-bg);
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .navbar {
      background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
      box-shadow: var(--card-shadow);
    }

    .navbar-brand {
      font-weight: 700;
      font-size: 1.5rem;
    }

    .dashboard-header {
      background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
      color: white;
      padding: 2rem 0;
      margin-bottom: 2rem;
    }

    .card {
      border: none;
      border-radius: 12px;
      box-shadow: var(--card-shadow);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      margin-bottom: 1.5rem;
    }

    .card:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .card-header {
      background-color: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      font-weight: 600;
      padding: 1rem 1.5rem;
    }

    .metric-card {
      text-align: center;
      padding: 1.5rem;
    }

    .metric-value {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .metric-label {
      color: var(--secondary-color);
      font-size: 0.875rem;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .metric-change {
      font-size: 0.875rem;
      font-weight: 500;
      margin-top: 0.5rem;
    }

    .metric-change.positive {
      color: var(--success-color);
    }

    .metric-change.negative {
      color: var(--danger-color);
    }

    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 8px;
    }

    .status-good { background-color: var(--success-color); }
    .status-warning { background-color: var(--warning-color); }
    .status-critical { background-color: var(--danger-color); }

    .chart-container {
      position: relative;
      height: 300px;
      padding: 1rem;
    }

    .site-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
      margin: 0.25rem;
    }

    .site-funda { background-color: #ff6b35; color: white; }
    .site-pararius { background-color: #0066cc; color: white; }
    .site-huurwoningen { background-color: #28a745; color: white; }

    .alert-item {
      border-left: 4px solid;
      padding: 1rem;
      margin-bottom: 0.5rem;
      border-radius: 0 8px 8px 0;
    }

    .alert-critical { border-left-color: var(--danger-color); background-color: #fef2f2; }
    .alert-warning { border-left-color: var(--warning-color); background-color: #fffbeb; }
    .alert-info { border-left-color: var(--info-color); background-color: #f0f9ff; }

    .refresh-btn {
      position: fixed;
      bottom: 2rem;
      right: 2rem;
      z-index: 1000;
      border-radius: 50%;
      width: 60px;
      height: 60px;
      box-shadow: var(--card-shadow);
    }

    .nav-tabs .nav-link {
      border: none;
      color: var(--secondary-color);
      font-weight: 500;
    }

    .nav-tabs .nav-link.active {
      background-color: var(--primary-color);
      color: white;
      border-radius: 8px;
    }

    .loading-spinner {
      display: none;
      text-align: center;
      padding: 2rem;
    }

    @media (max-width: 768px) {
      .dashboard-header {
        padding: 1rem 0;
      }
      
      .metric-value {
        font-size: 2rem;
      }
      
      .chart-container {
        height: 250px;
      }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
      <a class="navbar-brand" href="#">
        <i class="fas fa-home me-2"></i>Zakmakelaar Dashboard
      </a>
      <div class="navbar-nav ms-auto">
        <span class="navbar-text me-3">
          <i class="fas fa-clock me-1"></i>
          <span id="lastUpdate">Loading...</span>
        </span>
        <button class="btn btn-outline-light btn-sm" id="navRefreshBtn">
          <i class="fas fa-sync-alt me-1"></i>Refresh
        </button>
      </div>
    </div>
  </nav>

  <!-- Dashboard Header -->
  <div class="dashboard-header">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col-md-8">
          <h1 class="mb-2">
            <i class="fas fa-chart-line me-3"></i>
            Property Scraper Monitoring Dashboard
          </h1>
          <p class="mb-0 opacity-75">Real-time monitoring of scraping performance, transformations, and system health</p>
        </div>
        <div class="col-md-4 text-end">
          <div class="d-flex justify-content-end align-items-center">
            <div class="me-4">
              <div class="text-white-50 small">System Status</div>
              <div class="d-flex align-items-center">
                <span class="status-indicator" id="systemStatus"></span>
                <span id="systemStatusText">Checking...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="container-fluid">
    <!-- Key Metrics Overview -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="metric-value text-primary" id="totalListings">-</div>
          <div class="metric-label">Total Listings</div>
          <div class="metric-change" id="listingsChange">-</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="metric-value text-success" id="successRate">-</div>
          <div class="metric-label">Success Rate</div>
          <div class="metric-change" id="successRateChange">-</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="metric-value text-info" id="avgScrapingTime">-</div>
          <div class="metric-label">Avg Scraping Time</div>
          <div class="metric-change" id="scrapingTimeChange">-</div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="metric-value text-warning" id="transformationRate">-</div>
          <div class="metric-label">Transformation Rate</div>
          <div class="metric-change" id="transformationRateChange">-</div>
        </div>
      </div>
    </div>

    <!-- Main Dashboard Tabs -->
    <ul class="nav nav-tabs mb-4" id="dashboardTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
          <i class="fas fa-tachometer-alt me-2"></i>Overview
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="scraping-tab" data-bs-toggle="tab" data-bs-target="#scraping" type="button" role="tab">
          <i class="fas fa-spider me-2"></i>Scraping
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">
          <i class="fas fa-chart-bar me-2"></i>Performance
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="transformation-tab" data-bs-toggle="tab" data-bs-target="#transformation" type="button" role="tab">
          <i class="fas fa-cogs me-2"></i>Transformation
        </button>
      </li>
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="dashboardTabContent">
      <!-- Overview Tab -->
      <div class="tab-pane fade show active" id="overview" role="tabpanel">
        <div class="row">
          <!-- System Health -->
          <div class="col-lg-6 mb-4">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-heartbeat me-2"></i>System Health
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-6">
                    <div class="d-flex align-items-center mb-3">
                      <span class="status-indicator" id="scrapingHealth"></span>
                      <span>Scraping Service</span>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                      <span class="status-indicator" id="transformationHealth"></span>
                      <span>Transformation Pipeline</span>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="d-flex align-items-center mb-3">
                      <span class="status-indicator" id="databaseHealth"></span>
                      <span>Database</span>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                      <span class="status-indicator" id="apiHealth"></span>
                      <span>API Services</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="col-lg-6 mb-4">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-clock me-2"></i>Recent Activity
              </div>
              <div class="card-body">
                <div id="recentActivity">
                  <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin me-2"></i>Loading recent activity...
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Alerts -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-exclamation-triangle me-2"></i>Active Alerts
              </div>
              <div class="card-body">
                <div id="alertsContainer">
                  <div class="text-center text-muted">
                    <i class="fas fa-check-circle me-2 text-success"></i>No active alerts
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Scraping Tab -->
      <div class="tab-pane fade" id="scraping" role="tabpanel">
        <div class="row">
          <!-- Site Performance -->
          <div class="col-lg-8 mb-4">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-chart-bar me-2"></i>Listings by Site
              </div>
              <div class="card-body">
                <div class="chart-container">
                  <canvas id="siteListingsChart"></canvas>
                </div>
              </div>
            </div>
          </div>

          <!-- Site Metrics -->
          <div class="col-lg-4 mb-4">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-list me-2"></i>Site Metrics
              </div>
              <div class="card-body">
                <div id="siteMetrics">
                  <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin me-2"></i>Loading site metrics...
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Success vs Failure -->
        <div class="row">
          <div class="col-lg-6 mb-4">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-chart-pie me-2"></i>Success vs Failure
              </div>
              <div class="card-body">
                <div class="chart-container">
                  <canvas id="successFailureChart"></canvas>
                </div>
              </div>
            </div>
          </div>

          <!-- Error Distribution -->
          <div class="col-lg-6 mb-4">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-bug me-2"></i>Error Distribution
              </div>
              <div class="card-body">
                <div class="chart-container">
                  <canvas id="errorDistributionChart"></canvas>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Tab -->
      <div class="tab-pane fade" id="performance" role="tabpanel">
        <div class="row">
          <!-- Performance Metrics -->
          <div class="col-lg-8 mb-4">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-tachometer-alt me-2"></i>Performance Over Time
              </div>
              <div class="card-body">
                <div class="chart-container">
                  <canvas id="performanceChart"></canvas>
                </div>
              </div>
            </div>
          </div>

          <!-- Performance Stats -->
          <div class="col-lg-4 mb-4">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-stopwatch me-2"></i>Performance Stats
              </div>
              <div class="card-body">
                <div id="performanceStats">
                  <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin me-2"></i>Loading performance stats...
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Transformation Tab -->
      <div class="tab-pane fade" id="transformation" role="tabpanel">
        <div class="row">
          <!-- Transformation Pipeline -->
          <div class="col-lg-8 mb-4">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-project-diagram me-2"></i>Transformation Pipeline
              </div>
              <div class="card-body">
                <div class="chart-container">
                  <canvas id="transformationChart"></canvas>
                </div>
              </div>
            </div>
          </div>

          <!-- Data Quality -->
          <div class="col-lg-4 mb-4">
            <div class="card">
              <div class="card-header">
                <i class="fas fa-check-double me-2"></i>Data Quality
              </div>
              <div class="card-body">
                <div id="dataQuality">
                  <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin me-2"></i>Loading data quality metrics...
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Floating Refresh Button -->
  <button class="btn btn-primary refresh-btn" id="floatingRefreshBtn" title="Refresh Dashboard">
    <i class="fas fa-sync-alt"></i>
  </button>

  <!-- Loading Spinner -->
  <div class="loading-spinner" id="loadingSpinner">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2">Updating dashboard...</p>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- Dashboard JavaScript -->
  <script src="js/unified-dashboard.js"></script>
</body>
</html>
