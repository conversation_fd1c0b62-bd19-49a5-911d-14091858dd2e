// Monitoring Dashboard JavaScript
async function loadDashboard() {
  console.log("Loading dashboard...");
  document.getElementById("loading").style.display = "block";
  document.getElementById("dashboard").style.display = "none";

  try {
    console.log("Fetching dashboard data...");
    // Load dashboard data
    const response = await fetch("/api/monitoring/dashboard");
    console.log("Dashboard response status:", response.status);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log("Dashboard data:", data);

    if (data.status === "success") {
      renderDashboard(data.data);
      document.getElementById("loading").style.display = "none";
      document.getElementById("dashboard").style.display = "block";
      document.getElementById(
        "last-updated"
      ).textContent = `Last updated: ${new Date().toLocaleString()}`;
    }

    console.log("Fetching alerts...");
    // Load alerts
    const alertsResponse = await fetch("/api/monitoring/alerts");
    console.log("Alerts response status:", alertsResponse.status);

    if (alertsResponse.ok) {
      const alertsData = await alertsResponse.json();
      console.log("Alerts data:", alertsData);

      if (alertsData.status === "success") {
        renderAlerts(alertsData.data);
      }
    }
  } catch (error) {
    console.error("Error loading dashboard:", error);
    document.getElementById(
      "loading"
    ).innerHTML = `<div class="alert alert-critical">Failed to load dashboard data: ${error.message}</div>`;
  }
}

function renderDashboard(data) {
  console.log("Rendering dashboard with data:", data);

  try {
    // Health Status
    const healthHtml = `
      <div class="metric">
        <span>Overall Status</span>
        <span class="metric-value">
          <span class="status-indicator status-${
            data.indicators?.overall || "unknown"
          }"></span>
          ${(data.indicators?.overall || "unknown").toUpperCase()}
        </span>
      </div>
      <div class="metric">
        <span>Uptime</span>
        <span class="metric-value">${formatUptime(
          data.overview?.uptime || 0
        )}</span>
      </div>
    `;
    document.getElementById("health-status").innerHTML = healthHtml;

    // Overview Metrics
    const overviewHtml = `
      <div class="metric">
        <span>Total Scrapes</span>
        <span class="metric-value">${data.overview?.totalScrapes || 0}</span>
      </div>
      <div class="metric">
        <span>Success Rate</span>
        <span class="metric-value">${data.overview?.successRate || "0%"}</span>
      </div>
      <div class="metric">
        <span>Failed Scrapes</span>
        <span class="metric-value">${data.overview?.failedScrapes || 0}</span>
      </div>
    `;
    document.getElementById("overview-metrics").innerHTML = overviewHtml;

    // Performance Metrics
    const performanceHtml = `
      <div class="metric">
        <span>Avg Scraping Time</span>
        <span class="metric-value">${
          data.performance?.averageScrapingTime || "0s"
        }</span>
      </div>
      <div class="metric">
        <span>Listings per Scrape</span>
        <span class="metric-value">${
          data.performance?.avgListingsPerScrape || "0"
        }</span>
      </div>
      <div class="metric">
        <span>Total Listings Found</span>
        <span class="metric-value">${
          data.performance?.totalListingsFound || 0
        }</span>
      </div>
      <div class="metric">
        <span>Duplicate Rate</span>
        <span class="metric-value">${
          data.performance?.duplicateRate || "0%"
        }</span>
      </div>
    `;
    document.getElementById("performance-metrics").innerHTML = performanceHtml;

    // Recent Activity
    const activityHtml = `
      <div class="metric">
        <span>Last Scrape</span>
        <span class="metric-value">
          ${
            data.recentActivity?.lastScrapeTime
              ? new Date(data.recentActivity.lastScrapeTime).toLocaleString()
              : "Never"
          }
        </span>
      </div>
      <div class="metric">
        <span>Time Since Last Scrape</span>
        <span class="metric-value">
          ${
            data.recentActivity?.timeSinceLastScrape
              ? data.recentActivity.timeSinceLastScrape + " minutes ago"
              : "N/A"
          }
        </span>
      </div>
    `;
    document.getElementById("recent-activity").innerHTML = activityHtml;

    // Site-specific Metrics
    let siteMetricsHtml = '';
    if (data.siteMetrics) {
      const sites = ['funda', 'pararius', 'huurwoningen'];
      sites.forEach(site => {
        const siteData = data.siteMetrics[site] || {};
        const siteName = site.charAt(0).toUpperCase() + site.slice(1);
        const successRate = siteData.successfulScrapes + siteData.failedScrapes > 0 
          ? ((siteData.successfulScrapes / (siteData.successfulScrapes + siteData.failedScrapes)) * 100).toFixed(1)
          : '0';
        
        siteMetricsHtml += `
          <div style="margin-bottom: 1rem; padding: 1rem; border: 1px solid #eee; border-radius: 4px;">
            <h4 style="margin: 0 0 0.5rem 0; color: #2c3e50;">${siteName}</h4>
            <div class="metric">
              <span>Listings Found</span>
              <span class="metric-value">${siteData.listingsFound || 0}</span>
            </div>
            <div class="metric">
              <span>Listings Saved</span>
              <span class="metric-value">${siteData.listingsSaved || 0}</span>
            </div>
            <div class="metric">
              <span>Success Rate</span>
              <span class="metric-value">${successRate}%</span>
            </div>
            <div class="metric">
              <span>Last Scrape</span>
              <span class="metric-value">
                ${siteData.lastScrapeTime 
                  ? new Date(siteData.lastScrapeTime).toLocaleString()
                  : 'Never'
                }
              </span>
            </div>
          </div>
        `;
      });
    }
    
    if (!siteMetricsHtml) {
      siteMetricsHtml = '<div class="metric"><span>No site-specific data available</span></div>';
    }
    
    document.getElementById("site-metrics").innerHTML = siteMetricsHtml;

    // Render site listings chart if data is available
    if (data.charts?.siteListings && data.charts.siteListings.length > 0) {
      renderSiteListingsChart(data.charts.siteListings);
    }
  } catch (error) {
    console.error("Error rendering dashboard:", error);
    document.getElementById(
      "loading"
    ).innerHTML = `<div class="alert alert-critical">Error rendering dashboard: ${error.message}</div>`;
  }
}

function renderAlerts(alertsData) {
  const alertsContainer = document.getElementById("alerts-container");

  if (alertsData.alerts.length === 0) {
    alertsContainer.innerHTML =
      '<p style="color: #27ae60;">✅ No active alerts</p>';
    return;
  }

  const alertsHtml = alertsData.alerts
    .map(
      (alert) => `
    <div class="alert alert-${alert.level}">
      <strong>${alert.level.toUpperCase()}:</strong> ${alert.message}
      <br><small>Type: ${alert.type} | ${new Date(
        alert.timestamp
      ).toLocaleString()}</small>
    </div>
  `
    )
    .join("");

  alertsContainer.innerHTML = alertsHtml;
}

function renderSiteListingsChart(siteData) {
  const chartContainer = document.getElementById("site-listings-chart");
  
  if (!siteData || siteData.length === 0) {
    chartContainer.innerHTML = '<div style="text-align: center; color: #666;">No data available</div>';
    return;
  }

  // Find the maximum value for scaling
  const maxValue = Math.max(...siteData.map(item => item.value));
  
  let chartHtml = '<div style="display: flex; flex-direction: column; gap: 10px;">';
  
  siteData.forEach(item => {
    const percentage = maxValue > 0 ? (item.value / maxValue) * 100 : 0;
    const barColor = getBarColor(item.label);
    
    chartHtml += `
      <div style="display: flex; align-items: center; gap: 10px;">
        <div style="min-width: 80px; font-weight: 500;">${item.label}:</div>
        <div style="flex: 1; background: #f0f0f0; border-radius: 4px; overflow: hidden;">
          <div style="
            width: ${percentage}%;
            height: 20px;
            background: ${barColor};
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 8px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            min-width: 30px;
          ">
            ${item.value}
          </div>
        </div>
      </div>
    `;
  });
  
  chartHtml += '</div>';
  chartContainer.innerHTML = chartHtml;
}

function getBarColor(siteName) {
  const colors = {
    'Funda': '#ff6b35',
    'Pararius': '#4a90e2', 
    'Huurwoningen': '#7ed321'
  };
  return colors[siteName] || '#95a5a6';
}

function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) return `${days}d ${hours}h ${minutes}m`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
}

// Initialize dashboard when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  console.log("DOM loaded, initializing dashboard...");

  // Add event listener for refresh button
  const refreshBtn = document.getElementById("refresh-btn");
  if (refreshBtn) {
    refreshBtn.addEventListener("click", function (e) {
      e.preventDefault();
      console.log("Refresh button clicked");
      loadDashboard();
    });
  }

  // Load dashboard initially
  loadDashboard();

  // Auto-refresh every 30 seconds
  setInterval(loadDashboard, 30000);
});
