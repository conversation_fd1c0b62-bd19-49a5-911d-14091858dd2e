# ZakMakelaar Backend

ZakMakelaar is an automated real estate listing aggregator that scrapes Dutch property websites and provides a consolidated API for accessing rental listings.

## 📋 Project Overview

This backend service scrapes real estate listings from popular Dutch property websites like Funda.nl and Pararius.nl, stores them in a MongoDB database, and provides REST API endpoints for accessing and filtering these listings. The system also includes user authentication, listing management, and automated notifications for new listings.

### Core Features

- **Robust Scraping System**: Bypasses anti-bot protections to reliably extract property listings
- **Automated Scheduling**: Runs scrapers at regular intervals (every 5 minutes by default)
- **Listing Storage**: Stores property details in MongoDB with duplicate detection
- **REST API**: Provides endpoints for accessing listings and triggering scrapes
- **User Authentication**: JWT-based authentication for securing API access
- **Swagger Documentation**: Interactive API documentation

## 🚀 Getting Started

### Prerequisites

- Node.js (v14 or later)
- MongoDB (v4 or later)
- npm or yarn

### Installation

1. Clone the repository

   ```bash
   git clone https://github.com/majidELMEHAMMEDY/zakmakelaar.git
   cd zakmakelaar-backend
   ```

2. Install dependencies

   ```bash
   npm install
   ```

3. Configure environment variables

   - Create a `src/config/config.js` file based on the example below:

   ```javascript
   module.exports = {
     mongoURI: "mongodb://localhost:27017/zakmakelaar",
     jwtSecret: "your-jwt-secret-key",
     // Other configuration settings
   };
   ```

4. Start the server
   ```bash
   npm start
   ```

The server will start on port 3000 by default. You can access the Swagger documentation at `http://localhost:3000/api-docs`.

## 🏗️ Project Structure

```
zakmakelaar-backend/
├── src/
│   ├── config/           # Configuration files
│   ├── controllers/      # Request handlers
│   │   ├── listingController.js
│   │   ├── scraperController.js
│   │   └── userController.js
│   ├── middleware/       # Express middleware
│   │   └── auth.js       # Authentication middleware
│   ├── models/           # Mongoose models
│   │   ├── Application.js
│   │   ├── Listing.js
│   │   └── User.js
│   ├── routes/           # API route definitions
│   │   ├── auth.js
│   │   ├── listing.js
│   │   └── scraper.js
│   ├── services/         # Business logic
│   │   ├── alertService.js
│   │   └── scraper.js    # Property scraping logic
│   └── index.js          # Application entry point
├── package.json
└── README.md
```

## 🔌 API Endpoints

### Scraping

- `POST /api/scrape` - Manually trigger the scraping process
  - Response: Array of newly scraped listings

### Listings

- `GET /api/listings` - Get all listings

  - Query parameters:
    - `limit`: Maximum number of results (default: 20)
    - `page`: Page number for pagination (default: 1)
    - `sortBy`: Field to sort by (default: 'dateAdded')
    - `sortOrder`: Sort order ('asc' or 'desc', default: 'desc')
  - Response: Array of listings

- `GET /api/listings/:id` - Get a specific listing by ID
  - Response: Single listing object

### Authentication

- `POST /api/auth/register` - Register a new user

  - Request body: `{ "email": "<EMAIL>", "password": "password123" }`
  - Response: User object with JWT token

- `POST /api/auth/login` - Login an existing user
  - Request body: `{ "email": "<EMAIL>", "password": "password123" }`
  - Response: User object with JWT token

## 🔍 Scraping Implementation Details

### Funda Scraper

The Funda scraper uses advanced techniques to bypass anti-bot protections:

1. **Browser Emulation**: Uses Puppeteer in non-headless mode with realistic browser settings
2. **Human-like Behavior**: Implements random delays, scrolling, and realistic user agent headers
3. **Multiple Data Extraction Methods**:
   - Primary: Extracts structured data from JSON-LD metadata
   - Fallback: Parses HTML DOM for listing data
4. **Data Parsing**: Extracts property details including:
   - Title/address
   - Price (handles "Prijs op aanvraag" / price on request)
   - Location (city, postal code)
   - Property type (apartment, house, etc.)
   - Additional details (size in m², number of bedrooms)

### Pararius Scraper

The Pararius scraper uses a simpler approach:

1. **Direct HTML Parsing**: Uses Cheerio to parse listing elements
2. **CSS Selectors**: Targets specific listing containers and data elements
3. **Data Extraction**: Extracts title, price, location, and property type
4. **Error Handling**: Includes retry logic and graceful failure handling

### Huurwoningen.nl Scraper

The Huurwoningen scraper is designed for comprehensive data extraction:

1. **Multi-City Coverage**: Scrapes listings from major Dutch cities (Amsterdam, Rotterdam, Den Haag, Utrecht, Eindhoven, Groningen)
2. **Advanced Data Extraction**: Extracts detailed property information including:
   - Title and location (with postal code patterns)
   - Price per month
   - Property size in m²
   - Number of rooms/bedrooms
   - Build year
   - Interior type (Kaal, Gestoffeerd, Gemeubileerd)
3. **Smart Pattern Matching**: Uses regex patterns to identify and extract structured data
4. **Anti-Detection**: Implements cookie management and human-like browsing behavior
5. **Robust Error Handling**: Continues scraping other cities even if one fails

## 🗃️ Database Schema

### Listing Model

```javascript
{
  title: String,         // Property title/address
  price: String,         // Price (as string to handle various formats)
  location: String,      // Location (city, neighborhood)
  url: String,           // URL to the original listing
  size: String,          // Size in m² (if available)
  bedrooms: String,      // Number of bedrooms (if available)
  rooms: String,         // Total number of rooms (if available)
  propertyType: String,  // Type of property (apartment, house, etc.)
  description: String,   // Full description (if available)
  year: String,          // Build year (if available)
  interior: String,      // Interior type: Kaal, Gestoffeerd, Gemeubileerd
  source: String,        // Source website: funda.nl, pararius.nl, huurwoningen.nl
  dateAdded: Date,       // Date when the listing was added
  timestamp: Date        // Last updated timestamp
}
```

## 💻 Development

### Running Tests

```bash
npm test
```

### Running the Scrapers Manually

You can test the scrapers individually or all together:

```bash
# Test all scrapers (Funda, Pararius, Huurwoningen)
npm run test:all-scrapers

# Test individual scrapers
npm run test:scraper          # Test Funda scraper
npm run test:huurwoningen     # Test Huurwoningen scraper

# Legacy test script (Funda only)
node src/test-scraper.js
```

### Scheduling Configuration

All three scrapers run every 5 minutes by default. You can modify this schedule in `src/index.js`:

```javascript
schedule.scheduleJob("*/5 * * * *", async () => {
  console.log("Running all scrapers...");
  const [fundaResult, pariusResult, huurwoningenResult] =
    await Promise.allSettled([
      scrapeFunda(),
      scrapePararius(),
      scrapeHuurwoningen(),
    ]);
});
```

## 📝 License

This project is licensed under the MIT License.

## 🙏 Acknowledgements

- [Puppeteer](https://pptr.dev/) - Headless browser automation
- [Cheerio](https://cheerio.js.org/) - Fast and flexible HTML parsing
- [Express](https://expressjs.com/) - Web framework for Node.js
- [Mongoose](https://mongoosejs.com/) - MongoDB object modeling
- [node-schedule](https://github.com/node-schedule/node-schedule) - Task scheduling
- [Swagger](https://swagger.io/) - API documentation
