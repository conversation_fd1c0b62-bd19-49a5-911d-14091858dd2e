const express = require('express');
const LearningOptimizationService = require('../services/learningOptimizationService');
const { auth } = require('../middleware/auth');
const { logger } = require('../services/logger');

const router = express.Router();
const learningService = new LearningOptimizationService();

/**
 * @swagger
 * /api/learning-optimization/success-rates:
 *   get:
 *     summary: Get user's application success rates and patterns
 *     tags: [Learning & Optimization]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d]
 *           default: 30d
 *         description: Timeframe for analysis
 *     responses:
 *       200:
 *         description: Success rates and patterns
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 userId:
 *                   type: string
 *                 timeframe:
 *                   type: string
 *                 totalApplications:
 *                   type: number
 *                 successfulApplications:
 *                   type: number
 *                 successRate:
 *                   type: number
 *                 responseRate:
 *                   type: number
 *                 patterns:
 *                   type: object
 *                 lastUpdated:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/success-rates', auth, async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    const userId = req.user.id;

    const successRates = await learningService.trackSuccessRates(userId, timeframe);

    res.json({
      success: true,
      data: successRates
    });
  } catch (error) {
    logger.error('Error getting success rates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get success rates',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/learning-optimization/ml-insights:
 *   get:
 *     summary: Get machine learning insights for application improvement
 *     tags: [Learning & Optimization]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: ML insights and recommendations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 insights:
 *                   type: object
 *                   properties:
 *                     optimalTiming:
 *                       type: object
 *                     contentOptimization:
 *                       type: object
 *                     marketTrends:
 *                       type: object
 *                     personalizedRecommendations:
 *                       type: array
 *                 confidence:
 *                   type: number
 *                 lastUpdated:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/ml-insights', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    const insights = await learningService.generateMLInsights(userId);

    res.json({
      success: true,
      data: insights
    });
  } catch (error) {
    logger.error('Error generating ML insights:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate ML insights',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/learning-optimization/recommendations:
 *   get:
 *     summary: Get optimization recommendations for user
 *     tags: [Learning & Optimization]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Optimization recommendations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 recommendations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                       current:
 *                         type: string
 *                       recommended:
 *                         type: string
 *                       reason:
 *                         type: string
 *                       confidence:
 *                         type: number
 *                 totalAnalyzed:
 *                   type: number
 *                 lastUpdated:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/recommendations', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    const recommendations = await learningService.recommendOptimizations(userId);

    res.json({
      success: true,
      data: recommendations
    });
  } catch (error) {
    logger.error('Error getting optimization recommendations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get optimization recommendations',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/learning-optimization/market-trends:
 *   get:
 *     summary: Get market trend analysis and adaptive strategies
 *     tags: [Learning & Optimization]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Market trends and adaptive strategies
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 trends:
 *                   type: object
 *                   properties:
 *                     competitionLevel:
 *                       type: object
 *                     priceMovement:
 *                       type: object
 *                     responseTime:
 *                       type: object
 *                     successRateChange:
 *                       type: object
 *                     seasonalPatterns:
 *                       type: object
 *                 adaptiveStrategies:
 *                   type: array
 *                 marketCondition:
 *                   type: string
 *                   enum: [favorable, neutral, challenging]
 *                 lastUpdated:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/market-trends', auth, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user's applications for trend analysis
    const ApplicationResult = require('../models/ApplicationResult');
    const applications = await ApplicationResult.find({ userId })
      .populate('listingId')
      .sort({ createdAt: -1 })
      .limit(100);

    const trends = await learningService.analyzeMarketTrends(applications);

    res.json({
      success: true,
      data: trends
    });
  } catch (error) {
    logger.error('Error analyzing market trends:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to analyze market trends',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/learning-optimization/patterns:
 *   post:
 *     summary: Analyze success patterns for specific applications
 *     tags: [Learning & Optimization]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               applicationIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of application IDs to analyze
 *     responses:
 *       200:
 *         description: Success patterns analysis
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 patterns:
 *                   type: object
 *                 analysisDate:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Invalid request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/patterns', auth, async (req, res) => {
  try {
    const { applicationIds } = req.body;
    const userId = req.user.id;

    if (!applicationIds || !Array.isArray(applicationIds)) {
      return res.status(400).json({
        success: false,
        message: 'applicationIds must be an array'
      });
    }

    // Get specific applications for analysis
    const ApplicationResult = require('../models/ApplicationResult');
    const applications = await ApplicationResult.find({
      _id: { $in: applicationIds },
      userId
    }).populate('listingId');

    if (applications.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No applications found for analysis'
      });
    }

    const patterns = await learningService.analyzeSuccessPatterns(applications);

    res.json({
      success: true,
      data: {
        patterns,
        totalAnalyzed: applications.length,
        analysisDate: new Date()
      }
    });
  } catch (error) {
    logger.error('Error analyzing patterns:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to analyze patterns',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/learning-optimization/cache:
 *   delete:
 *     summary: Clear learning optimization cache
 *     tags: [Learning & Optimization]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Cache cleared successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.delete('/cache', auth, async (req, res) => {
  try {
    learningService.clearCache();

    res.json({
      success: true,
      message: 'Learning optimization cache cleared successfully'
    });
  } catch (error) {
    logger.error('Error clearing cache:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear cache',
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/learning-optimization/performance-metrics:
 *   get:
 *     summary: Get detailed performance metrics for user applications
 *     tags: [Learning & Optimization]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d]
 *           default: 30d
 *         description: Timeframe for metrics
 *       - in: query
 *         name: groupBy
 *         schema:
 *           type: string
 *           enum: [day, week, month]
 *           default: day
 *         description: How to group the metrics
 *     responses:
 *       200:
 *         description: Detailed performance metrics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 metrics:
 *                   type: object
 *                 timeframe:
 *                   type: string
 *                 groupBy:
 *                   type: string
 *                 lastUpdated:
 *                   type: string
 *                   format: date-time
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/performance-metrics', auth, async (req, res) => {
  try {
    const { timeframe = '30d', groupBy = 'day' } = req.query;
    const userId = req.user.id;

    // Get success rates first
    const successRates = await learningService.trackSuccessRates(userId, timeframe);
    
    // Add additional performance metrics
    const ApplicationResult = require('../models/ApplicationResult');
    const startDate = learningService.getStartDate(timeframe);
    
    const applications = await ApplicationResult.find({
      userId,
      createdAt: { $gte: startDate }
    }).populate('listingId');

    // Calculate additional metrics
    const avgProcessingTime = applications.reduce((sum, app) => {
      if (app.processingTime) {
        return sum + app.processingTime;
      }
      return sum;
    }, 0) / applications.length || 0;

    const responseTimeMetrics = applications
      .filter(app => app.response && app.responseTime)
      .reduce((acc, app) => {
        acc.total += app.responseTime;
        acc.count++;
        return acc;
      }, { total: 0, count: 0 });

    const avgResponseTime = responseTimeMetrics.count > 0 
      ? responseTimeMetrics.total / responseTimeMetrics.count 
      : 0;

    const metrics = {
      ...successRates,
      avgProcessingTime,
      avgResponseTime,
      applicationsByStatus: {
        submitted: applications.filter(app => app.status === 'submitted').length,
        failed: applications.filter(app => app.status === 'failed').length,
        pending: applications.filter(app => app.status === 'pending').length
      },
      topPerformingHours: Object.entries(successRates.patterns.applicationTiming || {})
        .sort(([,a], [,b]) => b.successRate - a.successRate)
        .slice(0, 3)
        .map(([hour, data]) => ({ hour: parseInt(hour), ...data }))
    };

    res.json({
      success: true,
      data: {
        metrics,
        timeframe,
        groupBy,
        lastUpdated: new Date()
      }
    });
  } catch (error) {
    logger.error('Error getting performance metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get performance metrics',
      error: error.message
    });
  }
});

module.exports = router;