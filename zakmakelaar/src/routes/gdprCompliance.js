const express = require('express');
const router = express.Router();
const gdprComplianceService = require('../services/gdprComplianceService');
const auditLogService = require('../services/auditLogService');
const { 
  authenticateWithAudit, 
  requireAdminWithAudit,
  standardRateLimit,
  strictRateLimit,
  requestLogger
} = require('../middleware/securityMiddleware');
const { loggers } = require('../services/logger');

/**
 * GDPR Compliance API Routes
 * 
 * Provides endpoints for:
 * - Consent management
 * - Data export (Right to Access)
 * - Data deletion (Right to Erasure)
 * - Privacy transparency
 */

// Apply middleware to all routes
router.use(requestLogger);
router.use(authenticateWithAudit);

/**
 * @route POST /api/gdpr/consent
 * @desc Record user consent
 * @access Private
 */
router.post('/consent', standardRateLimit, async (req, res) => {
  try {
    const { consentType, granted, privacyPolicyVersion } = req.body;
    
    if (!consentType || typeof granted !== 'boolean') {
      return res.status(400).json({
        status: 'error',
        message: 'Consent type and granted status are required'
      });
    }

    const validConsentTypes = ['data_processing', 'auto_application', 'marketing', 'analytics', 'third_party_sharing'];
    if (!validConsentTypes.includes(consentType)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid consent type'
      });
    }

    const consentRecord = await gdprComplianceService.recordConsent(
      req.user.id,
      consentType,
      granted,
      {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        privacyPolicyVersion: privacyPolicyVersion || '1.0',
        method: 'api'
      }
    );

    res.json({
      status: 'success',
      message: 'Consent recorded successfully',
      data: { consent: consentRecord }
    });

  } catch (error) {
    loggers.app.error('Error recording consent:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to record consent'
    });
  }
});

/**
 * @route GET /api/gdpr/consent
 * @desc Get user consent status
 * @access Private
 */
router.get('/consent', standardRateLimit, async (req, res) => {
  try {
    const { type } = req.query;
    
    const consentStatus = await gdprComplianceService.getConsentStatus(req.user.id, type);

    res.json({
      status: 'success',
      data: { consentStatus }
    });

  } catch (error) {
    loggers.app.error('Error getting consent status:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve consent status'
    });
  }
});

/**
 * @route POST /api/gdpr/export
 * @desc Export all user data (Right to Access)
 * @access Private
 */
router.post('/export', strictRateLimit, async (req, res) => {
  try {
    const { format = 'json', includeDocuments = false } = req.body;

    if (!['json', 'csv'].includes(format)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid export format. Supported formats: json, csv'
      });
    }

    const exportData = await gdprComplianceService.exportUserData(req.user.id, {
      format,
      includeDocuments
    });

    // Set appropriate headers for download
    res.setHeader('Content-Type', format === 'json' ? 'application/json' : 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="user-data-export-${Date.now()}.${format}"`);

    if (format === 'json') {
      res.json(exportData);
    } else {
      // Convert to CSV (simplified implementation)
      const csv = this._convertToCSV(exportData);
      res.send(csv);
    }

  } catch (error) {
    loggers.app.error('Error exporting user data:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to export user data'
    });
  }
});

/**
 * @route POST /api/gdpr/delete
 * @desc Request data deletion (Right to Erasure)
 * @access Private
 */
router.post('/delete', strictRateLimit, async (req, res) => {
  try {
    const { 
      confirmDeletion, 
      reason = 'user_request',
      keepAuditLogs = true,
      anonymizeInsteadOfDelete = false 
    } = req.body;

    if (!confirmDeletion) {
      return res.status(400).json({
        status: 'error',
        message: 'Deletion confirmation required',
        code: 'CONFIRMATION_REQUIRED'
      });
    }

    // Additional security check - require recent authentication
    const lastLogin = req.user.activity?.lastLogin;
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    
    if (!lastLogin || lastLogin < fiveMinutesAgo) {
      return res.status(403).json({
        status: 'error',
        message: 'Recent authentication required for data deletion',
        code: 'RECENT_AUTH_REQUIRED'
      });
    }

    const deletionSummary = await gdprComplianceService.deleteUserData(req.user.id, {
      reason,
      keepAuditLogs,
      anonymizeInsteadOfDelete
    });

    res.json({
      status: 'success',
      message: 'Data deletion completed successfully',
      data: { deletionSummary }
    });

  } catch (error) {
    loggers.app.error('Error deleting user data:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete user data'
    });
  }
});

/**
 * @route GET /api/gdpr/privacy-report
 * @desc Get privacy transparency report
 * @access Private
 */
router.get('/privacy-report', standardRateLimit, async (req, res) => {
  try {
    const report = await gdprComplianceService.getPrivacyTransparencyReport(req.user.id);

    res.json({
      status: 'success',
      data: { report }
    });

  } catch (error) {
    loggers.app.error('Error generating privacy report:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to generate privacy report'
    });
  }
});

/**
 * @route GET /api/gdpr/audit-logs
 * @desc Get user's audit logs
 * @access Private
 */
router.get('/audit-logs', standardRateLimit, async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      actions,
      categories,
      limit = 50,
      offset = 0
    } = req.query;

    const options = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      actions: actions ? actions.split(',') : undefined,
      categories: categories ? categories.split(',') : undefined,
      limit: Math.min(parseInt(limit), 100), // Max 100 logs per request
      offset: parseInt(offset)
    };

    const auditLogs = await auditLogService.getUserAuditLogs(req.user.id, options);

    res.json({
      status: 'success',
      data: { 
        auditLogs,
        pagination: {
          limit: options.limit,
          offset: options.offset,
          hasMore: auditLogs.length === options.limit
        }
      }
    });

  } catch (error) {
    loggers.app.error('Error retrieving audit logs:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve audit logs'
    });
  }
});

// Admin-only routes
/**
 * @route GET /api/gdpr/admin/compliance-report
 * @desc Generate system-wide compliance report (Admin only)
 * @access Admin
 */
router.get('/admin/compliance-report', requireAdminWithAudit, standardRateLimit, async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      userId
    } = req.query;

    const options = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      userId
    };

    const report = await auditLogService.generateComplianceReport(options);

    res.json({
      status: 'success',
      data: { report }
    });

  } catch (error) {
    loggers.app.error('Error generating compliance report:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to generate compliance report'
    });
  }
});

/**
 * @route GET /api/gdpr/admin/audit-logs
 * @desc Get system-wide audit logs (Admin only)
 * @access Admin
 */
router.get('/admin/audit-logs', requireAdminWithAudit, standardRateLimit, async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      actions,
      categories,
      severities,
      userIds,
      limit = 100,
      offset = 0
    } = req.query;

    const options = {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      actions: actions ? actions.split(',') : undefined,
      categories: categories ? categories.split(',') : undefined,
      severities: severities ? severities.split(',') : undefined,
      userIds: userIds ? userIds.split(',') : undefined,
      limit: Math.min(parseInt(limit), 500), // Max 500 logs per request for admin
      offset: parseInt(offset)
    };

    const result = await auditLogService.getSystemAuditLogs(options);

    res.json({
      status: 'success',
      data: result
    });

  } catch (error) {
    loggers.app.error('Error retrieving system audit logs:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve system audit logs'
    });
  }
});

/**
 * @route POST /api/gdpr/admin/cleanup-logs
 * @desc Clean up expired audit logs (Admin only)
 * @access Admin
 */
router.post('/admin/cleanup-logs', requireAdminWithAudit, strictRateLimit, async (req, res) => {
  try {
    const deletedCount = await auditLogService.cleanupExpiredLogs();

    res.json({
      status: 'success',
      message: `Cleaned up ${deletedCount} expired audit logs`,
      data: { deletedCount }
    });

  } catch (error) {
    loggers.app.error('Error cleaning up audit logs:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to cleanup audit logs'
    });
  }
});

// Helper method to convert data to CSV
function _convertToCSV(data) {
  // Simplified CSV conversion - in production, use a proper CSV library
  const headers = Object.keys(data).join(',');
  const values = Object.values(data).map(value => 
    typeof value === 'object' ? JSON.stringify(value) : value
  ).join(',');
  
  return `${headers}\n${values}`;
}

module.exports = router;