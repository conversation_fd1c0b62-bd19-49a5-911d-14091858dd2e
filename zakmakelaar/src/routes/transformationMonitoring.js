/**
 * Transformation Monitoring Routes
 * 
 * This module provides API endpoints for monitoring the transformation pipeline,
 * including metrics, alerts, and performance data.
 */

const express = require('express');
const router = express.Router();
const { transformationMonitor } = require('../monitoring/transformationMonitor');
const { catchAsync } = require('../middleware/errorHandler');

/**
 * @swagger
 * /api/monitoring/transformation/metrics:
 *   get:
 *     summary: Get transformation pipeline metrics
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Metrics retrieved successfully
 */
router.get('/metrics', catchAsync(async (req, res) => {
  const metrics = transformationMonitor.getMetrics();
  
  res.json({
    status: 'success',
    data: {
      metrics: metrics.formattedMetrics,
      raw: {
        totalTransformations: metrics.totalTransformations,
        successfulTransformations: metrics.successfulTransformations,
        failedTransformations: metrics.failedTransformations,
        successRate: metrics.successRate,
        averageTransformationTime: metrics.averageTransformationTime,
        dataQualityScores: metrics.dataQualityScores,
        memoryUsage: {
          average: Math.round(metrics.memoryUsage.average / 1024 / 1024),
          peak: Math.round(metrics.memoryUsage.peak / 1024 / 1024)
        },
        errorsByType: metrics.errorsByType
      }
    }
  });
}));

/**
 * @swagger
 * /api/monitoring/transformation/alerts:
 *   get:
 *     summary: Get transformation pipeline alerts
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Alerts retrieved successfully
 */
router.get('/alerts', catchAsync(async (req, res) => {
  const count = parseInt(req.query.count) || 10;
  const alerts = await transformationMonitor.getRecentAlerts(count);
  
  res.json({
    status: 'success',
    data: {
      alerts: alerts.flatMap(log => log.alerts),
      alertCount: alerts.reduce((sum, log) => sum + log.count, 0),
      criticalCount: alerts.reduce((sum, log) => sum + log.criticalCount, 0)
    }
  });
}));

/**
 * @swagger
 * /api/monitoring/transformation/report:
 *   get:
 *     summary: Get transformation pipeline performance report
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Report retrieved successfully
 */
router.get('/report', catchAsync(async (req, res) => {
  const report = transformationMonitor.generatePerformanceReport();
  
  res.json({
    status: 'success',
    data: report
  });
}));

/**
 * @swagger
 * /api/monitoring/transformation/health:
 *   get:
 *     summary: Get transformation pipeline health status
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Health status retrieved successfully
 */
router.get('/health', catchAsync(async (req, res) => {
  const metrics = transformationMonitor.getMetrics();
  
  // Determine health status based on metrics
  let status = 'healthy';
  const successRate = parseFloat(metrics.successRate);
  
  if (successRate < 50) {
    status = 'unhealthy';
  } else if (successRate < 80) {
    status = 'degraded';
  }
  
  // Check data quality
  if (metrics.dataQualityScores.completeness.average < 50 || 
      metrics.dataQualityScores.accuracy.average < 50) {
    status = 'unhealthy';
  } else if (metrics.dataQualityScores.completeness.average < 70 || 
             metrics.dataQualityScores.accuracy.average < 70) {
    status = 'degraded';
  }
  
  res.json({
    status: 'success',
    data: {
      health: {
        status,
        timestamp: new Date().toISOString(),
        metrics: {
          successRate: metrics.successRate,
          averageTransformationTime: metrics.averageTransformationTimeFormatted,
          dataQualityCompleteness: `${metrics.dataQualityScores.completeness.average}%`,
          dataQualityAccuracy: `${metrics.dataQualityScores.accuracy.average}%`,
          lastTransformationTime: metrics.lastTransformationTime
        }
      }
    }
  });
}));

module.exports = router;