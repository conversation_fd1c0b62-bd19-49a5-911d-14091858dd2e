/**
 * Schema Transformer Example
 * 
 * This example demonstrates how to use the SchemaTransformer to transform
 * raw scraper data into the unified property schema format.
 */

const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { MappingConfigLoader } = require('../services/mappingConfigLoader');
const { SchemaTransformer } = require('../services/schemaTransformer');
const { validateProperty } = require('../schemas/unifiedPropertySchema');
const path = require('path');

async function runExample() {
  try {
    console.log('Schema Transformer Example');
    console.log('=========================\n');
    
    // Create registry and load mappings
    console.log('1. Creating Field Mapping Registry...');
    const registry = new FieldMappingRegistry();
    
    // Load default mappings
    console.log('2. Loading default mappings...');
    const configLoader = new MappingConfigLoader(registry);
    await configLoader.loadDefaults();
    
    // Create transformer
    console.log('3. Creating Schema Transformer...');
    const transformer = new SchemaTransformer(registry);
    
    // Sample raw data from Funda
    console.log('4. Preparing sample data...');
    const fundaSample = {
      title: 'Modern Apartment in Amsterdam',
      description: 'Beautiful modern apartment in the center of Amsterdam',
      url: 'https://www.funda.nl/huur/amsterdam/appartement-12345678',
      price: '€1.250 per month',
      propertyType: 'Appartement',
      location: 'Amsterdam, Noord-Holland',
      size: '75 m²',
      rooms: '3 kamers (2 slaapkamers)',
      year: 'Bouwjaar 2015',
      interior: 'Gemeubileerd',
      images: [
        'https://cloud.funda.nl/image1.jpg',
        'https://cloud.funda.nl/image2.jpg'
      ],
      energyLabel: 'A',
      garden: 'Ja',
      balcony: 'Ja',
      parking: 'Nee'
    };
    
    // Transform the data
    console.log('5. Transforming data...');
    const transformedData = await transformer.transform(fundaSample, 'funda');
    
    // Validate the transformed data
    console.log('6. Validating transformed data...');
    const validation = validateProperty(transformedData);
    
    if (validation.error) {
      console.error('Validation failed:', validation.error.message);
    } else {
      console.log('Validation successful!');
    }
    
    // Calculate data quality
    console.log('7. Calculating data quality...');
    const quality = transformer.calculateDataQuality(transformedData);
    console.log(`   Completeness: ${quality.completeness}%`);
    console.log(`   Accuracy: ${quality.accuracy}%`);
    
    // Print the transformed data
    console.log('\n8. Transformed Data:');
    console.log(JSON.stringify(transformedData, null, 2));
    
    // Example of batch transformation
    console.log('\n9. Batch transformation example:');
    const batchSamples = [
      fundaSample,
      {
        title: 'House in Rotterdam',
        description: 'Spacious house with garden',
        url: 'https://www.funda.nl/huur/rotterdam/huis-87654321',
        price: '€1.800 per month',
        propertyType: 'Huis',
        location: 'Rotterdam, Zuid-Holland',
        size: '120 m²',
        rooms: '5 kamers (4 slaapkamers)',
        year: 'Bouwjaar 2000',
        interior: 'Gestoffeerd',
        images: ['https://cloud.funda.nl/image3.jpg'],
        garden: 'Ja',
        balcony: 'Nee',
        parking: 'Ja'
      }
    ];
    
    const batchResult = await transformer.batchTransform(batchSamples, 'funda');
    console.log(`   Processed: ${batchResult.totalProcessed}`);
    console.log(`   Success: ${batchResult.successCount}`);
    console.log(`   Errors: ${batchResult.errorCount}`);
    
    console.log('\nExample completed successfully!');
  } catch (error) {
    console.error('Error in example:', error);
  }
}

// Run the example if this script is executed directly
if (require.main === module) {
  runExample().catch(console.error);
}

module.exports = { runExample };