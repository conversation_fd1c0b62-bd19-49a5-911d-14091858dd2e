/**
 * Transformation Integration Example
 * 
 * This example demonstrates how to use the transformation pipeline
 * with the existing scrapers.
 */

const { 
  validateAndNormalizeListing, 
  validateAndNormalizeListingEnhanced,
  initializeTransformationPipeline 
} = require('../services/transformationIntegration');

// Sample listing data
const sampleListing = {
  title: 'Beautiful Apartment in Amsterdam',
  description: 'A spacious apartment in the center of Amsterdam',
  url: 'https://www.funda.nl/huur/amsterdam/appartement-12345678/',
  location: 'Amsterdam, Noord-Holland',
  price: '€ 1.500 per maand',
  propertyType: 'appartement',
  size: '85 m²',
  rooms: '3',
  bedrooms: '2',
  year: '2010',
  interior: 'Gemeubileerd',
  source: 'funda.nl',
  images: [
    'https://example.com/image1.jpg',
    'https://example.com/image2.jpg'
  ]
};

// Example of using the synchronous compatibility wrapper
function synchronousExample() {
  console.log('Using synchronous compatibility wrapper:');
  
  const normalizedListing = validateAndNormalizeListing(sampleListing);
  
  console.log('Normalized listing:');
  console.log(`- Title: ${normalizedListing.title}`);
  console.log(`- Location: ${normalizedListing.location}`);
  console.log(`- Price: ${normalizedListing.price}`);
  console.log(`- Property Type: ${normalizedListing.propertyType}`);
  
  return normalizedListing;
}

// Example of using the asynchronous enhanced version
async function asynchronousExample() {
  console.log('\nUsing asynchronous enhanced version:');
  
  // Initialize the transformation pipeline
  await initializeTransformationPipeline();
  
  const transformedListing = await validateAndNormalizeListingEnhanced(sampleListing);
  
  console.log('Transformed listing:');
  console.log(`- Title: ${transformedListing.title}`);
  console.log(`- Location: ${transformedListing.location}`);
  console.log(`- Price: ${transformedListing.price}`);
  console.log(`- Property Type: ${transformedListing.propertyType}`);
  
  // Access unified schema data
  console.log('\nUnified schema data:');
  console.log(`- Data Quality Completeness: ${transformedListing.unifiedData._internal.dataQuality.completeness}%`);
  console.log(`- Data Quality Accuracy: ${transformedListing.unifiedData._internal.dataQuality.accuracy}%`);
  
  return transformedListing;
}

// Run the examples
async function runExamples() {
  try {
    // Run synchronous example
    const syncResult = synchronousExample();
    
    // Run asynchronous example
    const asyncResult = await asynchronousExample();
    
    console.log('\nExamples completed successfully!');
  } catch (error) {
    console.error('Error running examples:', error);
  }
}

// Run the examples if this file is executed directly
if (require.main === module) {
  runExamples();
}

module.exports = {
  synchronousExample,
  asynchronousExample,
  runExamples
};