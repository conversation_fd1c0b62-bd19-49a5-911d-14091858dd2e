const mongoose = require("mongoose");

const messageSchema = new mongoose.Schema({
  match: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SocialMatch',
    required: true
  },
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  recipient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: true,
    maxlength: 1000
  },
  messageType: {
    type: String,
    enum: ['text', 'system'],
    default: 'text'
  },
  readAt: {
    type: Date
  },
  editedAt: {
    type: Date
  },
  deletedAt: {
    type: Date
  },
  isDeleted: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for performance
messageSchema.index({ match: 1, createdAt: -1 });
messageSchema.index({ sender: 1, createdAt: -1 });
messageSchema.index({ recipient: 1, readAt: 1 });
messageSchema.index({ match: 1, isDeleted: 1, createdAt: -1 });

// Virtual to check if message is read
messageSchema.virtual('isRead').get(function() {
  return !!this.readAt;
});

// Virtual to check if message is edited
messageSchema.virtual('isEdited').get(function() {
  return !!this.editedAt;
});

// Static method to get conversation between users
messageSchema.statics.getConversation = function(matchId, page = 1, limit = 50) {
  const skip = (page - 1) * limit;
  
  return this.find({
    match: matchId,
    isDeleted: false
  })
  .populate('sender', 'profile.firstName profile.lastName profile.profilePicture')
  .populate('recipient', 'profile.firstName profile.lastName profile.profilePicture')
  .sort({ createdAt: -1 })
  .skip(skip)
  .limit(limit);
};

// Static method to mark messages as read
messageSchema.statics.markAsRead = function(matchId, userId) {
  return this.updateMany(
    {
      match: matchId,
      recipient: userId,
      readAt: { $exists: false }
    },
    {
      readAt: new Date()
    }
  );
};

// Static method to get unread count for a user
messageSchema.statics.getUnreadCount = function(userId) {
  return this.countDocuments({
    recipient: userId,
    readAt: { $exists: false },
    isDeleted: false
  });
};

// Ensure virtual fields are serialized
messageSchema.set('toJSON', { virtuals: true });
messageSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model("Message", messageSchema);