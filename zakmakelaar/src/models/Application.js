const mongoose = require('mongoose');

const applicationSchema = new mongoose.Schema({
  // Basic application information
  applicant: {
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    // Snapshot of applicant data at time of application
    snapshot: {
      name: { type: String, required: true },
      email: { type: String, required: true },
      phone: { type: String },
      tenantScore: { type: Number },
      tenantScoreGrade: { type: String },
      profileCompleteness: { type: Number }
    }
  },
  
  // Property information
  property: {
    propertyId: { type: mongoose.Schema.Types.ObjectId, ref: 'Property', required: true },
    // Snapshot of property data at time of application
    snapshot: {
      title: { type: String, required: true },
      address: { type: String, required: true },
      rent: { type: Number, required: true },
      propertyType: { type: String }
    }
  },
  
  // Application details
  applicationData: {
    moveInDate: { type: Date, required: true },
    leaseDuration: { type: Number }, // in months
    numberOfOccupants: { type: Number, default: 1 },
    
    // Personal message from applicant
    personalMessage: { type: String, maxlength: 1000 },
    
    // Specific answers to property owner questions
    ownerQuestions: [{
      question: { type: String },
      answer: { type: String }
    }],
    
    // Income and employment details
    employment: {
      monthlyIncome: { type: Number },
      employer: { type: String },
      occupation: { type: String },
      contractType: { type: String },
      employmentStartDate: { type: Date }
    },
    
    // References
    references: [{
      type: { type: String, enum: ['landlord', 'employer', 'personal'] },
      name: { type: String },
      email: { type: String },
      phone: { type: String },
      relationship: { type: String },
      contactPermission: { type: Boolean, default: false }
    }],
    
    // Emergency contact
    emergencyContact: {
      name: { type: String },
      relationship: { type: String },
      phone: { type: String },
      email: { type: String }
    }
  },
  
  // Application status and workflow
  status: {
    type: String,
    enum: [
      'draft',           // Application being prepared
      'submitted',       // Application submitted, awaiting review
      'under_review',    // Being reviewed by property owner
      'screening',       // Undergoing tenant screening
      'shortlisted',     // Selected for further consideration
      'interview',       // Scheduled for interview/viewing
      'approved',        // Application approved
      'conditional',     // Approved with conditions
      'rejected',        // Application rejected
      'withdrawn',       // Withdrawn by applicant
      'expired'          // Application expired
    ],
    default: 'draft'
  },
  
  // Status history for tracking
  statusHistory: [{
    status: { type: String },
    changedAt: { type: Date, default: Date.now },
    changedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    reason: { type: String },
    notes: { type: String }
  }],
  
  // Screening and evaluation results
  screening: {
    tenantScore: { type: Number },
    screeningDate: { type: Date },
    screenedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    
    // Detailed screening results
    results: {
      incomeVerification: {
        verified: { type: Boolean },
        ratio: { type: Number }, // rent-to-income ratio
        sufficient: { type: Boolean }
      },
      
      employmentVerification: {
        verified: { type: Boolean },
        stable: { type: Boolean },
        contactedEmployer: { type: Boolean }
      },
      
      referenceCheck: {
        landlordReference: {
          contacted: { type: Boolean },
          positive: { type: Boolean },
          notes: { type: String }
        },
        employerReference: {
          contacted: { type: Boolean },
          positive: { type: Boolean },
          notes: { type: String }
        }
      },
      
      documentVerification: {
        allRequired: { type: Boolean },
        verified: { type: Boolean },
        missingDocuments: [{ type: String }]
      },
      
      backgroundCheck: {
        completed: { type: Boolean },
        issues: [{ type: String }],
        riskLevel: { type: String, enum: ['low', 'medium', 'high'] }
      }
    },
    
    // AI-generated insights
    aiInsights: {
      recommendation: { type: String, enum: ['approve', 'conditional', 'reject'] },
      confidence: { type: Number }, // 0-100
      reasoning: { type: String },
      riskFactors: [{ type: String }],
      strengths: [{ type: String }]
    },
    
    // Overall screening score and recommendation
    overallScore: { type: Number }, // 0-100
    recommendation: { type: String, enum: ['approve', 'conditional', 'reject'] },
    notes: { type: String }
  },
  
  // Communication and interaction
  communications: [{
    type: { type: String, enum: ['message', 'email', 'phone', 'meeting'] },
    from: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    to: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    subject: { type: String },
    content: { type: String },
    timestamp: { type: Date, default: Date.now },
    read: { type: Boolean, default: false }
  }],
  
  // Viewing and meeting scheduling
  viewings: [{
    scheduledDate: { type: Date },
    duration: { type: Number }, // in minutes
    type: { type: String, enum: ['individual', 'group', 'virtual'] },
    status: { type: String, enum: ['scheduled', 'completed', 'cancelled', 'no_show'] },
    attendees: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
    notes: { type: String },
    rating: { type: Number, min: 1, max: 5 }, // applicant rating of viewing
    feedback: { type: String }
  }],
  
  // Documents submitted with application
  documents: [{
    type: { 
      type: String, 
      enum: ['income_proof', 'employment_contract', 'bank_statement', 'id_document', 'rental_reference', 'other'],
      required: true
    },
    filename: { type: String, required: true },
    originalName: { type: String },
    fileSize: { type: Number },
    mimeType: { type: String },
    uploadedAt: { type: Date, default: Date.now },
    verified: { type: Boolean, default: false },
    verifiedAt: { type: Date },
    verifiedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    notes: { type: String }
  }],
  
  // Decision and outcome
  decision: {
    finalDecision: { type: String, enum: ['approved', 'rejected', 'withdrawn'] },
    decisionDate: { type: Date },
    decisionBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    reason: { type: String },
    conditions: [{ type: String }], // If conditionally approved
    
    // Contract details if approved
    contract: {
      startDate: { type: Date },
      endDate: { type: Date },
      monthlyRent: { type: Number },
      deposit: { type: Number },
      contractSigned: { type: Boolean, default: false },
      contractSignedDate: { type: Date }
    }
  },
  
  // Priority and ranking
  priority: {
    score: { type: Number, default: 0 }, // Overall priority score
    rank: { type: Number }, // Rank among all applications for this property
    factors: {
      tenantScore: { type: Number },
      applicationCompleteness: { type: Number },
      responseTime: { type: Number },
      ownerPreference: { type: Number }
    }
  },
  
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  submittedAt: { type: Date },
  reviewedAt: { type: Date },
  expiresAt: { type: Date },
  
  // Metadata
  metadata: {
    source: { type: String }, // How the application was submitted
    userAgent: { type: String },
    ipAddress: { type: String },
    applicationVersion: { type: String }
  }
});

// Indexes for performance
applicationSchema.index({ 'applicant.userId': 1 });
applicationSchema.index({ 'property.propertyId': 1 });
applicationSchema.index({ status: 1 });
applicationSchema.index({ createdAt: -1 });
applicationSchema.index({ submittedAt: -1 });
applicationSchema.index({ 'screening.overallScore': -1 });
applicationSchema.index({ 'priority.score': -1 });

// Compound indexes for common queries
applicationSchema.index({ 'property.propertyId': 1, status: 1 });
applicationSchema.index({ 'applicant.userId': 1, status: 1 });
applicationSchema.index({ 'property.propertyId': 1, 'priority.rank': 1 });
applicationSchema.index({ status: 1, createdAt: -1 });

// Virtual fields
applicationSchema.virtual('isActive').get(function() {
  return ['submitted', 'under_review', 'screening', 'shortlisted', 'interview'].includes(this.status);
});

applicationSchema.virtual('isCompleted').get(function() {
  return ['approved', 'rejected', 'withdrawn', 'expired'].includes(this.status);
});

applicationSchema.virtual('daysSinceSubmission').get(function() {
  if (!this.submittedAt) return null;
  return Math.floor((new Date() - this.submittedAt) / (1000 * 60 * 60 * 24));
});

applicationSchema.virtual('completenessScore').get(function() {
  let score = 0;
  let maxScore = 0;
  
  // Basic information
  maxScore += 20;
  if (this.applicationData.personalMessage) score += 5;
  if (this.applicationData.moveInDate) score += 5;
  if (this.applicationData.employment.monthlyIncome) score += 5;
  if (this.applicationData.employment.employer) score += 5;
  
  // References
  maxScore += 20;
  score += Math.min(this.applicationData.references.length * 10, 20);
  
  // Documents
  maxScore += 30;
  score += Math.min(this.documents.length * 6, 30);
  
  // Emergency contact
  maxScore += 10;
  if (this.applicationData.emergencyContact.name) score += 10;
  
  // Owner questions
  maxScore += 20;
  score += Math.min(this.applicationData.ownerQuestions.length * 5, 20);
  
  return Math.round((score / maxScore) * 100);
});

// Pre-save middleware
applicationSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Set submitted date when status changes to submitted
  if (this.isModified('status') && this.status === 'submitted' && !this.submittedAt) {
    this.submittedAt = new Date();
  }
  
  // Set reviewed date when status changes from submitted
  if (this.isModified('status') && this.status !== 'submitted' && this.status !== 'draft' && !this.reviewedAt) {
    this.reviewedAt = new Date();
  }
  
  // Add status change to history
  if (this.isModified('status')) {
    this.statusHistory.push({
      status: this.status,
      changedAt: new Date()
    });
  }
  
  // Set expiration date for new applications
  if (this.isNew && !this.expiresAt) {
    this.expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
  }
  
  next();
});

// Static methods
applicationSchema.statics.findByProperty = function(propertyId, status = null) {
  const query = { 'property.propertyId': propertyId };
  if (status) query.status = status;
  return this.find(query).sort({ 'priority.score': -1, createdAt: -1 });
};

applicationSchema.statics.findByApplicant = function(userId, status = null) {
  const query = { 'applicant.userId': userId };
  if (status) query.status = status;
  return this.find(query).sort({ createdAt: -1 });
};

applicationSchema.statics.findPendingScreening = function() {
  return this.find({ 
    status: { $in: ['submitted', 'under_review'] },
    'screening.screeningDate': { $exists: false }
  }).sort({ submittedAt: 1 });
};

applicationSchema.statics.getApplicationStats = function(propertyId) {
  return this.aggregate([
    { $match: { 'property.propertyId': mongoose.Types.ObjectId(propertyId) } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        averageScore: { $avg: '$screening.overallScore' }
      }
    }
  ]);
};

applicationSchema.statics.rankApplications = function(propertyId) {
  return this.find({ 'property.propertyId': propertyId, status: { $in: ['submitted', 'under_review', 'screening'] } })
    .sort({ 'priority.score': -1, 'screening.overallScore': -1, submittedAt: 1 });
};

// Instance methods
applicationSchema.methods.updateStatus = function(newStatus, reason = '', changedBy = null) {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    changedAt: new Date(),
    changedBy,
    reason
  });
  return this.save();
};

applicationSchema.methods.addCommunication = function(type, from, to, content, subject = '') {
  this.communications.push({
    type,
    from,
    to,
    subject,
    content,
    timestamp: new Date()
  });
  return this.save();
};

applicationSchema.methods.scheduleViewing = function(date, duration = 60, type = 'individual') {
  this.viewings.push({
    scheduledDate: date,
    duration,
    type,
    status: 'scheduled'
  });
  return this.save();
};

applicationSchema.methods.updateScreening = function(screeningData) {
  this.screening = { ...this.screening, ...screeningData };
  this.screening.screeningDate = new Date();
  return this.save();
};

applicationSchema.methods.calculatePriority = function() {
  let score = 0;
  
  // Tenant score factor (40%)
  if (this.screening.overallScore) {
    score += (this.screening.overallScore / 100) * 40;
  }
  
  // Application completeness (30%)
  score += (this.completenessScore / 100) * 30;
  
  // Response time factor (20%) - earlier applications get higher score
  if (this.submittedAt) {
    const daysSince = this.daysSinceSubmission;
    const responseScore = Math.max(0, 100 - (daysSince * 5)); // Decrease by 5 points per day
    score += (responseScore / 100) * 20;
  }
  
  // Owner preference (10%) - can be set manually
  if (this.priority.factors.ownerPreference) {
    score += (this.priority.factors.ownerPreference / 100) * 10;
  }
  
  this.priority.score = Math.round(score);
  this.priority.factors = {
    tenantScore: this.screening.overallScore || 0,
    applicationCompleteness: this.completenessScore,
    responseTime: this.submittedAt ? Math.max(0, 100 - (this.daysSinceSubmission * 5)) : 0,
    ownerPreference: this.priority.factors.ownerPreference || 0
  };
  
  return this.save();
};

applicationSchema.methods.isExpired = function() {
  return this.expiresAt && new Date() > this.expiresAt;
};

applicationSchema.methods.canBeWithdrawn = function() {
  return ['draft', 'submitted', 'under_review'].includes(this.status);
};

applicationSchema.methods.requiresAction = function() {
  return this.status === 'submitted' || 
         (this.status === 'conditional' && !this.decision.contract.contractSigned);
};

// Ensure virtual fields are serialized
applicationSchema.set('toJSON', { virtuals: true });
applicationSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Application', applicationSchema);