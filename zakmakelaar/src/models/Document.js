const mongoose = require("mongoose");
const crypto = require("crypto");

const documentSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true
  },
  filename: { 
    type: String, 
    required: true 
  },
  originalName: { 
    type: String, 
    required: true 
  },
  type: { 
    type: String, 
    enum: ["income_proof", "employment_contract", "bank_statement", "id_document", "rental_reference", "other"],
    required: true
  },
  size: { 
    type: Number, 
    required: true 
  },
  mimeType: { 
    type: String, 
    required: true 
  },
  encryptedPath: { 
    type: String, 
    required: true 
  },
  thumbnailPath: { 
    type: String 
  },
  verified: { 
    type: Boolean, 
    default: false
  },
  verifiedBy: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User' 
  },
  verifiedAt: { 
    type: Date 
  },
  expiryDate: { 
    type: Date
  },
  metadata: {
    extractedText: { type: String },
    pageCount: { type: Number },
    language: { type: String },
    containsPII: { type: Boolean, default: false },
    securityLevel: { 
      type: String, 
      enum: ["low", "medium", "high"], 
      default: "medium" 
    }
  },
  encryptionKey: { 
    type: String, 
    required: true 
  },
  accessLog: [{
    accessedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    accessedAt: { type: Date, default: Date.now },
    action: { 
      type: String, 
      enum: ["view", "download", "verify", "delete"] 
    },
    ipAddress: { type: String }
  }],
  createdAt: { 
    type: Date, 
    default: Date.now
  },
  updatedAt: { 
    type: Date, 
    default: Date.now 
  }
});

// Indexes for performance
documentSchema.index({ userId: 1, type: 1 });
documentSchema.index({ userId: 1, verified: 1 });
documentSchema.index({ createdAt: -1 });
documentSchema.index({ expiryDate: 1 }, { sparse: true });

// Virtual for checking if document is expired
documentSchema.virtual('isExpired').get(function() {
  return this.expiryDate && this.expiryDate < new Date();
});

// Virtual for file extension
documentSchema.virtual('fileExtension').get(function() {
  return this.originalName.split('.').pop().toLowerCase();
});

// Virtual for human-readable file size
documentSchema.virtual('humanReadableSize').get(function() {
  const bytes = this.size;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
});

// Pre-save middleware to update updatedAt
documentSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Pre-save middleware to generate encryption key if not exists
documentSchema.pre('save', function(next) {
  if (!this.encryptionKey) {
    this.encryptionKey = crypto.randomBytes(32).toString('hex');
  }
  next();
});

// Method to log access
documentSchema.methods.logAccess = function(userId, action, ipAddress) {
  this.accessLog.push({
    accessedBy: userId,
    action: action,
    ipAddress: ipAddress
  });
  return this.save();
};

// Method to check if user can access document
documentSchema.methods.canAccess = function(userId, userRole) {
  // Owner can always access
  if (this.userId.toString() === userId.toString()) {
    return true;
  }
  
  // Admin can access all documents
  if (userRole === 'admin') {
    return true;
  }
  
  return false;
};

// Static method to find documents by user and type
documentSchema.statics.findByUserAndType = function(userId, type) {
  const query = { userId };
  if (type) {
    query.type = type;
  }
  return this.find(query).sort({ createdAt: -1 });
};

// Static method to find expired documents
documentSchema.statics.findExpired = function() {
  return this.find({
    expiryDate: { $lt: new Date() }
  });
};

// Static method to find unverified documents
documentSchema.statics.findUnverified = function() {
  return this.find({ verified: false }).populate('userId', 'email profile.firstName profile.lastName');
};

// Ensure virtual fields are serialized
documentSchema.set('toJSON', { virtuals: true });
documentSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model("Document", documentSchema);