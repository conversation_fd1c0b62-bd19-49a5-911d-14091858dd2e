const mongoose = require("mongoose");
const bcrypt = require("bcrypt");

const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: {
    type: String,
    enum: ["user", "admin", "owner"],
    default: "user",
  },
  emailVerified: { type: Boolean, default: false },
  emailVerificationToken: { type: String },
  passwordResetToken: { type: String },
  passwordResetExpires: { type: Date },
  propertyOwner: {
    isPropertyOwner: { type: Boolean, default: false },
    properties: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Property' }],
    verificationStatus: { 
      type: String, 
      enum: ['pending', 'verified', 'rejected'], 
      default: 'pending' 
    },
    verificationDocuments: [{
      type: { type: String },
      url: { type: String },
      uploadDate: { type: Date, default: Date.now },
      status: { 
        type: String, 
        enum: ['pending', 'verified', 'rejected'], 
        default: 'pending' 
      }
    }]
  },
  preferences: {
    // Original fields
    location: { type: String },
    budget: { type: Number },
    rooms: { type: Number },
    propertyType: {
      type: String,
      enum: ["apartment", "house", "studio", "room", "any"],
    },
    minSize: { type: Number },
    maxSize: { type: Number },
    interior: {
      type: String,
      enum: ["kaal", "gestoffeerd", "gemeubileerd", "any"],
    },
    parking: { type: Boolean },
    balcony: { type: Boolean },
    garden: { type: Boolean },
    furnished: { type: Boolean },
    petsAllowed: { type: Boolean },
    smokingAllowed: { type: Boolean },
    studentFriendly: { type: Boolean },
    expatFriendly: { type: Boolean },
    commuteTime: { type: Number }, // in minutes
    preferredNeighborhoods: [{ type: String }],
    excludedNeighborhoods: [{ type: String }],
    
    // New fields to match frontend structure
    minPrice: { type: Number },
    maxPrice: { type: Number },
    minRooms: { type: Number },
    maxRooms: { type: Number },
    preferredLocations: [{ type: String }],
    propertyTypes: [{ type: String }],
    amenities: [{ type: String }],
    notifications: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true },
      sms: { type: Boolean, default: false }
    }
  },
  profile: {
    // Basic profile information
    firstName: { type: String },
    lastName: { type: String },
    name: { type: String }, // Keep for backward compatibility
    dateOfBirth: { type: Date },
    nationality: { type: String },
    phoneNumber: { type: String },
    profilePicture: { type: String },
    
    // User type classification (Requirement 3.1, 3.2)
    userType: {
      type: [String],
      enum: ["student", "expat", "young_professional", "property_owner"],
      default: []
    },
    
    // Enhanced employment information (Requirement 8.1, 8.2)
    employment: {
      occupation: { type: String },
      employmentType: {
        type: String,
        enum: ["full-time", "part-time", "student", "freelancer", "unemployed"],
      },
      contractType: {
        type: String,
        enum: ["permanent", "temporary", "student", "freelancer"],
      },
      employer: { type: String },
      workLocation: { type: String },
      monthlyIncome: { type: Number },
      incomeVerified: { type: Boolean, default: false },
      incomeDocuments: [{ type: String }] // Document IDs
    },
    
    // Enhanced rental history (Requirement 8.1, 8.2)
    rentalHistory: {
      previousAddresses: [{
        address: { type: String },
        landlordName: { type: String },
        landlordContact: { type: String },
        rentAmount: { type: Number },
        duration: { type: String },
        reasonForLeaving: { type: String },
        reference: { type: String }
      }],
      evictions: { type: Boolean, default: false },
      paymentIssues: { type: Boolean, default: false },
      creditScore: { 
        type: String, 
        enum: ["excellent", "good", "fair", "poor"] 
      },
      // Keep old fields for backward compatibility
      previousRentals: [{ type: String }]
    },
    
    // Social matching preferences (Requirement 9.1, 9.2)
    socialPreferences: {
      lookingForRoommate: { type: Boolean, default: false },
      roommateCriteria: {
        ageRange: { 
          min: { type: Number, min: 18, max: 100 }, 
          max: { type: Number, min: 18, max: 100 } 
        },
        gender: { 
          type: String, 
          enum: ["male", "female", "any"], 
          default: "any" 
        },
        occupation: [{ type: String }],
        lifestyle: {
          cleanliness: { 
            type: String, 
            enum: ["very_clean", "clean", "moderate", "relaxed"],
            default: "moderate"
          },
          noiseLevel: { 
            type: String, 
            enum: ["very_quiet", "quiet", "moderate", "lively"],
            default: "moderate"
          },
          socialLevel: { 
            type: String, 
            enum: ["very_social", "social", "moderate", "private"],
            default: "moderate"
          },
          smokingTolerance: { type: Boolean, default: false },
          petTolerance: { type: Boolean, default: false },
          guestPolicy: { 
            type: String, 
            enum: ["strict", "moderate", "flexible"],
            default: "moderate"
          }
        }
      },
      isVisible: { type: Boolean, default: false } // For social matching visibility
    },
    
    // Keep legacy fields for backward compatibility
    income: { type: Number },
    moveInDate: { type: Date },
    leaseDuration: { type: Number }, // in months
    hasGuarantor: { type: Boolean }
  },
  aiSettings: {
    matchThreshold: { type: Number, default: 70 }, // minimum match score
    alertFrequency: {
      type: String,
      enum: ["immediate", "hourly", "daily"],
      default: "immediate",
    },
    preferredLanguage: {
      type: String,
      enum: ["dutch", "english", "arabic", "turkish", "polish"],
      default: "english",
    },
    includeMarketAnalysis: { type: Boolean, default: true },
    includeContractAnalysis: { type: Boolean, default: true },
    autoGenerateApplications: { type: Boolean, default: false },
    applicationTemplate: {
      type: String,
      enum: ["professional", "casual", "student", "expat"],
      default: "professional",
    },
    
    // New AI features
    tenantScoreVisible: { type: Boolean, default: true },
    socialMatchingEnabled: { type: Boolean, default: false },
    autoApplyEnabled: { type: Boolean, default: false },
    fakeListingDetection: { type: Boolean, default: true }
  },
  
  // Tenant scoring data
  tenantScore: {
    overallScore: { type: Number, min: 0, max: 100, default: 0 },
    components: {
      incomeStability: { type: Number, min: 0, max: 100, default: 0 },
      rentalHistory: { type: Number, min: 0, max: 100, default: 0 },
      creditworthiness: { type: Number, min: 0, max: 100, default: 0 },
      employment: { type: Number, min: 0, max: 100, default: 0 },
      references: { type: Number, min: 0, max: 100, default: 0 }
    },
    lastCalculated: { type: Date },
    verificationLevel: { 
      type: String, 
      enum: ["unverified", "partial", "verified"], 
      default: "unverified" 
    }
  },
  
  // Document vault
  documents: [{
    id: { type: String },
    type: { 
      type: String, 
      enum: ["income_proof", "employment_contract", "bank_statement", "id_document", "rental_reference"] 
    },
    filename: { type: String },
    uploadDate: { type: Date, default: Date.now },
    verified: { type: Boolean, default: false },
    expiryDate: { type: Date }
  }],

  // Auto-application specific fields
  autoApplication: {
    enabled: { type: Boolean, default: false },
    settings: {
      maxApplicationsPerDay: { type: Number, default: 5, min: 1, max: 20 },
      applicationTemplate: { 
        type: String, 
        enum: ['professional', 'casual', 'student', 'expat'], 
        default: 'professional' 
      },
      autoSubmit: { type: Boolean, default: false },
      requireManualReview: { type: Boolean, default: true },
      notificationPreferences: {
        immediate: { type: Boolean, default: true },
        daily: { type: Boolean, default: true },
        weekly: { type: Boolean, default: false }
      }
    },
    criteria: {
      maxPrice: { type: Number },
      minRooms: { type: Number, min: 1 },
      maxRooms: { type: Number, min: 1 },
      propertyTypes: [{ type: String }],
      locations: [{ type: String }],
      excludeKeywords: [{ type: String }],
      includeKeywords: [{ type: String }]
    },
    personalInfo: {
      fullName: { type: String },
      email: { type: String },
      phone: { type: String },
      dateOfBirth: { type: Date },
      nationality: { type: String },
      occupation: { type: String },
      employer: { type: String },
      monthlyIncome: { type: Number, min: 0 },
      moveInDate: { type: Date },
      leaseDuration: { type: Number, min: 1, max: 60 }, // in months
      numberOfOccupants: { type: Number, min: 1, max: 10 },
      hasGuarantor: { type: Boolean, default: false },
      guarantorInfo: {
        name: { type: String },
        email: { type: String },
        phone: { type: String },
        relationship: { type: String },
        monthlyIncome: { type: Number }
      },
      emergencyContact: {
        name: { type: String },
        email: { type: String },
        phone: { type: String },
        relationship: { type: String }
      }
    },
    requiredDocuments: [{
      type: { 
        type: String, 
        enum: ["income_proof", "employment_contract", "bank_statement", "id_document", "rental_reference"],
        required: true
      },
      required: { type: Boolean, default: true },
      uploaded: { type: Boolean, default: false },
      documentId: { type: String }, // Reference to Document model
      lastChecked: { type: Date, default: Date.now }
    }],
    profileCompleteness: {
      personalInfo: { type: Number, default: 0, min: 0, max: 100 },
      documents: { type: Number, default: 0, min: 0, max: 100 },
      overall: { type: Number, default: 0, min: 0, max: 100 },
      lastCalculated: { type: Date, default: Date.now }
    },
    applicationHistory: {
      totalApplications: { type: Number, default: 0 },
      successfulApplications: { type: Number, default: 0 },
      lastApplicationDate: { type: Date },
      dailyApplicationCount: { type: Number, default: 0 },
      dailyResetDate: { type: Date, default: Date.now }
    }
  },
  
  // Property owner specific fields
  propertyOwner: {
    isPropertyOwner: { type: Boolean, default: false },
    properties: [{ type: String }], // Property IDs
    verificationStatus: { 
      type: String, 
      enum: ["pending", "verified", "rejected"], 
      default: "pending" 
    },
    businessRegistration: { type: String },
    taxNumber: { type: String },
    bankAccount: { type: String }
  },
  
  // Notification preferences
  notifications: {
    email: {
      newListings: { type: Boolean, default: true },
      priceChanges: { type: Boolean, default: true },
      applicationUpdates: { type: Boolean, default: true },
      socialMatches: { type: Boolean, default: true }
    },
    sms: {
      urgentAlerts: { type: Boolean, default: false },
      viewingReminders: { type: Boolean, default: false }
    },
    push: {
      newMatches: { type: Boolean, default: true },
      messages: { type: Boolean, default: true },
      systemUpdates: { type: Boolean, default: false }
    }
  },
  
  // Activity tracking
  activity: {
    lastLogin: { type: Date },
    loginCount: { type: Number, default: 0 },
    searchHistory: [{ type: String }],
    applicationsSent: { type: Number, default: 0 },
    viewingsAttended: { type: Number, default: 0 }
  },

  // GDPR Compliance and Privacy
  gdprConsent: {
    consents: {
      type: Map,
      of: {
        granted: { type: Boolean, required: true },
        timestamp: { type: Date, required: true },
        version: { type: String, default: '1.0' },
        ipAddress: { type: String },
        userAgent: { type: String }
      },
      default: new Map()
    },
    consentHistory: [{
      type: {
        type: String,
        enum: ['data_processing', 'auto_application', 'marketing', 'analytics', 'third_party_sharing'],
        required: true
      },
      granted: { type: Boolean, required: true },
      timestamp: { type: Date, required: true },
      ipAddress: { type: String },
      userAgent: { type: String },
      version: { type: String, default: '1.0' },
      method: { type: String, enum: ['web_form', 'api', 'email', 'phone'], default: 'web_form' }
    }],
    lastUpdated: { type: Date },
    privacyPolicyVersion: { type: String, default: '1.0' },
    dataRetentionConsent: { type: Boolean, default: false },
    marketingConsent: { type: Boolean, default: false }
  },

  // Security and Access Control
  security: {
    lastPasswordChange: { type: Date },
    passwordHistory: [{ 
      hash: { type: String },
      createdAt: { type: Date, default: Date.now }
    }],
    twoFactorEnabled: { type: Boolean, default: false },
    twoFactorSecret: { type: String },
    backupCodes: [{ type: String }],
    loginAttempts: {
      count: { type: Number, default: 0 },
      lastAttempt: { type: Date },
      lockedUntil: { type: Date }
    },
    suspiciousActivity: {
      flagged: { type: Boolean, default: false },
      lastFlagged: { type: Date },
      reason: { type: String },
      resolved: { type: Boolean, default: false }
    },
    sessionTokens: [{
      token: { type: String },
      createdAt: { type: Date, default: Date.now },
      expiresAt: { type: Date },
      ipAddress: { type: String },
      userAgent: { type: String },
      active: { type: Boolean, default: true }
    }]
  },

  createdAt: { type: Date, default: Date.now },
  lastActive: { type: Date, default: Date.now },
});

// Database indexes for performance optimization
userSchema.index({ "profile.userType": 1 });
userSchema.index({ "profile.socialPreferences.isVisible": 1 });
userSchema.index({ "tenantScore.overallScore": -1 });
userSchema.index({ "propertyOwner.isPropertyOwner": 1 });
userSchema.index({ "aiSettings.preferredLanguage": 1 });
userSchema.index({ "profile.socialPreferences.lookingForRoommate": 1 });
userSchema.index({ "tenantScore.verificationLevel": 1 });
userSchema.index({ "emailVerified": 1 });
userSchema.index({ "createdAt": 1 });
userSchema.index({ "lastActive": -1 });
userSchema.index({ "autoApplication.enabled": 1 });
userSchema.index({ "autoApplication.applicationHistory.dailyResetDate": 1 });
userSchema.index({ "autoApplication.profileCompleteness.overall": -1 });
userSchema.index({ "gdprConsent.lastUpdated": -1 });
userSchema.index({ "security.loginAttempts.lockedUntil": 1 }, { sparse: true });
userSchema.index({ "security.suspiciousActivity.flagged": 1 });

// Virtual fields
userSchema.virtual('isProfileComplete').get(function() {
  return !!(this.profile && 
         this.profile.firstName && 
         this.profile.lastName && 
         this.profile.userType && 
         this.profile.userType.length > 0 &&
         this.profile.employment &&
         this.profile.employment.occupation);
});

userSchema.virtual('tenantScoreGrade').get(function() {
  const score = this.tenantScore.overallScore;
  if (score >= 90) return 'A';
  if (score >= 80) return 'B';
  if (score >= 70) return 'C';
  if (score >= 60) return 'D';
  return 'F';
});

userSchema.virtual('fullName').get(function() {
  if (this.profile.firstName && this.profile.lastName) {
    return `${this.profile.firstName} ${this.profile.lastName}`;
  }
  return this.profile.name || '';
});

userSchema.virtual('isEligibleForSocialMatching').get(function() {
  return this.profile && 
         this.profile.socialPreferences && 
         this.profile.socialPreferences.lookingForRoommate && 
         this.profile.socialPreferences.isVisible &&
         this.isProfileComplete;
});

userSchema.virtual('isAutoApplicationReady').get(function() {
  return this.autoApplication && 
         this.autoApplication.profileCompleteness.overall >= 80 &&
         this.autoApplication.requiredDocuments.every(doc => doc.uploaded);
});

userSchema.virtual('canApplyToday').get(function() {
  if (!this.autoApplication || !this.autoApplication.enabled) return false;
  
  const today = new Date();
  const resetDate = this.autoApplication.applicationHistory.dailyResetDate;
  
  // Reset daily count if it's a new day
  if (!resetDate || resetDate.toDateString() !== today.toDateString()) {
    return true;
  }
  
  return this.autoApplication.applicationHistory.dailyApplicationCount < 
         this.autoApplication.settings.maxApplicationsPerDay;
});

userSchema.virtual('autoApplicationSuccessRate').get(function() {
  if (!this.autoApplication || this.autoApplication.applicationHistory.totalApplications === 0) {
    return 0;
  }
  
  return Math.round(
    (this.autoApplication.applicationHistory.successfulApplications / 
     this.autoApplication.applicationHistory.totalApplications) * 100
  );
});

userSchema.virtual('hasGdprConsent').get(function() {
  if (!this.gdprConsent || !this.gdprConsent.consents) {
    return false;
  }
  
  // Check if user has given consent for data processing (minimum required)
  const dataProcessingConsent = this.gdprConsent.consents.get('data_processing');
  return dataProcessingConsent && dataProcessingConsent.granted;
});

userSchema.virtual('hasAutoApplicationConsent').get(function() {
  if (!this.gdprConsent || !this.gdprConsent.consents) {
    return false;
  }
  
  const autoAppConsent = this.gdprConsent.consents.get('auto_application');
  return autoAppConsent && autoAppConsent.granted;
});

userSchema.virtual('isAccountLocked').get(function() {
  if (!this.security || !this.security.loginAttempts.lockedUntil) {
    return false;
  }
  
  return this.security.loginAttempts.lockedUntil > new Date();
});

userSchema.virtual('isSuspiciousActivityFlagged').get(function() {
  return this.security && 
         this.security.suspiciousActivity.flagged && 
         !this.security.suspiciousActivity.resolved;
});

userSchema.virtual('activeSessions').get(function() {
  if (!this.security || !this.security.sessionTokens) {
    return [];
  }
  
  const now = new Date();
  return this.security.sessionTokens.filter(session => 
    session.active && session.expiresAt > now
  );
});

// Ensure virtual fields are serialized
userSchema.set('toJSON', { virtuals: true });
userSchema.set('toObject', { virtuals: true });

// Pre-save middleware
userSchema.pre("save", async function (next) {
  if (!this.isModified("password")) return next();
  this.password = await bcrypt.hash(this.password, 10);
  next();
});

// Update lastActive on save
userSchema.pre("save", function (next) {
  if (this.isNew || this.isModified()) {
    this.lastActive = new Date();
  }
  next();
});

// Validation for age range in social preferences
userSchema.path('profile.socialPreferences.roommateCriteria.ageRange.min').validate(function(value) {
  if (this.profile.socialPreferences.roommateCriteria.ageRange.max) {
    return value <= this.profile.socialPreferences.roommateCriteria.ageRange.max;
  }
  return true;
}, 'Minimum age must be less than or equal to maximum age');

userSchema.path('profile.socialPreferences.roommateCriteria.ageRange.max').validate(function(value) {
  if (this.profile.socialPreferences.roommateCriteria.ageRange.min) {
    return value >= this.profile.socialPreferences.roommateCriteria.ageRange.min;
  }
  return true;
}, 'Maximum age must be greater than or equal to minimum age');

// Security and GDPR Methods

/**
 * Record GDPR consent
 */
userSchema.methods.recordGdprConsent = function(consentType, granted, metadata = {}) {
  if (!this.gdprConsent) {
    this.gdprConsent = {
      consents: new Map(),
      consentHistory: [],
      lastUpdated: new Date(),
      privacyPolicyVersion: '1.0'
    };
  }

  const consentRecord = {
    type: consentType,
    granted,
    timestamp: new Date(),
    ipAddress: metadata.ipAddress,
    userAgent: metadata.userAgent,
    version: metadata.privacyPolicyVersion || '1.0',
    method: metadata.method || 'web_form'
  };

  // Update current consent
  this.gdprConsent.consents.set(consentType, {
    granted,
    timestamp: consentRecord.timestamp,
    version: consentRecord.version,
    ipAddress: metadata.ipAddress,
    userAgent: metadata.userAgent
  });

  // Add to history
  this.gdprConsent.consentHistory.push(consentRecord);
  this.gdprConsent.lastUpdated = new Date();

  return this.save();
};

/**
 * Check if user has specific consent
 */
userSchema.methods.hasConsent = function(consentType) {
  if (!this.gdprConsent || !this.gdprConsent.consents) {
    return false;
  }
  
  const consent = this.gdprConsent.consents.get(consentType);
  return consent && consent.granted;
};

/**
 * Record failed login attempt
 */
userSchema.methods.recordFailedLogin = function(ipAddress) {
  if (!this.security) {
    this.security = {
      loginAttempts: { count: 0 },
      suspiciousActivity: { flagged: false, resolved: false },
      sessionTokens: []
    };
  }

  this.security.loginAttempts.count += 1;
  this.security.loginAttempts.lastAttempt = new Date();

  // Lock account after 5 failed attempts
  if (this.security.loginAttempts.count >= 5) {
    this.security.loginAttempts.lockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
  }

  return this.save();
};

/**
 * Reset login attempts after successful login
 */
userSchema.methods.resetLoginAttempts = function() {
  if (this.security && this.security.loginAttempts) {
    this.security.loginAttempts.count = 0;
    this.security.loginAttempts.lastAttempt = undefined;
    this.security.loginAttempts.lockedUntil = undefined;
  }
  
  return this.save();
};

/**
 * Flag suspicious activity
 */
userSchema.methods.flagSuspiciousActivity = function(reason) {
  if (!this.security) {
    this.security = {
      loginAttempts: { count: 0 },
      suspiciousActivity: { flagged: false, resolved: false },
      sessionTokens: []
    };
  }

  this.security.suspiciousActivity.flagged = true;
  this.security.suspiciousActivity.lastFlagged = new Date();
  this.security.suspiciousActivity.reason = reason;
  this.security.suspiciousActivity.resolved = false;

  return this.save();
};

/**
 * Resolve suspicious activity flag
 */
userSchema.methods.resolveSuspiciousActivity = function() {
  if (this.security && this.security.suspiciousActivity) {
    this.security.suspiciousActivity.resolved = true;
  }
  
  return this.save();
};

/**
 * Add session token
 */
userSchema.methods.addSessionToken = function(token, expiresAt, metadata = {}) {
  if (!this.security) {
    this.security = {
      loginAttempts: { count: 0 },
      suspiciousActivity: { flagged: false, resolved: false },
      sessionTokens: []
    };
  }

  this.security.sessionTokens.push({
    token,
    createdAt: new Date(),
    expiresAt,
    ipAddress: metadata.ipAddress,
    userAgent: metadata.userAgent,
    active: true
  });

  // Keep only last 10 sessions
  if (this.security.sessionTokens.length > 10) {
    this.security.sessionTokens = this.security.sessionTokens.slice(-10);
  }

  return this.save();
};

/**
 * Revoke session token
 */
userSchema.methods.revokeSessionToken = function(token) {
  if (this.security && this.security.sessionTokens) {
    const session = this.security.sessionTokens.find(s => s.token === token);
    if (session) {
      session.active = false;
    }
  }
  
  return this.save();
};

/**
 * Revoke all session tokens
 */
userSchema.methods.revokeAllSessions = function() {
  if (this.security && this.security.sessionTokens) {
    this.security.sessionTokens.forEach(session => {
      session.active = false;
    });
  }
  
  return this.save();
};

/**
 * Check if password was recently used
 */
userSchema.methods.wasPasswordRecentlyUsed = function(passwordHash) {
  if (!this.security || !this.security.passwordHistory) {
    return false;
  }

  // Check last 5 passwords
  const recentPasswords = this.security.passwordHistory.slice(-5);
  return recentPasswords.some(p => p.hash === passwordHash);
};

/**
 * Add password to history
 */
userSchema.methods.addPasswordToHistory = function(passwordHash) {
  if (!this.security) {
    this.security = {
      loginAttempts: { count: 0 },
      suspiciousActivity: { flagged: false, resolved: false },
      sessionTokens: [],
      passwordHistory: []
    };
  }

  if (!this.security.passwordHistory) {
    this.security.passwordHistory = [];
  }

  this.security.passwordHistory.push({
    hash: passwordHash,
    createdAt: new Date()
  });

  // Keep only last 10 passwords
  if (this.security.passwordHistory.length > 10) {
    this.security.passwordHistory = this.security.passwordHistory.slice(-10);
  }

  this.security.lastPasswordChange = new Date();
  
  return this.save();
};

module.exports = mongoose.model("User", userSchema);
