const mongoose = require("mongoose");

const socialMatchSchema = new mongoose.Schema({
  user1: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  user2: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  compatibilityScore: { 
    type: Number, 
    min: 0, 
    max: 100, 
    required: true 
  },
  matchedCriteria: [{
    type: String,
    enum: [
      'age_range', 
      'gender_preference', 
      'occupation', 
      'cleanliness', 
      'noise_level', 
      'social_level', 
      'smoking_tolerance', 
      'pet_tolerance', 
      'guest_policy',
      'location_preference',
      'budget_compatibility'
    ]
  }],
  status: {
    type: String,
    enum: ["pending", "accepted", "rejected", "blocked"],
    default: "pending"
  },
  initiatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
  respondedAt: { 
    type: Date 
  },
  lastInteraction: {
    type: Date,
    default: Date.now
  },
  // Privacy and safety features
  reportedBy: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    reason: { 
      type: String,
      enum: ['inappropriate_behavior', 'fake_profile', 'harassment', 'spam', 'other']
    },
    description: String,
    reportedAt: { type: Date, default: Date.now }
  }],
  blockedBy: [{
    user: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
    blockedAt: { type: Date, default: Date.now }
  }]
});

// Indexes for performance
socialMatchSchema.index({ user1: 1, user2: 1 }, { unique: true });
socialMatchSchema.index({ user1: 1, status: 1 });
socialMatchSchema.index({ user2: 1, status: 1 });
socialMatchSchema.index({ compatibilityScore: -1 });
socialMatchSchema.index({ createdAt: -1 });
socialMatchSchema.index({ lastInteraction: -1 });

// Compound index for efficient matching queries
socialMatchSchema.index({ 
  user1: 1, 
  status: 1, 
  compatibilityScore: -1 
});

// Virtual to get the other user in the match
socialMatchSchema.virtual('getOtherUser').get(function() {
  return function(currentUserId) {
    return this.user1.toString() === currentUserId.toString() ? this.user2 : this.user1;
  };
});

// Virtual to check if match is active
socialMatchSchema.virtual('isActive').get(function() {
  return this.status === 'accepted' || this.status === 'pending';
});

// Virtual to check if match is blocked
socialMatchSchema.virtual('isBlocked').get(function() {
  return this.status === 'blocked' || this.blockedBy.length > 0;
});

// Pre-save middleware to ensure user1 < user2 for consistent ordering
socialMatchSchema.pre('save', function(next) {
  if (this.user1.toString() > this.user2.toString()) {
    [this.user1, this.user2] = [this.user2, this.user1];
  }
  next();
});

// Static method to find matches for a user
socialMatchSchema.statics.findMatchesForUser = function(userId, status = null) {
  const query = {
    $or: [
      { user1: userId },
      { user2: userId }
    ]
  };
  
  if (status) {
    query.status = status;
  }
  
  return this.find(query)
    .populate('user1', 'profile.firstName profile.lastName profile.profilePicture profile.socialPreferences')
    .populate('user2', 'profile.firstName profile.lastName profile.profilePicture profile.socialPreferences')
    .sort({ compatibilityScore: -1, createdAt: -1 });
};

// Static method to check if users are already matched
socialMatchSchema.statics.existsBetweenUsers = function(user1Id, user2Id) {
  const [userId1, userId2] = user1Id.toString() < user2Id.toString() 
    ? [user1Id, user2Id] 
    : [user2Id, user1Id];
    
  return this.findOne({
    user1: userId1,
    user2: userId2
  });
};

// Ensure virtual fields are serialized
socialMatchSchema.set('toJSON', { virtuals: true });
socialMatchSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model("SocialMatch", socialMatchSchema);