/**
 * Property Interfaces and Type Definitions
 * 
 * This file defines the core interfaces and types used throughout the
 * unified property schema system for better code documentation and validation.
 */

/**
 * @typedef {Object} UnifiedProperty
 * @property {string} _id - MongoDB ObjectId as string
 * @property {string} id - Alias for _id for compatibility
 * @property {string} title - Property title
 * @property {string} [description] - Property description
 * @property {string} source - Source website (funda.nl, huurwoningen.nl, pararius.nl)
 * @property {string} url - Property URL
 * @property {string} dateAdded - ISO date string when property was added
 * @property {(string|LocationObject)} location - Location as string or structured object
 * @property {string} propertyType - Type of property (apartment, house, studio, room, etc.)
 * @property {string} [size] - Formatted size string (e.g., "85 m²")
 * @property {number} [area] - Numeric area in square meters
 * @property {(string|number)} [rooms] - Total number of rooms
 * @property {(string|number)} [bedrooms] - Number of bedrooms
 * @property {(string|number)} [bathrooms] - Number of bathrooms
 * @property {string} [year] - Build year as string
 * @property {(string|number)} price - Property price
 * @property {string} [interior] - Interior type (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>eerd)
 * @property {boolean} [furnished] - Whether property is furnished
 * @property {boolean} [pets] - Whether pets are allowed
 * @property {boolean} [smoking] - Whether smoking is allowed
 * @property {boolean} [garden] - Whether property has garden
 * @property {boolean} [balcony] - Whether property has balcony
 * @property {boolean} [parking] - Whether property has parking
 * @property {string} [energyLabel] - Energy efficiency label
 * @property {string[]} [images] - Array of image URLs
 * @property {boolean} [isActive] - Whether property is active
 * @property {string[]} [features] - Array of feature strings
 * @property {number} [deposit] - Security deposit amount
 * @property {number} [utilities] - Utilities cost
 * @property {string} [dateAvailable] - Date when property becomes available
 * @property {ContactInfo} [contactInfo] - Contact information
 * @property {InternalData} [_internal] - Internal processing data
 */

/**
 * @typedef {Object} LocationObject
 * @property {AddressObject} [_unified] - Structured address information
 * @property {string} [_legacy] - Simple location string for backward compatibility
 * @property {Function} [toString] - Function to get formatted address string
 */

/**
 * @typedef {Object} AddressObject
 * @property {Object} address - Address components
 * @property {string} [address.street] - Street name
 * @property {string} [address.houseNumber] - House number
 * @property {string} [address.postalCode] - Postal code
 * @property {string} [address.city] - City name
 * @property {string} [address.province] - Province/state
 * @property {string} [address.country] - Country (default: Netherlands)
 * @property {CoordinatesObject} [coordinates] - Geographic coordinates
 */

/**
 * @typedef {Object} CoordinatesObject
 * @property {number} lat - Latitude
 * @property {number} lng - Longitude
 */

/**
 * @typedef {Object} ContactInfo
 * @property {string} [name] - Contact person name
 * @property {string} [phone] - Phone number
 * @property {string} [email] - Email address
 */

/**
 * @typedef {Object} InternalData
 * @property {SourceMetadata} [sourceMetadata] - Source-specific metadata
 * @property {RawData} [rawData] - Raw scraped data preservation
 * @property {DataQuality} [dataQuality] - Data quality indicators
 */

/**
 * @typedef {Object} SourceMetadata
 * @property {string} website - Source website
 * @property {string} [externalId] - External property ID
 * @property {Date} scrapedAt - When property was scraped
 * @property {Date} lastUpdated - Last update timestamp
 * @property {number} version - Schema version
 */

/**
 * @typedef {Object} RawData
 * @property {Object} original - Original scraped data
 * @property {Object} processed - Intermediate processing data
 * @property {Object} metadata - Processing metadata
 */

/**
 * @typedef {Object} DataQuality
 * @property {number} completeness - Completeness score (0-100)
 * @property {number} accuracy - Accuracy score (0-100)
 * @property {Date} [lastValidated] - Last validation timestamp
 * @property {string[]} validationErrors - Array of validation error messages
 */

/**
 * @typedef {Object} ValidationResult
 * @property {UnifiedProperty} [value] - Validated and normalized property data
 * @property {Object} [error] - Validation error details
 */

/**
 * @typedef {Object} TransformationContext
 * @property {string} source - Source website identifier
 * @property {Object} rawData - Original scraped data
 * @property {Object} mappings - Field mappings configuration
 * @property {Date} timestamp - Transformation timestamp
 */

/**
 * @typedef {Object} FieldMapping
 * @property {string} [path] - Path to source field
 * @property {*} [value] - Static value to use
 * @property {string} [transform] - Transformation function name
 * @property {*} [default] - Default value if source field is missing
 * @property {boolean} [required] - Whether field is required
 */

/**
 * @typedef {Object} MappingConfiguration
 * @property {string} source - Source identifier
 * @property {Object.<string, FieldMapping>} fields - Field mappings
 * @property {Object} [options] - Additional mapping options
 */

/**
 * @typedef {Object} TransformationFunction
 * @property {string} name - Function name
 * @property {Function} fn - Transformation function
 * @property {string} [description] - Function description
 */

/**
 * Property type enumeration
 */
const PropertyTypes = {
  APARTMENT: 'apartment',
  APPARTEMENT: 'appartement', // Dutch
  HOUSE: 'house',
  HUIS: 'huis', // Dutch
  STUDIO: 'studio',
  ROOM: 'room',
  KAMER: 'kamer', // Dutch
  WONING: 'woning' // Dutch generic
};

/**
 * Interior type enumeration
 */
const InteriorTypes = {
  UNFURNISHED: 'Kaal', // Dutch
  SEMI_FURNISHED: 'Gestoffeerd', // Dutch
  FURNISHED: 'Gemeubileerd', // Dutch
  UNFURNISHED_EN: 'unfurnished',
  SEMI_FURNISHED_EN: 'semi-furnished',
  FURNISHED_EN: 'furnished'
};

/**
 * Source website enumeration
 */
const SourceWebsites = {
  FUNDA: 'funda.nl',
  HUURWONINGEN: 'huurwoningen.nl',
  PARARIUS: 'pararius.nl'
};

/**
 * Energy label enumeration
 */
const EnergyLabels = {
  A_PLUS_PLUS_PLUS: 'A+++',
  A_PLUS_PLUS: 'A++',
  A_PLUS: 'A+',
  A: 'A',
  B: 'B',
  C: 'C',
  D: 'D',
  E: 'E',
  F: 'F',
  G: 'G'
};

/**
 * Validation error types
 */
const ValidationErrorTypes = {
  REQUIRED_FIELD_MISSING: 'REQUIRED_FIELD_MISSING',
  INVALID_TYPE: 'INVALID_TYPE',
  INVALID_FORMAT: 'INVALID_FORMAT',
  INVALID_VALUE: 'INVALID_VALUE',
  FIELD_TOO_LONG: 'FIELD_TOO_LONG',
  FIELD_TOO_SHORT: 'FIELD_TOO_SHORT',
  INVALID_URL: 'INVALID_URL',
  INVALID_EMAIL: 'INVALID_EMAIL',
  INVALID_DATE: 'INVALID_DATE'
};

/**
 * Transformation error types
 */
const TransformationErrorTypes = {
  MAPPING_ERROR: 'MAPPING_ERROR',
  TRANSFORMATION_FUNCTION_ERROR: 'TRANSFORMATION_FUNCTION_ERROR',
  SOURCE_FIELD_MISSING: 'SOURCE_FIELD_MISSING',
  INVALID_TRANSFORMATION_CONFIG: 'INVALID_TRANSFORMATION_CONFIG'
};

/**
 * Interface for property transformation services
 */
class IPropertyTransformer {
  /**
   * Transform raw scraped data to unified property format
   * @param {Object} rawData - Raw scraped data
   * @param {string} source - Source identifier
   * @returns {Promise<UnifiedProperty>} Transformed property
   */
  async transform(rawData, source) {
    throw new Error('transform method must be implemented');
  }

  /**
   * Validate transformed property data
   * @param {UnifiedProperty} property - Property to validate
   * @returns {ValidationResult} Validation result
   */
  validate(property) {
    throw new Error('validate method must be implemented');
  }
}

/**
 * Interface for field mapping registry
 */
class IFieldMappingRegistry {
  /**
   * Register field mappings for a source
   * @param {string} source - Source identifier
   * @param {MappingConfiguration} mappings - Field mappings
   */
  registerMapping(source, mappings) {
    throw new Error('registerMapping method must be implemented');
  }

  /**
   * Get field mappings for a source
   * @param {string} source - Source identifier
   * @returns {MappingConfiguration} Field mappings
   */
  getMapping(source) {
    throw new Error('getMapping method must be implemented');
  }

  /**
   * Get all registered mappings
   * @returns {Object.<string, MappingConfiguration>} All mappings
   */
  getAllMappings() {
    throw new Error('getAllMappings method must be implemented');
  }
}

/**
 * Interface for validation engine
 */
class IValidationEngine {
  /**
   * Validate property data
   * @param {UnifiedProperty} property - Property to validate
   * @returns {ValidationResult} Validation result
   */
  validate(property) {
    throw new Error('validate method must be implemented');
  }

  /**
   * Validate specific field
   * @param {string} fieldName - Field name
   * @param {*} value - Field value
   * @returns {ValidationResult} Validation result
   */
  validateField(fieldName, value) {
    throw new Error('validateField method must be implemented');
  }
}

module.exports = {
  // Type definitions are exported as JSDoc comments above
  
  // Enumerations
  PropertyTypes,
  InteriorTypes,
  SourceWebsites,
  EnergyLabels,
  ValidationErrorTypes,
  TransformationErrorTypes,
  
  // Interfaces
  IPropertyTransformer,
  IFieldMappingRegistry,
  IValidationEngine
};