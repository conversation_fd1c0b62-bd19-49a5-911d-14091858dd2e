const mongoose = require("mongoose");
const config = require("./config/config");
const { scrapeFunda, getScrapingMetrics } = require("./services/scraper");

// Main function to run scraper with database connection
async function runScraper() {
  const startTime = Date.now();

  try {
    // Connect to MongoDB first
    console.log("Connecting to MongoDB...");
    await mongoose.connect(config.mongoURI);
    console.log("MongoDB connected successfully");

    // Run the scraper
    console.log("Starting improved Funda scraper...");
    console.log("=".repeat(50));

    const listings = await scrapeFunda();

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    // Log results
    console.log("=".repeat(50));
    console.log(`SCRAPING COMPLETED IN ${duration.toFixed(2)} SECONDS`);
    console.log(
      `Successfully extracted and processed ${listings.length} listings`
    );

    // Get scraping metrics
    const metrics = getScrapingMetrics();
    console.log("\n--- SCRAPING METRICS ---");
    console.log(`Total scrapes: ${metrics.totalScrapes}`);
    console.log(`Successful scrapes: ${metrics.successfulScrapes}`);
    console.log(`Failed scrapes: ${metrics.failedScrapes}`);
    console.log(`Success rate: ${metrics.successRate}%`);
    console.log(
      `Average listings per scrape: ${metrics.averageListingsPerScrape}`
    );

    // Display detailed listing information
    if (listings.length > 0) {
      console.log("\n--- DETAILED LISTINGS ---");
      listings.forEach((listing, i) => {
        console.log(`\n${i + 1}. ${listing.title}`);
        console.log(`   Price: ${listing.price}`);
        console.log(`   Location: ${listing.location}`);
        console.log(`   Property Type: ${listing.propertyType}`);
        if (listing.size) console.log(`   Size: ${listing.size}`);
        if (listing.bedrooms) console.log(`   Bedrooms: ${listing.bedrooms}`);
        console.log(`   URL: ${listing.url}`);
        console.log(`   Date Added: ${listing.dateAdded.toISOString()}`);
      });

      // Analyze price extraction success
      const pricesFound = listings.filter(
        (l) => l.price !== "Prijs op aanvraag"
      ).length;
      const priceSuccessRate = ((pricesFound / listings.length) * 100).toFixed(
        1
      );

      console.log("\n--- PRICE EXTRACTION ANALYSIS ---");
      console.log(
        `Listings with actual prices: ${pricesFound}/${listings.length} (${priceSuccessRate}%)`
      );
      console.log(
        `Listings with "Prijs op aanvraag": ${listings.length - pricesFound}`
      );

      // Show unique prices found
      const uniquePrices = [...new Set(listings.map((l) => l.price))];
      console.log(`Unique price formats found: ${uniquePrices.length}`);
      uniquePrices.forEach((price) => {
        const count = listings.filter((l) => l.price === price).length;
        console.log(`  "${price}": ${count} listings`);
      });
    } else {
      console.log("\n❌ No listings found. This might indicate:");
      console.log("   - Website structure has changed");
      console.log("   - Anti-bot measures are blocking the scraper");
      console.log("   - Network connectivity issues");
      console.log("   - The search URL is not returning results");
    }
  } catch (error) {
    console.error("\n❌ Error running scraper:", error);
    console.error("Stack trace:", error.stack);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log("\nMongoDB connection closed");
    process.exit(0);
  }
}

// Run the scraper
runScraper();
