const mongoose = require('mongoose');
const config = require('./config/config');
const { scrapeFunda, getScrapingMetrics } = require('./services/scraper');

// Performance benchmark for the Funda scraper
async function benchmarkScraper() {
  console.log('🚀 FUNDA SCRAPER PERFORMANCE BENCHMARK');
  console.log('='.repeat(60));
  
  try {
    // Connect to MongoDB
    console.log('📊 Connecting to MongoDB...');
    await mongoose.connect(config.mongoURI);
    console.log('✅ MongoDB connected successfully');

    // Run multiple scraping sessions to get average performance
    const benchmarkRuns = 3;
    const results = [];
    
    for (let i = 1; i <= benchmarkRuns; i++) {
      console.log(`\n🔄 Running benchmark ${i}/${benchmarkRuns}...`);
      console.log('-'.repeat(40));
      
      const startTime = Date.now();
      const startMemory = process.memoryUsage();
      
      try {
        const listings = await scrapeFunda();
        
        const endTime = Date.now();
        const endMemory = process.memoryUsage();
        const duration = (endTime - startTime) / 1000;
        
        const memoryUsed = {
          rss: (endMemory.rss - startMemory.rss) / 1024 / 1024, // MB
          heapUsed: (endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024, // MB
          external: (endMemory.external - startMemory.external) / 1024 / 1024 // MB
        };
        
        const result = {
          run: i,
          duration: duration,
          listingsFound: listings.length,
          listingsPerSecond: (listings.length / duration).toFixed(2),
          memoryUsed: memoryUsed,
          success: true
        };
        
        results.push(result);
        
        console.log(`✅ Run ${i} completed:`);
        console.log(`   Duration: ${duration.toFixed(2)}s`);
        console.log(`   Listings found: ${listings.length}`);
        console.log(`   Rate: ${result.listingsPerSecond} listings/second`);
        console.log(`   Memory used: ${memoryUsed.rss.toFixed(2)} MB RSS`);
        
        // Analyze price extraction success
        const pricesFound = listings.filter(l => l.price !== "Prijs op aanvraag").length;
        const priceSuccessRate = ((pricesFound / listings.length) * 100).toFixed(1);
        console.log(`   Price extraction: ${pricesFound}/${listings.length} (${priceSuccessRate}%)`);
        
      } catch (error) {
        console.error(`❌ Run ${i} failed:`, error.message);
        results.push({
          run: i,
          duration: 0,
          listingsFound: 0,
          listingsPerSecond: 0,
          memoryUsed: { rss: 0, heapUsed: 0, external: 0 },
          success: false,
          error: error.message
        });
      }
      
      // Wait between runs to avoid rate limiting
      if (i < benchmarkRuns) {
        console.log('⏳ Waiting 30 seconds before next run...');
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
    }
    
    // Calculate and display benchmark results
    console.log('\n📈 BENCHMARK RESULTS');
    console.log('='.repeat(60));
    
    const successfulRuns = results.filter(r => r.success);
    
    if (successfulRuns.length === 0) {
      console.log('❌ No successful runs to analyze');
      return;
    }
    
    const avgDuration = successfulRuns.reduce((sum, r) => sum + r.duration, 0) / successfulRuns.length;
    const avgListings = successfulRuns.reduce((sum, r) => sum + r.listingsFound, 0) / successfulRuns.length;
    const avgRate = successfulRuns.reduce((sum, r) => sum + parseFloat(r.listingsPerSecond), 0) / successfulRuns.length;
    const avgMemory = successfulRuns.reduce((sum, r) => sum + r.memoryUsed.rss, 0) / successfulRuns.length;
    
    const minDuration = Math.min(...successfulRuns.map(r => r.duration));
    const maxDuration = Math.max(...successfulRuns.map(r => r.duration));
    const minListings = Math.min(...successfulRuns.map(r => r.listingsFound));
    const maxListings = Math.max(...successfulRuns.map(r => r.listingsFound));
    
    console.log(`📊 Performance Summary (${successfulRuns.length}/${benchmarkRuns} successful runs):`);
    console.log(`   Average duration: ${avgDuration.toFixed(2)}s (min: ${minDuration.toFixed(2)}s, max: ${maxDuration.toFixed(2)}s)`);
    console.log(`   Average listings: ${avgListings.toFixed(1)} (min: ${minListings}, max: ${maxListings})`);
    console.log(`   Average rate: ${avgRate.toFixed(2)} listings/second`);
    console.log(`   Average memory: ${avgMemory.toFixed(2)} MB RSS`);
    
    // Performance ratings
    console.log('\n🏆 PERFORMANCE RATINGS:');
    
    if (avgDuration < 60) {
      console.log('   ⚡ Speed: EXCELLENT (< 1 minute)');
    } else if (avgDuration < 120) {
      console.log('   🚀 Speed: GOOD (1-2 minutes)');
    } else if (avgDuration < 300) {
      console.log('   ⏱️  Speed: ACCEPTABLE (2-5 minutes)');
    } else {
      console.log('   🐌 Speed: SLOW (> 5 minutes)');
    }
    
    if (avgListings >= 10) {
      console.log('   📈 Listings: EXCELLENT (≥ 10 listings)');
    } else if (avgListings >= 5) {
      console.log('   📊 Listings: GOOD (5-9 listings)');
    } else if (avgListings >= 1) {
      console.log('   📉 Listings: LOW (1-4 listings)');
    } else {
      console.log('   ❌ Listings: NONE (0 listings)');
    }
    
    if (avgMemory < 100) {
      console.log('   💚 Memory: EFFICIENT (< 100 MB)');
    } else if (avgMemory < 200) {
      console.log('   💛 Memory: MODERATE (100-200 MB)');
    } else {
      console.log('   🔴 Memory: HIGH (> 200 MB)');
    }
    
    // Get overall scraping metrics
    const metrics = getScrapingMetrics();
    console.log('\n📋 OVERALL SCRAPING METRICS:');
    console.log(`   Total scrapes: ${metrics.totalScrapes}`);
    console.log(`   Success rate: ${metrics.successRate}%`);
    console.log(`   Failed scrapes: ${metrics.failedScrapes}`);
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    if (avgDuration > 120) {
      console.log('   • Consider optimizing wait times and page load strategies');
    }
    if (avgListings < 5) {
      console.log('   • Check if website structure has changed');
      console.log('   • Verify search URL is returning expected results');
    }
    if (avgMemory > 150) {
      console.log('   • Consider implementing better memory management');
      console.log('   • Close browser pages more aggressively');
    }
    if (successfulRuns.length < benchmarkRuns) {
      console.log('   • Investigate failed runs and improve error handling');
    }
    
    console.log('\n✅ Benchmark completed successfully!');
    
  } catch (error) {
    console.error('❌ Benchmark failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 MongoDB connection closed');
    process.exit(0);
  }
}

// Run the benchmark
benchmarkScraper().catch(console.error);
