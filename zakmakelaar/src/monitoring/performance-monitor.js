const { getScrapingMetrics } = require('../services/scraper');
const fs = require('fs').promises;
const path = require('path');

class PerformanceMonitor {
  constructor() {
    this.logFile = path.join(__dirname, '../../logs/performance.log');
    this.alertThresholds = {
      successRate: 80, // Alert if below 80%
      avgScrapingTime: 300000, // Alert if above 5 minutes
      consecutiveFailures: 3,
      noActivityMinutes: 60
    };
    this.isMonitoring = false;
    this.monitoringInterval = null;
  }

  async startMonitoring(intervalMinutes = 5) {
    if (this.isMonitoring) {
      console.log('Performance monitoring is already running');
      return;
    }

    this.isMonitoring = true;
    console.log(`🔍 Starting performance monitoring (interval: ${intervalMinutes} minutes)`);

    // Initial check
    await this.performCheck();

    // Set up recurring checks
    this.monitoringInterval = setInterval(async () => {
      await this.performCheck();
    }, intervalMinutes * 60 * 1000);
  }

  stopMonitoring() {
    if (!this.isMonitoring) {
      console.log('Performance monitoring is not running');
      return;
    }

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    console.log('🛑 Performance monitoring stopped');
  }

  async performCheck() {
    try {
      const metrics = getScrapingMetrics();
      const timestamp = new Date().toISOString();
      
      // Analyze metrics
      const analysis = this.analyzeMetrics(metrics);
      
      // Log performance data
      await this.logPerformanceData(timestamp, metrics, analysis);
      
      // Check for alerts
      const alerts = this.checkAlerts(metrics, analysis);
      
      if (alerts.length > 0) {
        await this.handleAlerts(alerts, timestamp);
      }
      
      // Log summary
      console.log(`📊 Performance check completed at ${timestamp}`);
      console.log(`   Status: ${analysis.overallStatus}`);
      console.log(`   Success Rate: ${metrics.successRate}`);
      console.log(`   Alerts: ${alerts.length}`);
      
    } catch (error) {
      console.error('Error during performance check:', error);
    }
  }

  analyzeMetrics(metrics) {
    const successRate = parseFloat(metrics.successRate);
    const avgScrapingTime = metrics.averageScrapingTime || 0;
    
    // Determine overall status
    let overallStatus = 'healthy';
    if (successRate < 50) {
      overallStatus = 'critical';
    } else if (successRate < 80 || avgScrapingTime > this.alertThresholds.avgScrapingTime) {
      overallStatus = 'degraded';
    }

    // Calculate performance scores
    const performanceScore = this.calculatePerformanceScore(metrics);
    
    // Analyze trends (if we have historical data)
    const trends = this.analyzeTrends(metrics);

    return {
      overallStatus,
      performanceScore,
      trends,
      recommendations: this.generateRecommendations(metrics)
    };
  }

  calculatePerformanceScore(metrics) {
    const successRate = parseFloat(metrics.successRate);
    const avgTime = metrics.averageScrapingTime || 0;
    const listingsPerScrape = metrics.totalScrapes > 0 
      ? metrics.totalListingsFound / metrics.totalScrapes 
      : 0;

    // Weighted scoring
    const successScore = Math.min(successRate, 100); // 0-100
    const timeScore = Math.max(0, 100 - (avgTime / 1000 / 60)); // Penalty for longer times
    const productivityScore = Math.min(listingsPerScrape * 10, 100); // 0-100

    const overallScore = (successScore * 0.5 + timeScore * 0.3 + productivityScore * 0.2);
    
    return {
      overall: Math.round(overallScore),
      success: Math.round(successScore),
      speed: Math.round(timeScore),
      productivity: Math.round(productivityScore)
    };
  }

  analyzeTrends(metrics) {
    // This would analyze historical data to identify trends
    // For now, return basic trend indicators
    return {
      successRateTrend: 'stable', // 'improving', 'declining', 'stable'
      performanceTrend: 'stable',
      errorTrend: 'stable'
    };
  }

  generateRecommendations(metrics) {
    const recommendations = [];
    const successRate = parseFloat(metrics.successRate);
    const avgTime = metrics.averageScrapingTime || 0;

    if (successRate < 80) {
      recommendations.push({
        priority: 'high',
        category: 'reliability',
        message: 'Success rate is below optimal. Review error logs and improve error handling.'
      });
    }

    if (avgTime > 180000) { // 3 minutes
      recommendations.push({
        priority: 'medium',
        category: 'performance',
        message: 'Average scraping time is high. Consider optimizing wait times and selectors.'
      });
    }

    const errorTypes = Object.keys(metrics.errorsByType || {});
    if (errorTypes.length > 3) {
      recommendations.push({
        priority: 'medium',
        category: 'stability',
        message: 'Multiple error types detected. Review and consolidate error handling.'
      });
    }

    if (metrics.totalScrapes > 0 && metrics.totalListingsFound / metrics.totalScrapes < 5) {
      recommendations.push({
        priority: 'high',
        category: 'effectiveness',
        message: 'Low listings per scrape. Check if website structure has changed.'
      });
    }

    return recommendations;
  }

  checkAlerts(metrics, analysis) {
    const alerts = [];
    const successRate = parseFloat(metrics.successRate);
    const avgTime = metrics.averageScrapingTime || 0;

    // Success rate alert
    if (successRate < this.alertThresholds.successRate) {
      alerts.push({
        level: successRate < 50 ? 'critical' : 'warning',
        type: 'success_rate',
        message: `Success rate (${successRate}%) is below threshold (${this.alertThresholds.successRate}%)`,
        value: successRate,
        threshold: this.alertThresholds.successRate
      });
    }

    // Performance alert
    if (avgTime > this.alertThresholds.avgScrapingTime) {
      alerts.push({
        level: 'warning',
        type: 'performance',
        message: `Average scraping time (${(avgTime/1000/60).toFixed(1)}min) exceeds threshold (${this.alertThresholds.avgScrapingTime/1000/60}min)`,
        value: avgTime,
        threshold: this.alertThresholds.avgScrapingTime
      });
    }

    // Activity alert
    if (metrics.lastScrapeTime) {
      const timeSinceLastScrape = Date.now() - new Date(metrics.lastScrapeTime).getTime();
      const minutesSinceLastScrape = timeSinceLastScrape / 1000 / 60;
      
      if (minutesSinceLastScrape > this.alertThresholds.noActivityMinutes) {
        alerts.push({
          level: 'warning',
          type: 'activity',
          message: `No scraping activity for ${Math.floor(minutesSinceLastScrape)} minutes`,
          value: minutesSinceLastScrape,
          threshold: this.alertThresholds.noActivityMinutes
        });
      }
    }

    return alerts;
  }

  async handleAlerts(alerts, timestamp) {
    console.log(`🚨 ${alerts.length} alert(s) detected at ${timestamp}:`);
    
    alerts.forEach(alert => {
      const emoji = alert.level === 'critical' ? '🔴' : '🟡';
      console.log(`   ${emoji} ${alert.level.toUpperCase()}: ${alert.message}`);
    });

    // Log alerts to file
    const alertLog = {
      timestamp,
      alerts,
      count: alerts.length,
      criticalCount: alerts.filter(a => a.level === 'critical').length
    };

    await this.logAlerts(alertLog);
  }

  async logPerformanceData(timestamp, metrics, analysis) {
    const logEntry = {
      timestamp,
      metrics: {
        totalScrapes: metrics.totalScrapes,
        successfulScrapes: metrics.successfulScrapes,
        failedScrapes: metrics.failedScrapes,
        successRate: metrics.successRate,
        averageScrapingTime: metrics.averageScrapingTimeFormatted,
        totalListingsFound: metrics.totalListingsFound,
        errorsByType: metrics.errorsByType
      },
      analysis: {
        overallStatus: analysis.overallStatus,
        performanceScore: analysis.performanceScore,
        recommendations: analysis.recommendations.length
      }
    };

    try {
      const logLine = JSON.stringify(logEntry) + '\n';
      await fs.appendFile(this.logFile, logLine);
    } catch (error) {
      console.error('Error writing to performance log:', error);
    }
  }

  async logAlerts(alertLog) {
    const alertLogFile = path.join(__dirname, '../../logs/alerts.log');
    
    try {
      const logLine = JSON.stringify(alertLog) + '\n';
      await fs.appendFile(alertLogFile, logLine);
    } catch (error) {
      console.error('Error writing to alerts log:', error);
    }
  }

  async getPerformanceReport(hours = 24) {
    try {
      const logContent = await fs.readFile(this.logFile, 'utf8');
      const lines = logContent.trim().split('\n').filter(line => line);
      
      const cutoffTime = Date.now() - (hours * 60 * 60 * 1000);
      const recentEntries = lines
        .map(line => JSON.parse(line))
        .filter(entry => new Date(entry.timestamp).getTime() > cutoffTime);

      return {
        period: `${hours} hours`,
        entries: recentEntries.length,
        summary: this.summarizePerformanceData(recentEntries)
      };
    } catch (error) {
      console.error('Error reading performance log:', error);
      return null;
    }
  }

  summarizePerformanceData(entries) {
    if (entries.length === 0) return null;

    const successRates = entries.map(e => parseFloat(e.metrics.successRate));
    const avgSuccessRate = successRates.reduce((sum, rate) => sum + rate, 0) / successRates.length;

    const statusCounts = entries.reduce((counts, entry) => {
      const status = entry.analysis.overallStatus;
      counts[status] = (counts[status] || 0) + 1;
      return counts;
    }, {});

    return {
      averageSuccessRate: avgSuccessRate.toFixed(2) + '%',
      statusDistribution: statusCounts,
      totalRecommendations: entries.reduce((sum, e) => sum + e.analysis.recommendations, 0)
    };
  }
}

module.exports = PerformanceMonitor;
