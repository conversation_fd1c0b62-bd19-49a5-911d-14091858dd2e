const Document = require('../models/Document');
const User = require('../models/User');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const multer = require('multer');
const { loggers } = require('./logger');

/**
 * DocumentVaultService - Secure file upload and management service
 * 
 * This service provides:
 * - Secure file upload with encryption
 * - Document type validation and metadata extraction
 * - Access control and audit logging
 * - Document verification workflow for admin users
 * - Integration with existing auth middleware
 */
class DocumentVaultService {
  constructor() {
    this.UPLOAD_DIR = path.join(__dirname, '../../uploads/documents');
    this.THUMBNAIL_DIR = path.join(__dirname, '../../uploads/thumbnails');
    this.MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    this.ALLOWED_MIME_TYPES = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];
    this.ENCRYPTION_ALGORITHM = 'aes-256-gcm';
    
    // Initialize upload directories
    this._initializeDirectories();
  }

  /**
   * Initialize upload directories
   * @private
   */
  async _initializeDirectories() {
    try {
      await fs.mkdir(this.UPLOAD_DIR, { recursive: true });
      await fs.mkdir(this.THUMBNAIL_DIR, { recursive: true });
      loggers.app.info('Document vault directories initialized');
    } catch (error) {
      loggers.app.error('Error initializing document vault directories:', error);
    }
  }

  /**
   * Configure multer for document uploads
   * @returns {multer.Multer} Configured multer instance
   */
  getUploadMiddleware() {
    const storage = multer.diskStorage({
      destination: async (req, file, cb) => {
        try {
          await fs.mkdir(this.UPLOAD_DIR, { recursive: true });
          cb(null, this.UPLOAD_DIR);
        } catch (error) {
          cb(error);
        }
      },
      filename: (req, file, cb) => {
        // Generate unique filename with timestamp and random string
        const uniqueSuffix = Date.now() + '-' + crypto.randomBytes(6).toString('hex');
        const extension = path.extname(file.originalname);
        cb(null, `${req.user._id}-${uniqueSuffix}${extension}`);
      }
    });

    const fileFilter = (req, file, cb) => {
      if (this.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error(`File type ${file.mimetype} not allowed`), false);
      }
    };

    return multer({
      storage: storage,
      limits: {
        fileSize: this.MAX_FILE_SIZE,
        files: 5 // Maximum 5 files per upload
      },
      fileFilter: fileFilter
    });
  }

  /**
   * Upload and encrypt a document
   * @param {string} userId - User ID uploading the document
   * @param {Object} file - Multer file object
   * @param {string} type - Document type
   * @param {Object} options - Additional options (expiryDate, etc.)
   * @returns {Promise<Object>} Created document record
   */
  async uploadDocument(userId, file, type, options = {}) {
    try {
      loggers.app.info(`Uploading document for user ${userId}, type: ${type}`);

      // Validate file
      this._validateFile(file);

      // Validate document type
      this._validateDocumentType(type);

      // Generate encryption key
      const encryptionKey = crypto.randomBytes(32);
      
      // Encrypt file
      const encryptedPath = await this._encryptFile(file.path, encryptionKey);
      
      // Extract metadata
      const metadata = await this._extractMetadata(file);
      
      // Generate thumbnail if it's an image
      let thumbnailPath = null;
      if (file.mimetype.startsWith('image/')) {
        thumbnailPath = await this._generateThumbnail(file.path);
      }

      // Create document record
      const document = new Document({
        userId,
        filename: file.filename,
        originalName: file.originalname,
        type,
        size: file.size,
        mimeType: file.mimetype,
        encryptedPath,
        thumbnailPath,
        encryptionKey: encryptionKey.toString('hex'),
        metadata,
        expiryDate: options.expiryDate ? new Date(options.expiryDate) : null
      });

      await document.save();

      // Clean up original unencrypted file
      await fs.unlink(file.path);

      // Update user's document references
      await this._updateUserDocuments(userId, document._id, type);

      // Log the upload
      await document.logAccess(userId, 'upload', options.ipAddress);

      loggers.app.info(`Document uploaded successfully: ${document._id}`);
      
      return {
        id: document._id,
        filename: document.originalName,
        type: document.type,
        size: document.size,
        humanReadableSize: document.humanReadableSize,
        verified: document.verified,
        createdAt: document.createdAt,
        expiryDate: document.expiryDate,
        isExpired: document.isExpired
      };

    } catch (error) {
      // Clean up file if upload failed
      if (file && file.path) {
        try {
          await fs.unlink(file.path);
        } catch (cleanupError) {
          loggers.app.error('Error cleaning up failed upload:', cleanupError);
        }
      }
      
      loggers.app.error(`Error uploading document for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get documents for a user
   * @param {string} userId - User ID
   * @param {string} type - Optional document type filter
   * @returns {Promise<Array>} Array of user documents
   */
  async getDocuments(userId, type = null) {
    try {
      loggers.app.info(`Getting documents for user ${userId}, type: ${type || 'all'}`);

      const documents = await Document.findByUserAndType(userId, type);
      
      return documents.map(doc => ({
        id: doc._id,
        filename: doc.originalName,
        type: doc.type,
        size: doc.size,
        humanReadableSize: doc.humanReadableSize,
        verified: doc.verified,
        verifiedAt: doc.verifiedAt,
        createdAt: doc.createdAt,
        expiryDate: doc.expiryDate,
        isExpired: doc.isExpired,
        metadata: {
          pageCount: doc.metadata.pageCount,
          language: doc.metadata.language,
          securityLevel: doc.metadata.securityLevel
        }
      }));

    } catch (error) {
      loggers.app.error(`Error getting documents for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Download a document (decrypt and serve)
   * @param {string} documentId - Document ID
   * @param {string} userId - User requesting the document
   * @param {string} userRole - User role for access control
   * @param {string} ipAddress - IP address for logging
   * @returns {Promise<Object>} Decrypted file data and metadata
   */
  async downloadDocument(documentId, userId, userRole, ipAddress) {
    try {
      loggers.app.info(`Download request for document ${documentId} by user ${userId}`);

      const document = await Document.findById(documentId);
      if (!document) {
        throw new Error('Document not found');
      }

      // Check access permissions
      if (!document.canAccess(userId, userRole)) {
        throw new Error('Access denied');
      }

      // Check if document is expired
      if (document.isExpired) {
        throw new Error('Document has expired');
      }

      // Decrypt file
      const decryptedData = await this._decryptFile(
        document.encryptedPath, 
        Buffer.from(document.encryptionKey, 'hex')
      );

      // Log access
      await document.logAccess(userId, 'download', ipAddress);

      return {
        data: decryptedData,
        filename: document.originalName,
        mimeType: document.mimeType,
        size: document.size
      };

    } catch (error) {
      loggers.app.error(`Error downloading document ${documentId}:`, error);
      throw error;
    }
  }

  /**
   * Delete a document
   * @param {string} documentId - Document ID
   * @param {string} userId - User requesting deletion
   * @param {string} userRole - User role for access control
   * @param {string} ipAddress - IP address for logging
   * @returns {Promise<boolean>} Success status
   */
  async deleteDocument(documentId, userId, userRole, ipAddress) {
    try {
      loggers.app.info(`Delete request for document ${documentId} by user ${userId}`);

      const document = await Document.findById(documentId);
      if (!document) {
        throw new Error('Document not found');
      }

      // Check access permissions
      if (!document.canAccess(userId, userRole)) {
        throw new Error('Access denied');
      }

      // Log deletion before removing
      await document.logAccess(userId, 'delete', ipAddress);

      // Remove encrypted file
      try {
        await fs.unlink(document.encryptedPath);
      } catch (error) {
        logger.warn(`Could not delete encrypted file: ${error.message}`);
      }

      // Remove thumbnail if exists
      if (document.thumbnailPath) {
        try {
          await fs.unlink(document.thumbnailPath);
        } catch (error) {
          logger.warn(`Could not delete thumbnail: ${error.message}`);
        }
      }

      // Remove document record
      await Document.findByIdAndDelete(documentId);

      // Update user's document references
      await this._removeUserDocumentReference(document.userId, documentId, document.type);

      loggers.app.info(`Document ${documentId} deleted successfully`);
      return true;

    } catch (error) {
      loggers.app.error(`Error deleting document ${documentId}:`, error);
      throw error;
    }
  }

  /**
   * Verify a document (admin only)
   * @param {string} documentId - Document ID
   * @param {string} adminId - Admin user ID
   * @param {boolean} verified - Verification status
   * @param {string} notes - Optional verification notes
   * @returns {Promise<Object>} Updated document
   */
  async verifyDocument(documentId, adminId, verified, notes = '') {
    try {
      loggers.app.info(`Verifying document ${documentId} by admin ${adminId}`);

      const document = await Document.findById(documentId);
      if (!document) {
        throw new Error('Document not found');
      }

      // Update verification status
      document.verified = verified;
      document.verifiedBy = adminId;
      document.verifiedAt = new Date();
      
      if (notes) {
        document.metadata.verificationNotes = notes;
      }

      await document.save();

      // Log verification
      await document.logAccess(adminId, 'verify', null);

      // Update user's document verification status
      await this._updateUserDocumentVerification(document.userId, documentId, verified);

      loggers.app.info(`Document ${documentId} verification status updated to: ${verified}`);
      
      return {
        id: document._id,
        verified: document.verified,
        verifiedBy: document.verifiedBy,
        verifiedAt: document.verifiedAt
      };

    } catch (error) {
      loggers.app.error(`Error verifying document ${documentId}:`, error);
      throw error;
    }
  }

  /**
   * Generate document report for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Document report
   */
  async generateDocumentReport(userId) {
    try {
      loggers.app.info(`Generating document report for user ${userId}`);

      const documents = await Document.find({ userId }).sort({ createdAt: -1 });
      
      const report = {
        userId,
        generatedAt: new Date(),
        totalDocuments: documents.length,
        verifiedDocuments: documents.filter(doc => doc.verified).length,
        expiredDocuments: documents.filter(doc => doc.isExpired).length,
        documentsByType: {},
        totalSize: 0,
        securitySummary: {
          high: 0,
          medium: 0,
          low: 0
        }
      };

      // Analyze documents
      documents.forEach(doc => {
        // Count by type
        if (!report.documentsByType[doc.type]) {
          report.documentsByType[doc.type] = {
            count: 0,
            verified: 0,
            totalSize: 0
          };
        }
        report.documentsByType[doc.type].count++;
        if (doc.verified) report.documentsByType[doc.type].verified++;
        report.documentsByType[doc.type].totalSize += doc.size;

        // Total size
        report.totalSize += doc.size;

        // Security levels
        const securityLevel = doc.metadata.securityLevel || 'medium';
        report.securitySummary[securityLevel]++;
      });

      // Calculate verification percentage
      report.verificationPercentage = report.totalDocuments > 0 
        ? Math.round((report.verifiedDocuments / report.totalDocuments) * 100)
        : 0;

      return report;

    } catch (error) {
      loggers.app.error(`Error generating document report for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get unverified documents for admin review
   * @param {number} limit - Maximum number of documents to return
   * @returns {Promise<Array>} Array of unverified documents
   */
  async getUnverifiedDocuments(limit = 50) {
    try {
      const documents = await Document.findUnverified()
        .limit(limit)
        .sort({ createdAt: -1 });

      return documents.map(doc => ({
        id: doc._id,
        userId: doc.userId._id,
        userEmail: doc.userId.email,
        userName: doc.userId.profile?.firstName && doc.userId.profile?.lastName 
          ? `${doc.userId.profile.firstName} ${doc.userId.profile.lastName}`
          : doc.userId.email,
        filename: doc.originalName,
        type: doc.type,
        size: doc.humanReadableSize,
        uploadedAt: doc.createdAt,
        metadata: {
          securityLevel: doc.metadata.securityLevel,
          containsPII: doc.metadata.containsPII
        }
      }));

    } catch (error) {
      loggers.app.error('Error getting unverified documents:', error);
      throw error;
    }
  }

  // Private helper methods

  /**
   * Validate uploaded file
   * @private
   */
  _validateFile(file) {
    if (!file) {
      throw new Error('No file provided');
    }

    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`File size exceeds maximum allowed size of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`);
    }

    if (!this.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
      throw new Error(`File type ${file.mimetype} is not allowed`);
    }
  }

  /**
   * Validate document type
   * @private
   */
  _validateDocumentType(type) {
    const validTypes = ["income_proof", "employment_contract", "bank_statement", "id_document", "rental_reference", "other"];
    if (!validTypes.includes(type)) {
      throw new Error(`Invalid document type: ${type}`);
    }
  }

  /**
   * Encrypt file
   * @private
   */
  async _encryptFile(filePath, encryptionKey) {
    try {
      const data = await fs.readFile(filePath);
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipherGCM(this.ENCRYPTION_ALGORITHM, encryptionKey, iv);
      
      const encrypted = Buffer.concat([cipher.update(data), cipher.final()]);
      const authTag = cipher.getAuthTag();
      
      const encryptedPath = filePath + '.enc';
      const encryptedData = Buffer.concat([iv, authTag, encrypted]);
      
      await fs.writeFile(encryptedPath, encryptedData);
      
      return encryptedPath;
    } catch (error) {
      loggers.app.error('Error encrypting file:', error);
      throw new Error('File encryption failed');
    }
  }

  /**
   * Decrypt file
   * @private
   */
  async _decryptFile(encryptedPath, encryptionKey) {
    try {
      const encryptedData = await fs.readFile(encryptedPath);
      
      const iv = encryptedData.slice(0, 16);
      const authTag = encryptedData.slice(16, 32);
      const encrypted = encryptedData.slice(32);
      
      const decipher = crypto.createDecipherGCM(this.ENCRYPTION_ALGORITHM, encryptionKey, iv);
      decipher.setAuthTag(authTag);
      
      const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);
      
      return decrypted;
    } catch (error) {
      loggers.app.error('Error decrypting file:', error);
      throw new Error('File decryption failed');
    }
  }

  /**
   * Extract metadata from file
   * @private
   */
  async _extractMetadata(file) {
    const metadata = {
      extractedText: null,
      pageCount: null,
      language: null,
      containsPII: false,
      securityLevel: 'medium'
    };

    // Basic metadata extraction based on file type
    if (file.mimetype === 'application/pdf') {
      // For PDF files, we could use a PDF parser here
      metadata.securityLevel = 'high'; // PDFs often contain sensitive info
    } else if (file.mimetype.startsWith('image/')) {
      metadata.securityLevel = 'medium';
    } else if (file.mimetype === 'text/plain') {
      // For text files, we could analyze content
      metadata.securityLevel = 'low';
    }

    // Simple PII detection based on filename
    const piiKeywords = ['passport', 'id', 'ssn', 'bank', 'salary', 'income'];
    const filename = file.originalname.toLowerCase();
    metadata.containsPII = piiKeywords.some(keyword => filename.includes(keyword));

    return metadata;
  }

  /**
   * Generate thumbnail for image files
   * @private
   */
  async _generateThumbnail(filePath) {
    // For now, return null - thumbnail generation would require image processing library
    // In a full implementation, you'd use sharp or similar library
    return null;
  }

  /**
   * Update user's document references
   * @private
   */
  async _updateUserDocuments(userId, documentId, type) {
    try {
      await User.findByIdAndUpdate(userId, {
        $push: {
          documents: {
            id: documentId.toString(),
            type: type,
            filename: '',
            uploadDate: new Date(),
            verified: false
          }
        }
      });
    } catch (error) {
      loggers.app.error('Error updating user documents:', error);
    }
  }

  /**
   * Remove user document reference
   * @private
   */
  async _removeUserDocumentReference(userId, documentId, type) {
    try {
      await User.findByIdAndUpdate(userId, {
        $pull: {
          documents: { id: documentId.toString() }
        }
      });
    } catch (error) {
      loggers.app.error('Error removing user document reference:', error);
    }
  }

  /**
   * Update user document verification status
   * @private
   */
  async _updateUserDocumentVerification(userId, documentId, verified) {
    try {
      await User.findOneAndUpdate(
        { _id: userId, 'documents.id': documentId.toString() },
        { $set: { 'documents.$.verified': verified } }
      );
    } catch (error) {
      loggers.app.error('Error updating user document verification:', error);
    }
  }
}

module.exports = new DocumentVaultService();