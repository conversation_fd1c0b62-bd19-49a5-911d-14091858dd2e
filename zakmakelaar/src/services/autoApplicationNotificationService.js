const User = require('../models/User');
const AutoApplicationSettings = require('../models/AutoApplicationSettings');
const ApplicationResult = require('../models/ApplicationResult');
const ApplicationQueue = require('../models/ApplicationQueue');
const { loggers } = require('./logger');
const websocketService = require('./websocketService');

// Lazy initialization of external services
let sgMail = null;
let twilioClient = null;

// Initialize SendGrid only if API key is provided
const initSendGrid = () => {
  if (!sgMail && process.env.SENDGRID_API_KEY) {
    sgMail = require('@sendgrid/mail');
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  }
  return sgMail;
};

// Initialize Twilio only if credentials are provided
const initTwilio = () => {
  if (!twilioClient && process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
    const twilio = require('twilio');
    twilioClient = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
  }
  return twilioClient;
};

/**
 * AutoApplicationNotificationService - Comprehensive notification system for auto-application events
 * 
 * This service provides:
 * - Multi-channel notification delivery (email, SMS, push, WebSocket)
 * - Application status update notifications
 * - Daily/weekly summary reports
 * - Urgent alerts for system issues or manual intervention needs
 * - Customizable notification preferences
 */
class AutoApplicationNotificationService {
  constructor() {
    this.notificationQueue = [];
    this.isProcessingQueue = false;
    this.batchSize = 10;
    this.processingInterval = null;
    this.QUEUE_PROCESSING_INTERVAL = 30 * 1000; // 30 seconds

    // Start queue processing
    this.startQueueProcessing();
  }

  /**
   * Send application status update notification
   * @param {string} userId - User ID
   * @param {Object} applicationData - Application data
   * @param {string} newStatus - New application status
   * @param {Object} metadata - Additional metadata
   */
  async sendApplicationStatusUpdate(userId, applicationData, newStatus, metadata = {}) {
    try {
      const user = await User.findById(userId);
      const settings = await AutoApplicationSettings.findByUserId(userId);

      if (!user || !settings || !settings.settings?.notificationPreferences?.immediate) {
        return;
      }

      const notificationData = {
        type: 'application_status_update',
        userId,
        priority: this.getStatusPriority(newStatus),
        data: {
          applicationId: applicationData._id,
          listingTitle: applicationData.listingSnapshot?.title || 'Property Application',
          listingLocation: applicationData.listingSnapshot?.location || 'Unknown Location',
          listingPrice: applicationData.listingSnapshot?.price || 'Price not available',
          listingUrl: applicationData.listingUrl,
          oldStatus: metadata.oldStatus,
          newStatus,
          timestamp: new Date(),
          confirmationNumber: applicationData.confirmationNumber,
          nextSteps: this.getNextSteps(newStatus, applicationData)
        }
      };

      await this.queueNotification(notificationData);

      // Send immediate WebSocket notification for real-time updates
      websocketService.sendAutoApplicationUpdate(userId, {
        action: 'status_update',
        applicationId: applicationData._id,
        status: newStatus,
        data: notificationData.data
      });

      loggers.app.info(`Application status notification queued for user ${userId}`, {
        applicationId: applicationData._id,
        newStatus
      });

    } catch (error) {
      loggers.app.error('Error sending application status update:', error);
    }
  }

  /**
   * Send daily summary report
   * @param {string} userId - User ID
   * @param {Object} summaryData - Daily summary data
   */
  async sendDailySummary(userId, summaryData) {
    try {
      const user = await User.findById(userId);
      const settings = await AutoApplicationSettings.findByUserId(userId);

      if (!user || !settings || !settings.settings?.notificationPreferences?.daily) {
        return;
      }

      const notificationData = {
        type: 'daily_summary',
        userId,
        priority: 'low',
        data: {
          date: new Date().toISOString().split('T')[0],
          applicationsSubmitted: summaryData.applicationsSubmitted || 0,
          applicationsSuccessful: summaryData.applicationsSuccessful || 0,
          applicationsRejected: summaryData.applicationsRejected || 0,
          applicationsPending: summaryData.applicationsPending || 0,
          successRate: summaryData.successRate || 0,
          topPerformingLocation: summaryData.topPerformingLocation,
          averageResponseTime: summaryData.averageResponseTime,
          dailyLimit: settings.settings.maxApplicationsPerDay,
          remainingApplications: settings.dailyApplicationsRemaining,
          recommendations: summaryData.recommendations || [],
          upcomingViewings: summaryData.upcomingViewings || []
        }
      };

      await this.queueNotification(notificationData);

      loggers.app.info(`Daily summary notification queued for user ${userId}`);

    } catch (error) {
      loggers.app.error('Error sending daily summary:', error);
    }
  }

  /**
   * Send weekly summary report
   * @param {string} userId - User ID
   * @param {Object} summaryData - Weekly summary data
   */
  async sendWeeklySummary(userId, summaryData) {
    try {
      const user = await User.findById(userId);
      const settings = await AutoApplicationSettings.findByUserId(userId);

      if (!user || !settings || !settings.settings?.notificationPreferences?.weekly) {
        return;
      }

      const notificationData = {
        type: 'weekly_summary',
        userId,
        priority: 'low',
        data: {
          weekStart: summaryData.weekStart,
          weekEnd: summaryData.weekEnd,
          totalApplications: summaryData.totalApplications || 0,
          successfulApplications: summaryData.successfulApplications || 0,
          landlordResponses: summaryData.landlordResponses || 0,
          viewingInvites: summaryData.viewingInvites || 0,
          acceptances: summaryData.acceptances || 0,
          successRate: summaryData.successRate || 0,
          responseRate: summaryData.responseRate || 0,
          acceptanceRate: summaryData.acceptanceRate || 0,
          topPerformingLocations: summaryData.topPerformingLocations || [],
          bestApplicationTimes: summaryData.bestApplicationTimes || [],
          marketInsights: summaryData.marketInsights || [],
          performanceComparison: summaryData.performanceComparison || {},
          actionItems: summaryData.actionItems || []
        }
      };

      await this.queueNotification(notificationData);

      loggers.app.info(`Weekly summary notification queued for user ${userId}`);

    } catch (error) {
      loggers.app.error('Error sending weekly summary:', error);
    }
  }

  /**
   * Send urgent alert for system issues or manual intervention needs
   * @param {string} userId - User ID
   * @param {string} alertType - Type of alert
   * @param {Object} alertData - Alert data
   */
  async sendUrgentAlert(userId, alertType, alertData) {
    try {
      const user = await User.findById(userId);
      if (!user) return;

      const notificationData = {
        type: 'urgent_alert',
        userId,
        priority: 'urgent',
        data: {
          alertType,
          title: this.getAlertTitle(alertType),
          message: this.getAlertMessage(alertType, alertData),
          actionRequired: this.getRequiredAction(alertType),
          timestamp: new Date(),
          ...alertData
        }
      };

      // Send urgent notifications immediately, bypassing the queue
      await this.deliverNotification(notificationData);

      // Also send WebSocket notification for immediate UI updates
      websocketService.sendAutoApplicationUpdate(userId, {
        action: 'urgent_alert',
        alertType,
        data: notificationData.data
      });

      loggers.app.warn(`Urgent alert sent to user ${userId}`, {
        alertType,
        message: notificationData.data.message
      });

    } catch (error) {
      loggers.app.error('Error sending urgent alert:', error);
    }
  }

  /**
   * Send system maintenance notification
   * @param {Array} userIds - Array of user IDs (optional, null for all users)
   * @param {Object} maintenanceData - Maintenance information
   */
  async sendMaintenanceNotification(userIds = null, maintenanceData) {
    try {
      let users;
      if (userIds) {
        users = await User.find({ _id: { $in: userIds } });
      } else {
        // Get all users with auto-application enabled
        const activeSettings = await AutoApplicationSettings.find({ enabled: true });
        const activeUserIds = activeSettings.map(s => s.userId);
        users = await User.find({ _id: { $in: activeUserIds } });
      }

      const notificationData = {
        type: 'system_maintenance',
        priority: 'high',
        data: {
          title: 'Auto-Application System Maintenance',
          scheduledStart: maintenanceData.scheduledStart,
          estimatedDuration: maintenanceData.estimatedDuration,
          affectedServices: maintenanceData.affectedServices || ['Auto-Application'],
          impact: maintenanceData.impact || 'Auto-applications will be temporarily paused',
          alternativeActions: maintenanceData.alternativeActions || [],
          contactInfo: maintenanceData.contactInfo
        }
      };

      // Send to all affected users
      for (const user of users) {
        const userNotification = {
          ...notificationData,
          userId: user._id
        };
        await this.queueNotification(userNotification);
      }

      loggers.app.info(`Maintenance notification queued for ${users.length} users`);

    } catch (error) {
      loggers.app.error('Error sending maintenance notification:', error);
    }
  }

  /**
   * Queue notification for batch processing
   * @param {Object} notificationData - Notification data
   */
  async queueNotification(notificationData) {
    this.notificationQueue.push({
      ...notificationData,
      queuedAt: new Date(),
      attempts: 0,
      maxAttempts: 3
    });

    // If queue is getting large, process immediately
    if (this.notificationQueue.length >= this.batchSize) {
      await this.processNotificationQueue();
    }
  }

  /**
   * Start queue processing interval
   */
  startQueueProcessing() {
    if (!this.processingInterval) {
      this.processingInterval = setInterval(() => {
        this.processNotificationQueue().catch(error => {
          loggers.app.error('Error in notification queue processing:', error);
        });
      }, this.QUEUE_PROCESSING_INTERVAL);

      loggers.app.info('Notification queue processing started');
    }
  }

  /**
   * Stop queue processing interval
   */
  stopQueueProcessing() {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      loggers.app.info('Notification queue processing stopped');
    }
  }

  /**
   * Process notification queue
   */
  async processNotificationQueue() {
    if (this.isProcessingQueue || this.notificationQueue.length === 0) {
      return;
    }

    try {
      this.isProcessingQueue = true;

      const batch = this.notificationQueue.splice(0, this.batchSize);

      loggers.app.info(`Processing ${batch.length} notifications`);

      for (const notification of batch) {
        try {
          await this.deliverNotification(notification);
          loggers.app.debug(`Notification delivered successfully`, {
            userId: notification.userId,
            type: notification.type
          });
        } catch (error) {
          notification.attempts += 1;
          notification.lastError = error.message;

          if (notification.attempts < notification.maxAttempts) {
            // Re-queue for retry
            this.notificationQueue.push(notification);
            loggers.app.warn(`Notification delivery failed, retrying`, {
              userId: notification.userId,
              type: notification.type,
              attempt: notification.attempts,
              error: error.message
            });
          } else {
            loggers.app.error(`Notification delivery failed permanently`, {
              userId: notification.userId,
              type: notification.type,
              attempts: notification.attempts,
              error: error.message
            });
          }
        }
      }

    } catch (error) {
      loggers.app.error('Error processing notification queue:', error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * Deliver notification through appropriate channels
   * @param {Object} notification - Notification data
   */
  async deliverNotification(notification) {
    const { userId, type, priority, data } = notification;

    const user = await User.findById(userId);
    if (!user) {
      throw new Error(`User ${userId} not found`);
    }

    const settings = await AutoApplicationSettings.findByUserId(userId);
    const preferences = settings?.settings?.notificationPreferences || {};

    const channels = this.getNotificationChannels(user, preferences, priority);

    // Deliver through each channel
    const deliveryPromises = [];

    if (channels.includes('email')) {
      deliveryPromises.push(this.sendEmailNotification(user, type, data));
    }

    if (channels.includes('sms')) {
      deliveryPromises.push(this.sendSMSNotification(user, type, data));
    }

    if (channels.includes('push')) {
      deliveryPromises.push(this.sendPushNotification(user, type, data));
    }

    // Wait for all deliveries to complete
    await Promise.allSettled(deliveryPromises);
  }

  /**
   * Send email notification
   * @param {Object} user - User object
   * @param {string} type - Notification type
   * @param {Object} data - Notification data
   */
  async sendEmailNotification(user, type, data) {
    const sgMailClient = initSendGrid();

    if (!sgMailClient) {
      loggers.app.warn('SendGrid not configured, skipping email notification');
      return;
    }

    const emailContent = this.formatEmailContent(type, data);

    const msg = {
      to: user.email,
      from: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
      subject: emailContent.subject,
      html: emailContent.html,
      text: emailContent.text
    };

    await sgMailClient.send(msg);

    loggers.app.info(`Email notification sent to ${user.email}`, {
      type,
      subject: emailContent.subject
    });
  }

  /**
   * Send SMS notification
   * @param {Object} user - User object
   * @param {string} type - Notification type
   * @param {Object} data - Notification data
   */
  async sendSMSNotification(user, type, data) {
    const twilioClientInstance = initTwilio();

    if (!twilioClientInstance) {
      loggers.app.warn('Twilio not configured, skipping SMS notification');
      return;
    }

    const phoneNumber = user.profile?.phone;
    if (!phoneNumber) {
      loggers.app.warn(`No phone number for user ${user._id}, skipping SMS`);
      return;
    }

    const smsContent = this.formatSMSContent(type, data);

    await twilioClientInstance.messages.create({
      body: smsContent,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phoneNumber
    });

    loggers.app.info(`SMS notification sent to ${phoneNumber}`, {
      type,
      userId: user._id
    });
  }

  /**
   * Send push notification
   * @param {Object} user - User object
   * @param {string} type - Notification type
   * @param {Object} data - Notification data
   */
  async sendPushNotification(user, type, data) {
    // Push notification implementation would go here
    // This could integrate with services like Firebase Cloud Messaging, OneSignal, etc.

    const pushContent = this.formatPushContent(type, data);

    loggers.app.info(`Push notification would be sent to user ${user._id}`, {
      type,
      title: pushContent.title,
      body: pushContent.body
    });

    // For now, just log the push notification
    // In a real implementation, this would send to a push service
  }

  /**
   * Get appropriate notification channels based on user preferences and priority
   * @param {Object} user - User object
   * @param {Object} preferences - Notification preferences
   * @param {string} priority - Notification priority
   * @returns {Array} Array of channel names
   */
  getNotificationChannels(user, preferences, priority) {
    const channels = [];

    // Email is always available if user has email
    if (user.email && preferences.email !== false) {
      channels.push('email');
    }

    // SMS for urgent notifications or if explicitly enabled
    if (user.profile?.phone && (priority === 'urgent' || preferences.sms === true)) {
      channels.push('sms');
    }

    // Push notifications if enabled
    if (preferences.push !== false) {
      channels.push('push');
    }

    return channels;
  }

  /**
   * Get priority level for application status
   * @param {string} status - Application status
   * @returns {string} Priority level
   */
  getStatusPriority(status) {
    switch (status) {
      case 'submitted':
        return 'high';
      case 'failed':
      case 'blocked':
      case 'captcha_required':
        return 'urgent';
      case 'completed':
        return 'medium';
      default:
        return 'low';
    }
  }

  /**
   * Get next steps based on application status
   * @param {string} status - Application status
   * @param {Object} applicationData - Application data
   * @returns {Array} Array of next steps
   */
  getNextSteps(status, applicationData) {
    switch (status) {
      case 'submitted':
        return [
          'Wait for landlord response',
          'Check your email for confirmation',
          'Prepare for potential viewing invitation'
        ];
      case 'failed':
        return [
          'Review error details in your dashboard',
          'Check if manual application is needed',
          'Contact support if issue persists'
        ];
      case 'blocked':
        return [
          'Auto-application has been paused',
          'Manual verification may be required',
          'Contact support for assistance'
        ];
      case 'captcha_required':
        return [
          'Complete CAPTCHA verification',
          'Check your dashboard for manual steps',
          'Application will resume automatically'
        ];
      default:
        return [];
    }
  }

  /**
   * Get alert title based on alert type
   * @param {string} alertType - Alert type
   * @returns {string} Alert title
   */
  getAlertTitle(alertType) {
    switch (alertType) {
      case 'daily_limit_reached':
        return 'Daily Application Limit Reached';
      case 'account_blocked':
        return 'Account Temporarily Blocked';
      case 'captcha_required':
        return 'Manual Verification Required';
      case 'system_error':
        return 'System Error Detected';
      case 'profile_incomplete':
        return 'Profile Information Required';
      case 'documents_missing':
        return 'Required Documents Missing';
      default:
        return 'Auto-Application Alert';
    }
  }

  /**
   * Get alert message based on alert type
   * @param {string} alertType - Alert type
   * @param {Object} alertData - Alert data
   * @returns {string} Alert message
   */
  getAlertMessage(alertType, alertData) {
    switch (alertType) {
      case 'daily_limit_reached':
        return `You have reached your daily application limit of ${alertData.limit}. Auto-application will resume tomorrow.`;
      case 'account_blocked':
        return 'Your account has been temporarily blocked by the platform. Auto-application is paused until resolved.';
      case 'captcha_required':
        return 'A CAPTCHA verification is required to continue. Please complete the verification in your dashboard.';
      case 'system_error':
        return `A system error has occurred: ${alertData.error}. Our team has been notified.`;
      case 'profile_incomplete':
        return 'Your profile information is incomplete. Please update your details to continue auto-application.';
      case 'documents_missing':
        return `Required documents are missing: ${alertData.missingDocuments?.join(', ')}. Please upload them to continue.`;
      default:
        return 'An alert has been triggered for your auto-application system.';
    }
  }

  /**
   * Get required action based on alert type
   * @param {string} alertType - Alert type
   * @returns {string} Required action
   */
  getRequiredAction(alertType) {
    switch (alertType) {
      case 'daily_limit_reached':
        return 'No action required - system will resume automatically tomorrow';
      case 'account_blocked':
        return 'Contact support or wait for automatic resolution';
      case 'captcha_required':
        return 'Complete CAPTCHA verification in dashboard';
      case 'system_error':
        return 'Monitor system status or contact support if persistent';
      case 'profile_incomplete':
        return 'Update profile information in settings';
      case 'documents_missing':
        return 'Upload required documents in document vault';
      default:
        return 'Check dashboard for details';
    }
  }

  /**
   * Format email content based on notification type
   * @param {string} type - Notification type
   * @param {Object} data - Notification data
   * @returns {Object} Email content
   */
  formatEmailContent(type, data) {
    switch (type) {
      case 'application_status_update':
        return this.formatApplicationStatusEmail(data);
      case 'daily_summary':
        return this.formatDailySummaryEmail(data);
      case 'weekly_summary':
        return this.formatWeeklySummaryEmail(data);
      case 'urgent_alert':
        return this.formatUrgentAlertEmail(data);
      case 'system_maintenance':
        return this.formatMaintenanceEmail(data);
      default:
        return {
          subject: 'ZakMakelaar Auto-Application Notification',
          html: '<p>You have a new notification from ZakMakelaar.</p>',
          text: 'You have a new notification from ZakMakelaar.'
        };
    }
  }

  /**
   * Format application status update email
   * @param {Object} data - Application data
   * @returns {Object} Email content
   */
  formatApplicationStatusEmail(data) {
    const statusEmoji = {
      submitted: '✅',
      failed: '❌',
      blocked: '🚫',
      captcha_required: '🔐',
      completed: '🎉'
    };

    const emoji = statusEmoji[data.newStatus] || '📋';

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>${emoji} Application Status Update</h2>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>${data.listingTitle}</h3>
          <p><strong>Location:</strong> ${data.listingLocation}</p>
          <p><strong>Price:</strong> ${data.listingPrice}</p>
          <p><strong>Status:</strong> ${data.newStatus.toUpperCase()}</p>
          ${data.confirmationNumber ? `<p><strong>Confirmation:</strong> ${data.confirmationNumber}</p>` : ''}
        </div>

        ${data.nextSteps && data.nextSteps.length > 0 ? `
          <div style="margin: 20px 0;">
            <h4>Next Steps:</h4>
            <ul>
              ${data.nextSteps.map(step => `<li>${step}</li>`).join('')}
            </ul>
          </div>
        ` : ''}

        <div style="margin: 30px 0; text-align: center;">
          <a href="${data.listingUrl}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">View Property</a>
        </div>

        <p style="color: #666; font-size: 12px;">
          This is an automated notification from ZakMakelaar Auto-Application system.
        </p>
      </div>
    `;

    return {
      subject: `${emoji} Application Update: ${data.listingTitle}`,
      html,
      text: `Application Status Update\n\nProperty: ${data.listingTitle}\nLocation: ${data.listingLocation}\nStatus: ${data.newStatus}\n\nView property: ${data.listingUrl}`
    };
  }

  /**
   * Format daily summary email
   * @param {Object} data - Summary data
   * @returns {Object} Email content
   */
  formatDailySummaryEmail(data) {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>📊 Daily Auto-Application Summary - ${data.date}</h2>
        
        <div style="display: flex; flex-wrap: wrap; gap: 15px; margin: 20px 0;">
          <div style="background-color: #e3f2fd; padding: 15px; border-radius: 8px; flex: 1; min-width: 120px;">
            <h4 style="margin: 0; color: #1976d2;">Applications Submitted</h4>
            <p style="font-size: 24px; font-weight: bold; margin: 5px 0; color: #1976d2;">${data.applicationsSubmitted}</p>
          </div>
          <div style="background-color: #e8f5e8; padding: 15px; border-radius: 8px; flex: 1; min-width: 120px;">
            <h4 style="margin: 0; color: #388e3c;">Successful</h4>
            <p style="font-size: 24px; font-weight: bold; margin: 5px 0; color: #388e3c;">${data.applicationsSuccessful}</p>
          </div>
          <div style="background-color: #fff3e0; padding: 15px; border-radius: 8px; flex: 1; min-width: 120px;">
            <h4 style="margin: 0; color: #f57c00;">Pending</h4>
            <p style="font-size: 24px; font-weight: bold; margin: 5px 0; color: #f57c00;">${data.applicationsPending}</p>
          </div>
        </div>

        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h4>Performance Metrics</h4>
          <p><strong>Success Rate:</strong> ${data.successRate}%</p>
          <p><strong>Remaining Applications Today:</strong> ${data.remainingApplications}/${data.dailyLimit}</p>
          ${data.topPerformingLocation ? `<p><strong>Top Location:</strong> ${data.topPerformingLocation}</p>` : ''}
          ${data.averageResponseTime ? `<p><strong>Avg Response Time:</strong> ${data.averageResponseTime}</p>` : ''}
        </div>

        ${data.recommendations && data.recommendations.length > 0 ? `
          <div style="margin: 20px 0;">
            <h4>💡 Recommendations</h4>
            <ul>
              ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
          </div>
        ` : ''}

        ${data.upcomingViewings && data.upcomingViewings.length > 0 ? `
          <div style="margin: 20px 0;">
            <h4>📅 Upcoming Viewings</h4>
            <ul>
              ${data.upcomingViewings.map(viewing => `<li>${viewing.property} - ${viewing.date}</li>`).join('')}
            </ul>
          </div>
        ` : ''}

        <div style="margin: 30px 0; text-align: center;">
          <a href="${process.env.FRONTEND_URL}/auto-application-dashboard" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">View Dashboard</a>
        </div>
      </div>
    `;

    return {
      subject: `📊 Daily Summary - ${data.applicationsSubmitted} applications submitted`,
      html,
      text: `Daily Auto-Application Summary - ${data.date}\n\nApplications Submitted: ${data.applicationsSubmitted}\nSuccessful: ${data.applicationsSuccessful}\nPending: ${data.applicationsPending}\nSuccess Rate: ${data.successRate}%`
    };
  }

  /**
   * Format weekly summary email
   * @param {Object} data - Summary data
   * @returns {Object} Email content
   */
  formatWeeklySummaryEmail(data) {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>📈 Weekly Auto-Application Report</h2>
        <p style="color: #666;">${data.weekStart} - ${data.weekEnd}</p>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;">
          <div style="background-color: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
            <h4 style="margin: 0; color: #1976d2;">Total Applications</h4>
            <p style="font-size: 28px; font-weight: bold; margin: 5px 0; color: #1976d2;">${data.totalApplications}</p>
          </div>
          <div style="background-color: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
            <h4 style="margin: 0; color: #388e3c;">Success Rate</h4>
            <p style="font-size: 28px; font-weight: bold; margin: 5px 0; color: #388e3c;">${data.successRate}%</p>
          </div>
          <div style="background-color: #f3e5f5; padding: 15px; border-radius: 8px; text-align: center;">
            <h4 style="margin: 0; color: #7b1fa2;">Response Rate</h4>
            <p style="font-size: 28px; font-weight: bold; margin: 5px 0; color: #7b1fa2;">${data.responseRate}%</p>
          </div>
          <div style="background-color: #fff8e1; padding: 15px; border-radius: 8px; text-align: center;">
            <h4 style="margin: 0; color: #f57f17;">Acceptances</h4>
            <p style="font-size: 28px; font-weight: bold; margin: 5px 0; color: #f57f17;">${data.acceptances}</p>
          </div>
        </div>

        ${data.topPerformingLocations && data.topPerformingLocations.length > 0 ? `
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h4>🏆 Top Performing Locations</h4>
            <ol>
              ${data.topPerformingLocations.map(loc => `<li>${loc.location} - ${loc.successRate}% success rate</li>`).join('')}
            </ol>
          </div>
        ` : ''}

        ${data.marketInsights && data.marketInsights.length > 0 ? `
          <div style="margin: 20px 0;">
            <h4>📊 Market Insights</h4>
            <ul>
              ${data.marketInsights.map(insight => `<li>${insight}</li>`).join('')}
            </ul>
          </div>
        ` : ''}

        ${data.actionItems && data.actionItems.length > 0 ? `
          <div style="background-color: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h4>🎯 Action Items</h4>
            <ul>
              ${data.actionItems.map(item => `<li><strong>${item.priority}:</strong> ${item.action}</li>`).join('')}
            </ul>
          </div>
        ` : ''}

        <div style="margin: 30px 0; text-align: center;">
          <a href="${process.env.FRONTEND_URL}/auto-application-dashboard" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">View Full Report</a>
        </div>
      </div>
    `;

    return {
      subject: `📈 Weekly Report - ${data.totalApplications} applications, ${data.successRate}% success rate`,
      html,
      text: `Weekly Auto-Application Report\n\nTotal Applications: ${data.totalApplications}\nSuccess Rate: ${data.successRate}%\nResponse Rate: ${data.responseRate}%\nAcceptances: ${data.acceptances}`
    };
  }

  /**
   * Format urgent alert email
   * @param {Object} data - Alert data
   * @returns {Object} Email content
   */
  formatUrgentAlertEmail(data) {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #ffebee; border-left: 4px solid #f44336; padding: 20px; margin: 20px 0;">
          <h2 style="color: #d32f2f; margin: 0;">🚨 ${data.title}</h2>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p style="font-size: 16px; line-height: 1.5;">${data.message}</p>
          
          <div style="margin: 20px 0;">
            <h4 style="color: #d32f2f;">Action Required:</h4>
            <p style="font-weight: bold;">${data.actionRequired}</p>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            <strong>Time:</strong> ${new Date(data.timestamp).toLocaleString()}
          </p>
        </div>

        <div style="margin: 30px 0; text-align: center;">
          <a href="${process.env.FRONTEND_URL}/auto-application-dashboard" style="background-color: #f44336; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px;">Take Action</a>
        </div>

        <p style="color: #666; font-size: 12px;">
          This is an urgent notification from ZakMakelaar Auto-Application system.
        </p>
      </div>
    `;

    return {
      subject: `🚨 URGENT: ${data.title}`,
      html,
      text: `URGENT ALERT: ${data.title}\n\n${data.message}\n\nAction Required: ${data.actionRequired}\n\nTime: ${new Date(data.timestamp).toLocaleString()}`
    };
  }

  /**
   * Format maintenance notification email
   * @param {Object} data - Maintenance data
   * @returns {Object} Email content
   */
  formatMaintenanceEmail(data) {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #e3f2fd; border-left: 4px solid #2196f3; padding: 20px; margin: 20px 0;">
          <h2 style="color: #1976d2; margin: 0;">🔧 ${data.title}</h2>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Scheduled Start:</strong> ${new Date(data.scheduledStart).toLocaleString()}</p>
          <p><strong>Estimated Duration:</strong> ${data.estimatedDuration}</p>
          <p><strong>Affected Services:</strong> ${data.affectedServices.join(', ')}</p>
          
          <div style="margin: 20px 0;">
            <h4>Impact:</h4>
            <p>${data.impact}</p>
          </div>

          ${data.alternativeActions && data.alternativeActions.length > 0 ? `
            <div style="margin: 20px 0;">
              <h4>Alternative Actions:</h4>
              <ul>
                ${data.alternativeActions.map(action => `<li>${action}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
        </div>

        ${data.contactInfo ? `
          <p style="color: #666;">
            Questions? Contact us at: ${data.contactInfo}
          </p>
        ` : ''}

        <p style="color: #666; font-size: 12px;">
          We apologize for any inconvenience and appreciate your understanding.
        </p>
      </div>
    `;

    return {
      subject: `🔧 Scheduled Maintenance: ${data.title}`,
      html,
      text: `Scheduled Maintenance: ${data.title}\n\nStart: ${new Date(data.scheduledStart).toLocaleString()}\nDuration: ${data.estimatedDuration}\nImpact: ${data.impact}`
    };
  }

  /**
   * Format SMS content based on notification type
   * @param {string} type - Notification type
   * @param {Object} data - Notification data
   * @returns {string} SMS content
   */
  formatSMSContent(type, data) {
    switch (type) {
      case 'application_status_update':
        return `ZakMakelaar: Application for ${data.listingTitle} is now ${data.newStatus.toUpperCase()}. Check your dashboard for details.`;
      case 'urgent_alert':
        return `ZakMakelaar URGENT: ${data.title}. ${data.message} Action required: ${data.actionRequired}`;
      case 'daily_summary':
        return `ZakMakelaar Daily: ${data.applicationsSubmitted} applications submitted, ${data.applicationsSuccessful} successful (${data.successRate}% success rate)`;
      default:
        return 'You have a new notification from ZakMakelaar Auto-Application system.';
    }
  }

  /**
   * Format push notification content
   * @param {string} type - Notification type
   * @param {Object} data - Notification data
   * @returns {Object} Push content
   */
  formatPushContent(type, data) {
    switch (type) {
      case 'application_status_update':
        return {
          title: 'Application Status Update',
          body: `${data.listingTitle} - Status: ${data.newStatus}`,
          data: {
            applicationId: data.applicationId,
            listingUrl: data.listingUrl
          }
        };
      case 'urgent_alert':
        return {
          title: data.title,
          body: data.message,
          data: {
            alertType: data.alertType,
            actionRequired: data.actionRequired
          }
        };
      case 'daily_summary':
        return {
          title: 'Daily Summary',
          body: `${data.applicationsSubmitted} applications submitted today`,
          data: {
            successRate: data.successRate
          }
        };
      default:
        return {
          title: 'ZakMakelaar Notification',
          body: 'You have a new notification',
          data: {}
        };
    }
  }

  /**
   * Shutdown the service gracefully
   */
  shutdown() {
    this.stopQueueProcessing();

    // Process remaining notifications
    if (this.notificationQueue.length > 0) {
      loggers.app.info(`Processing ${this.notificationQueue.length} remaining notifications before shutdown`);
      this.processNotificationQueue().catch(error => {
        loggers.app.error('Error processing final notifications:', error);
      });
    }

    loggers.app.info('AutoApplicationNotificationService shutdown complete');
  }
}

module.exports = new AutoApplicationNotificationService();