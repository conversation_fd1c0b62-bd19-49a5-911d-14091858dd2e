const Redis = require('ioredis');
const config = require('../config/config');

class CacheService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.init();
  }

  async init() {
    try {
      // Initialize Redis client with configuration
      this.client = new Redis({
        host: config.redisHost,
        port: config.redisPort,
        password: config.redisPassword,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        // Graceful handling when Redis is not available
        enableOfflineQueue: false,
      });

      // Event handlers
      this.client.on('connect', () => {
        console.log('✅ Redis connected');
        this.isConnected = true;
      });

      this.client.on('error', (err) => {
        console.log('⚠️ Redis connection error:', err.message);
        this.isConnected = false;
      });

      this.client.on('close', () => {
        console.log('⚠️ Redis connection closed');
        this.isConnected = false;
      });

      // Try to connect
      if (config.redisHost) {
        await this.client.connect();
      } else {
        console.log('⚠️ Redis not configured, caching disabled');
      }
    } catch (error) {
      console.log('⚠️ Redis initialization failed:', error.message);
      this.isConnected = false;
    }
  }

  // Get value from cache
  async get(key) {
    if (!this.isConnected) {
      console.log(`Cache get skipped - not connected: ${key}`);
      return null;
    }
    
    try {
      const value = await this.client.get(key);
      const result = value ? JSON.parse(value) : null;
      
      if (result) {
        console.log(`Cache hit: ${key}`);
      } else {
        console.log(`Cache miss: ${key}`);
      }
      
      return result;
    } catch (error) {
      console.error(`Cache get error for key ${key}:`, error.message);
      // Log to application logger if available
      try {
        const { logHelpers } = require('./logger');
        logHelpers.logCache('get', key, false, error);
      } catch (logError) {
        // Ignore logging errors
      }
      return null;
    }
  }

  // Set value in cache with TTL (time to live in seconds)
  async set(key, value, ttl = 3600) {
    if (!this.isConnected) {
      console.log(`Cache set skipped - not connected: ${key}`);
      return false;
    }
    
    try {
      const serialized = JSON.stringify(value);
      if (ttl > 0) {
        await this.client.setex(key, ttl, serialized);
        console.log(`Cache set with TTL ${ttl}s: ${key}`);
      } else {
        await this.client.set(key, serialized);
        console.log(`Cache set (no TTL): ${key}`);
      }
      
      // Log to application logger if available
      try {
        const { logHelpers } = require('./logger');
        logHelpers.logCache('set', key, null, null, `ttl:${ttl}s`);
      } catch (logError) {
        // Ignore logging errors
      }
      
      return true;
    } catch (error) {
      console.error(`Cache set error for key ${key}:`, error.message);
      
      // Log to application logger if available
      try {
        const { logHelpers } = require('./logger');
        logHelpers.logCache('set', key, null, error);
      } catch (logError) {
        // Ignore logging errors
      }
      
      return false;
    }
  }

  // Delete key from cache
  async del(key) {
    if (!this.isConnected) return false;
    
    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  // Delete multiple keys matching pattern
  async delPattern(pattern) {
    if (!this.isConnected) return false;
    
    try {
      const keys = await this.client.keys(pattern);
      if (keys.length > 0) {
        await this.client.del(...keys);
      }
      return true;
    } catch (error) {
      console.error('Cache delete pattern error:', error);
      return false;
    }
  }

  // Check if key exists
  async exists(key) {
    if (!this.isConnected) {
      console.log(`Cache exists check skipped - not connected: ${key}`);
      return false;
    }
    
    try {
      const result = await this.client.exists(key);
      const exists = result === 1;
      
      // Log to application logger if available
      try {
        const { logHelpers } = require('./logger');
        logHelpers.logCache('exists', key, exists);
      } catch (logError) {
        // Ignore logging errors
      }
      
      return exists;
    } catch (error) {
      console.error(`Cache exists error for key ${key}:`, error.message);
      
      // Log to application logger if available
      try {
        const { logHelpers } = require('./logger');
        logHelpers.logCache('exists', key, false, error);
      } catch (logError) {
        // Ignore logging errors
      }
      
      return false;
    }
  }

  // Get cache statistics
  async getStats() {
    if (!this.isConnected) return null;
    
    try {
      const info = await this.client.info('memory');
      const keyspace = await this.client.info('keyspace');
      return {
        connected: this.isConnected,
        memory: info,
        keyspace: keyspace
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return null;
    }
  }

  // Flush all cache
  async flush() {
    if (!this.isConnected) return false;
    
    try {
      await this.client.flushall();
      return true;
    } catch (error) {
      console.error('Cache flush error:', error);
      return false;
    }
  }

  // Close connection
  async close() {
    if (this.client) {
      await this.client.quit();
      this.isConnected = false;
    }
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;
