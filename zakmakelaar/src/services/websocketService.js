const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const config = require('../config/config');
const { loggers } = require('./logger');

class WebSocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socket mapping
  }

  /**
   * Initialize WebSocket server
   * @param {http.Server} server - HTTP server instance
   */
  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: config.corsOrigin,
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, config.jwtSecret);
        const user = await this.getUserById(decoded.id);
        
        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user._id.toString();
        socket.user = user;
        next();
      } catch (error) {
        loggers.app.error('WebSocket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });

    // Connection handling
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
    });

    loggers.app.info('WebSocket service initialized');
  }

  /**
   * Handle new socket connection
   * @param {Socket} socket - Socket.io socket instance
   */
  handleConnection(socket) {
    const userId = socket.userId;
    
    // Store user connection
    this.connectedUsers.set(userId, socket);
    
    loggers.app.info(`User ${userId} connected via WebSocket`);

    // Join user-specific room
    socket.join(`user:${userId}`);

    // Handle auto-application specific events
    socket.on('subscribe:auto-application', () => {
      socket.join(`auto-application:${userId}`);
      loggers.app.debug(`User ${userId} subscribed to auto-application updates`);
    });

    socket.on('unsubscribe:auto-application', () => {
      socket.leave(`auto-application:${userId}`);
      loggers.app.debug(`User ${userId} unsubscribed from auto-application updates`);
    });

    // Handle queue monitoring
    socket.on('subscribe:queue-status', () => {
      socket.join(`queue:${userId}`);
      loggers.app.debug(`User ${userId} subscribed to queue status updates`);
    });

    socket.on('unsubscribe:queue-status', () => {
      socket.leave(`queue:${userId}`);
      loggers.app.debug(`User ${userId} unsubscribed from queue status updates`);
    });

    // Handle application monitoring
    socket.on('subscribe:application-monitor', () => {
      socket.join(`monitor:${userId}`);
      loggers.app.debug(`User ${userId} subscribed to application monitoring`);
    });

    socket.on('unsubscribe:application-monitor', () => {
      socket.leave(`monitor:${userId}`);
      loggers.app.debug(`User ${userId} unsubscribed from application monitoring`);
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      this.connectedUsers.delete(userId);
      loggers.app.info(`User ${userId} disconnected: ${reason}`);
    });

    // Send initial connection confirmation
    socket.emit('connected', {
      message: 'WebSocket connection established',
      userId: userId,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Send auto-application status update to user
   * @param {string} userId - User ID
   * @param {Object} statusUpdate - Status update data
   */
  sendAutoApplicationUpdate(userId, statusUpdate) {
    if (!this.io) return;

    const data = {
      type: 'auto-application-status',
      timestamp: new Date().toISOString(),
      ...statusUpdate
    };

    this.io.to(`auto-application:${userId}`).emit('auto-application:status-update', data);
    loggers.app.debug(`Sent auto-application update to user ${userId}:`, data);
  }

  /**
   * Send queue status update to user
   * @param {string} userId - User ID
   * @param {Object} queueUpdate - Queue update data
   */
  sendQueueUpdate(userId, queueUpdate) {
    if (!this.io) return;

    const data = {
      type: 'queue-status',
      timestamp: new Date().toISOString(),
      ...queueUpdate
    };

    this.io.to(`queue:${userId}`).emit('queue:status-update', data);
    loggers.app.debug(`Sent queue update to user ${userId}:`, data);
  }

  /**
   * Send application result update to user
   * @param {string} userId - User ID
   * @param {Object} applicationResult - Application result data
   */
  sendApplicationResult(userId, applicationResult) {
    if (!this.io) return;

    const data = {
      type: 'application-result',
      timestamp: new Date().toISOString(),
      ...applicationResult
    };

    this.io.to(`user:${userId}`).emit('application:result', data);
    this.io.to(`monitor:${userId}`).emit('monitor:application-result', data);
    loggers.app.debug(`Sent application result to user ${userId}:`, data);
  }

  /**
   * Send application progress update to user
   * @param {string} userId - User ID
   * @param {Object} progressUpdate - Progress update data
   */
  sendApplicationProgress(userId, progressUpdate) {
    if (!this.io) return;

    const data = {
      type: 'application-progress',
      timestamp: new Date().toISOString(),
      ...progressUpdate
    };

    this.io.to(`user:${userId}`).emit('application:progress', data);
    loggers.app.debug(`Sent application progress to user ${userId}:`, data);
  }

  /**
   * Send system alert to user
   * @param {string} userId - User ID
   * @param {Object} alert - Alert data
   */
  sendSystemAlert(userId, alert) {
    if (!this.io) return;

    const data = {
      type: 'system-alert',
      timestamp: new Date().toISOString(),
      ...alert
    };

    this.io.to(`user:${userId}`).emit('system:alert', data);
    loggers.app.debug(`Sent system alert to user ${userId}:`, data);
  }

  /**
   * Send document upload confirmation to user
   * @param {string} userId - User ID
   * @param {Object} documentUpdate - Document update data
   */
  sendDocumentUpdate(userId, documentUpdate) {
    if (!this.io) return;

    const data = {
      type: 'document-update',
      timestamp: new Date().toISOString(),
      ...documentUpdate
    };

    this.io.to(`user:${userId}`).emit('document:update', data);
    loggers.app.debug(`Sent document update to user ${userId}:`, data);
  }

  /**
   * Send monitoring data update to user
   * @param {string} userId - User ID
   * @param {Object} monitoringData - Monitoring data
   */
  sendMonitoringUpdate(userId, monitoringData) {
    if (!this.io) return;

    const data = {
      type: 'monitoring-update',
      timestamp: new Date().toISOString(),
      ...monitoringData
    };

    this.io.to(`monitor:${userId}`).emit('monitor:update', data);
    loggers.app.debug(`Sent monitoring update to user ${userId}:`, data);
  }

  /**
   * Broadcast system-wide notification
   * @param {Object} notification - Notification data
   */
  broadcastSystemNotification(notification) {
    if (!this.io) return;

    const data = {
      type: 'system-notification',
      timestamp: new Date().toISOString(),
      ...notification
    };

    this.io.emit('system:notification', data);
    loggers.app.info('Broadcasted system notification:', data);
  }

  /**
   * Get connected user count
   * @returns {number} Number of connected users
   */
  getConnectedUserCount() {
    return this.connectedUsers.size;
  }

  /**
   * Check if user is connected
   * @param {string} userId - User ID
   * @returns {boolean} True if user is connected
   */
  isUserConnected(userId) {
    return this.connectedUsers.has(userId);
  }

  /**
   * Get user by ID (placeholder - should be implemented based on your user service)
   * @param {string} userId - User ID
   * @returns {Object|null} User object or null
   */
  async getUserById(userId) {
    try {
      // This should be replaced with actual user service call
      const User = require('../models/User');
      return await User.findById(userId);
    } catch (error) {
      loggers.app.error('Error fetching user:', error);
      return null;
    }
  }

  /**
   * Close WebSocket server
   */
  close() {
    if (this.io) {
      this.io.close();
      this.connectedUsers.clear();
      loggers.app.info('WebSocket service closed');
    }
  }
}

// Export singleton instance
module.exports = new WebSocketService();