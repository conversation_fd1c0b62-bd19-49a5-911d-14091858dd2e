# Frontend Compatibility Layer

This module ensures that unified property schema data is compatible with frontend expectations. It provides conversion functions to transform unified property data into formats expected by the frontend components, maintaining backward compatibility while leveraging the enhanced schema.

## Overview

The Frontend Compatibility Layer serves as a bridge between the unified property schema and the frontend application. It ensures that property data is returned in a format that the frontend components expect, regardless of whether the data comes from the legacy Listing model or the new unified property schema.

## Key Features

1. **Format Conversion**: Converts between different data formats (e.g., string vs. numeric prices, structured vs. string locations)
2. **Data Type Normalization**: Ensures consistent data types for frontend components
3. **Backward Compatibility**: Maintains compatibility with existing frontend code
4. **Frontend-Expected Formats**: Handles special formatting requirements like price formatting

## Main Functions

### `convertToFrontendFormat(unifiedProperty)`

Converts a unified property object to a frontend-compatible format. This is the main function used by API endpoints to ensure consistent data format for the frontend.

```javascript
const frontendProperty = convertToFrontendFormat(unifiedProperty);
```

### `convertFromLegacyListing(legacyListing)`

Converts a legacy listing to the unified format, useful for migration and ensuring consistent data structure.

```javascript
const unifiedProperty = convertFromLegacyListing(legacyListing);
```

### Helper Functions

- `formatLocation(location)`: Formats location data for frontend compatibility
- `formatPrice(price)`: Formats price for frontend display
- `extractNumericSize(sizeString)`: Extracts numeric size from size string
- `inferFurnishedStatus(interior)`: Infers furnished status from interior description
- `normalizeRooms(rooms)`: Normalizes room count to frontend-expected format

## Frontend Data Format Requirements

The frontend expects property data in the following format:

| Field | Type | Format | Notes |
|-------|------|--------|-------|
| _id | String | MongoDB ObjectId | |
| id | String | Same as _id | Alias for compatibility |
| title | String | | |
| description | String | | |
| price | String | "€ X.XXX per maand" | Frontend has formatPrice utility |
| location | String | "Street HouseNumber, City" | |
| propertyType | String | "apartment", "house", etc. | |
| rooms | String/Number | | Frontend handles both formats |
| bedrooms | String/Number | | Frontend handles both formats |
| bathrooms | String/Number | | Frontend handles both formats |
| area | Number | | Numeric area in square meters |
| size | String | "85 m²" | Formatted size string |
| year | String | "2010" | |
| interior | String | "Gemeubileerd", "Kaal", etc. | Dutch terms |
| furnished | Boolean | | |
| images | Array | ["url1", "url2"] | Array of image URLs |
| source | String | "funda.nl", etc. | |
| dateAdded | String | ISO date string | |

## Integration with API Endpoints

The Frontend Compatibility Layer is integrated with the API endpoints in the following way:

1. The `listingController.js` uses `convertToFrontendFormat()` to convert unified property data before sending it to the frontend
2. The `transformationIntegration.js` uses the layer to ensure scraped data is compatible with frontend expectations

## Testing

Comprehensive tests are available in:
- `frontendCompatibilityLayer.test.js`: Unit tests for the compatibility layer functions
- `frontendCompatibility.integration.test.js`: Integration tests with API endpoints

## Usage Example

```javascript
const { convertToFrontendFormat } = require('./frontendCompatibilityLayer');

// In an API endpoint
app.get('/api/properties/:id', async (req, res) => {
  const property = await Property.findById(req.params.id);
  
  // Convert to frontend-compatible format
  const frontendProperty = convertToFrontendFormat(property);
  
  res.json({
    status: 'success',
    data: {
      property: frontendProperty
    }
  });
});
```