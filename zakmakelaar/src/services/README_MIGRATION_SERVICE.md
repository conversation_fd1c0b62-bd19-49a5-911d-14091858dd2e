# Migration Service

The Migration Service provides a robust framework for migrating data from legacy Listing records to the new unified schema-based Enhanced Property model. It handles batch processing, validation, and rollback functionality to ensure a smooth migration process.

## Features

- **Batch Processing**: Efficiently processes large datasets in configurable batches to avoid memory issues
- **Validation**: Validates migrated data to ensure integrity and consistency
- **Error Handling**: Comprehensive error tracking and reporting
- **Rollback Support**: Ability to roll back migrations if issues are detected
- **Progress Tracking**: Detailed statistics and logging of the migration process
- **Customizable Transformation**: Support for custom transformers and validators

## Usage

### Basic Migration

```javascript
const { MigrationService } = require('./services/migrationService');
const Listing = require('./models/Listing');
const EnhancedProperty = require('./models/EnhancedProperty');

// Create migration service
const migrationService = new MigrationService(Listing, EnhancedProperty, {
  batchSize: 100,
  validationLevel: 'standard',
  logProgress: true
});

// Run migration
migrationService.migrateExistingData()
  .then(stats => {
    console.log('Migration completed:', stats);
  })
  .catch(error => {
    console.error('Migration failed:', error);
  });
```

### Filtering Records to Migrate

You can provide a query to filter which records to migrate:

```javascript
// Only migrate listings from a specific source
migrationService.migrateExistingData({ source: 'funda.nl' })
  .then(stats => {
    console.log('Migration completed:', stats);
  });
```

### Rolling Back a Migration

If issues are detected, you can roll back the migration:

```javascript
// Roll back all migrated records
migrationService.rollbackMigration()
  .then(stats => {
    console.log('Rollback completed:', stats);
  });

// Roll back only specific records
migrationService.rollbackMigration({ 'sourceMetadata.website': 'funda.nl' })
  .then(stats => {
    console.log('Rollback completed:', stats);
  });
```

## Configuration Options

The Migration Service accepts the following configuration options:

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `batchSize` | Number | 100 | Number of records to process in each batch |
| `preserveOriginal` | Boolean | true | Whether to preserve original data in the target record |
| `validationLevel` | String | 'standard' | Validation level: 'none', 'standard', or 'strict' |
| `logProgress` | Boolean | true | Whether to log progress to the console |
| `systemUserId` | ObjectId | '000000000000000000000000' | User ID to use for system-created records |
| `transformationVersion` | String | '1.0.0-migration' | Version identifier for the migration |
| `transformer` | Object | null | Custom transformer instance to use |
| `validator` | Object | null | Custom validator instance to use |

## Command Line Interface

A command-line utility is provided for running migrations:

```bash
# Run migration with default options
node migrate-listings.js

# Run migration with custom batch size
node migrate-listings.js --batch=50

# Run migration with strict validation
node migrate-listings.js --validation=strict

# Roll back migration
node migrate-listings.js --rollback

# Show help
node migrate-listings.js --help
```

## Integration with Schema Transformer

For advanced transformations, you can integrate with the Schema Transformer:

```javascript
const { MigrationService } = require('./services/migrationService');
const { SchemaTransformer } = require('./services/schemaTransformer');
const { ValidationEngine } = require('./services/validationEngine');
const { FieldMappingRegistry } = require('./services/fieldMappingRegistry');
const { MappingConfigLoader } = require('./services/mappingConfigLoader');
const { getSchema } = require('./schemas/unifiedPropertySchema');

// Create field mapping registry and load mappings
const mappingRegistry = new FieldMappingRegistry();
const mappingLoader = new MappingConfigLoader(mappingRegistry);
await mappingLoader.loadMappings();

// Create schema transformer and validation engine
const transformer = new SchemaTransformer(mappingRegistry);
const validator = new ValidationEngine(getSchema());

// Create migration service with custom transformer and validator
const migrationService = new MigrationService(Listing, EnhancedProperty, {
  transformer,
  validator,
  validationLevel: 'strict'
});

// Run migration
await migrationService.migrateExistingData();
```

## Error Handling

The Migration Service provides detailed error tracking and reporting:

```javascript
// Get migration statistics
const stats = migrationService.getStats();
console.log(`Processed: ${stats.processed}`);
console.log(`Successful: ${stats.successful}`);
console.log(`Failed: ${stats.failed}`);
console.log(`Validation errors: ${stats.validationErrors}`);
console.log(`Transformation errors: ${stats.transformationErrors}`);

// Get migration history
const history = migrationService.getMigrationHistory();
console.log(`Migration history:`, history);
```

## Best Practices

1. **Test First**: Always test migrations on a small subset of data before running on the full dataset
2. **Backup Data**: Create a backup of your database before running migrations
3. **Monitor Progress**: Use the logging and statistics to monitor migration progress
4. **Validate Results**: Verify migrated data integrity after migration
5. **Incremental Migration**: For large datasets, consider migrating in stages by source or date range