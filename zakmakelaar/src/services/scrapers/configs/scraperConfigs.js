/**
 * Scraper configurations for different rental websites
 */

const scraperConfigs = {
  huurwoningen: {
    baseUrl: "https://www.huurwoningen.nl",
    timeout: 30000,
    retries: 3,
    delayMin: 2000,
    delayMax: 5000,
    
    cookieSettings: {
      name: "cookie_consent",
      value: "accepted",
      domain: ".huurwoningen.nl",
      httpOnly: false,
      secure: true,
    },
    
    selectors: {
      // Main listing selectors
      listContainer: '.search-list .search-list__item, .property-list .property-item, .listing-item, .rental-item, article.listing, .card.property-card, [data-listing-id], [data-property-id]',
      title: 'h1, h2, h3, h4, .title, .property-title, .listing-title',
      price: '.price, .rental-price, .property-price, .listing-price, [class*="price"]',
      location: '.location, .address, .property-address, .listing-address, [class*="location"], [class*="address"]',
      url: 'a[href*="/huren/"]',
      image: 'img[src], img[data-src], img[data-lazy-src]',
      
      // Detail page selectors
      detailSelectors: {
        description: [
          ".description",
          ".property-description", 
          ".listing-description",
          ".object-description",
          ".detail-description",
          ".property-details .description",
          ".listing-details .description",
          ".property-info .description",
          "[class*='description']",
          ".property-text",
          ".listing-text",
          ".object-text",
          ".detail-text",
          ".property-content",
          ".listing-content",
          ".object-content",
          ".detail-content",
          ".property-details p",
          ".listing-details p", 
          ".object-details p",
          ".detail-section p",
          ".property-info p",
          ".listing-info p",
          "main p",
          "article p",
          ".content p"
        ],
        features: [
          ".property-features", 
          ".property-info", 
          ".property-details",
          ".listing-features",
          ".listing-details",
          ".object-features",
          ".object-details",
          ".detail-features",
          ".specifications",
          ".amenities"
        ],
        images: [
          '.carrousel__track img',
          '.media-viewer img',
          '.slider-wrapper img',
          '.property-photos img',
          '.property-gallery img',
          '.property-slider img',
          '.listing-photos img',
          '.listing-gallery img',
          '.photo-gallery img',
          '.thumbnails img',
          '.property-thumbnails img',
          '.gallery-thumbnails img',
          '.property-media img',
          '.listing-media img',
          '.property img[src*="media"]',
          '.listing img[src*="media"]',
          'img[src*="huurwoningen"][src*="media"]',
          'img[data-src*="huurwoningen"][data-src*="media"]'
        ]
      }
    }
  },
  
  funda: {
    baseUrl: "https://www.funda.nl",
    timeout: 30000,
    retries: 3,
    delayMin: 3000,
    delayMax: 7000,
    
    selectors: {
      listContainer: 'ol[data-test-id="search-results"] > li',
      title: 'h2[data-test-id="street-name-house-number"]',
      price: 'p[data-test-id="price"]',
      location: 'p[data-test-id="postal-code-city"]',
      url: 'a[data-test-id="object-image-link"]',
      
      detailSelectors: {
        description: [
          '[data-test-id="object-description"]',
          '.object-description',
          '.property-description',
          '.description'
        ]
      }
    }
  },
  
  pararius: {
    baseUrl: "https://www.pararius.nl",
    timeout: 30000,
    retries: 3,
    delayMin: 2000,
    delayMax: 4000,
    
    selectors: {
      listContainer: ".search-list__item--listing",
      title: ".listing-search-item__title",
      price: ".listing-search-item__price",
      location: ".listing-search-item__location",
      url: "a",
      
      detailSelectors: {
        description: [
          '.listing-detail-description',
          '.property-description',
          '.description'
        ]
      }
    }
  }
};

/**
 * Get configuration for a specific scraper
 * @param {string} scraperName - Name of the scraper (huurwoningen, funda, pararius)
 * @returns {object} Scraper configuration
 */
function getScraperConfig(scraperName) {
  const config = scraperConfigs[scraperName];
  if (!config) {
    throw new Error(`No configuration found for scraper: ${scraperName}`);
  }
  return { ...config }; // Return a copy to prevent mutations
}

/**
 * Get all available scraper names
 * @returns {string[]} Array of scraper names
 */
function getAvailableScrapers() {
  return Object.keys(scraperConfigs);
}

module.exports = {
  scraperConfigs,
  getScraperConfig,
  getAvailableScrapers
};