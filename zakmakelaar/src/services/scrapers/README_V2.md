# Enhanced Scraper System V2

## Overview

The scraper system has been completely refactored with a modern, maintainable architecture that addresses all the issues identified in the original implementation.

## 🏗️ Architecture

### Base Classes
- **BaseScraper**: Abstract base class providing common functionality
- **ScraperService**: Main orchestration service with health monitoring
- **PerformanceMonitor**: Comprehensive metrics and alerting system

### Site-Specific Scrapers
- **FundaScraperV2**: Enhanced Funda scraper
- **ParariusScraperV2**: Enhanced Pararius scraper  
- **HuurwoningenScraperV2**: Enhanced Huurwoningen scraper

### Supporting Systems
- **Error Handling**: Classified errors with recovery strategies
- **Rate Limiting**: Intelligent delays and anti-detection
- **Configuration Management**: Centralized selector and config management
- **Performance Monitoring**: Real-time metrics and alerting

## 🚀 Key Improvements

### 1. Modular Architecture
```javascript
// Old way - monolithic functions
await scrapeFunda();

// New way - object-oriented with inheritance
const scraper = new FundaScraper();
const result = await scraper.scrape();
```

### 2. Centralized Configuration
```javascript
// Selectors and configs are now centralized
const config = getScraperConfig('funda');
console.log(config.selectors.price); // Array of price selectors
```

### 3. Intelligent Error Handling
```javascript
// Errors are classified and handled appropriately
try {
  await scraper.scrape();
} catch (error) {
  const classified = ErrorClassifier.classify(error, 'funda');
  const recovery = await ErrorRecovery.handleError(classified);
  // Automatic retry with appropriate strategy
}
```

### 4. Smart Rate Limiting
```javascript
// Automatic rate limiting based on site behavior
await rateLimiter.waitForDelay('funda'); // Intelligent delay
rateLimiter.recordError('funda', error); // Increases backoff
```

### 5. Performance Monitoring
```javascript
// Real-time performance tracking
const sessionId = performanceMonitor.startSession('funda');
performanceMonitor.recordPageMetrics(sessionId, metrics);
const summary = performanceMonitor.getPerformanceSummary();
```

## 📊 Usage Examples

### Basic Usage
```javascript
const { scraperService } = require('./ScraperServiceV2');

// Scrape a single site
const result = await scraperService.scrapeSite('funda');

// Scrape all sites
const results = await scraperService.scrapeAll();

// Get system health
const health = scraperService.getSystemHealth();
```

### Advanced Usage
```javascript
// Custom options
const result = await scraperService.scrapeSite('funda', {
  maxPages: 10,
  timeout: 45000
});

// Monitor specific scraper
const status = scraperService.getScraperStatus('pararius');
console.log(status.rateLimitStats);
console.log(status.errorStats);
```

### Error Recovery
```javascript
// Automatic error classification and recovery
try {
  await scraperService.scrapeSite('huurwoningen');
} catch (error) {
  if (error.retryable) {
    // System will automatically retry with backoff
    console.log('Will retry automatically');
  } else {
    // Manual intervention needed
    console.log('Requires manual fix:', error.message);
  }
}
```

## 🔧 Configuration

### Site Configuration
```javascript
// configs/scraperConfigs.js
const scraperConfigs = {
  funda: {
    baseUrl: 'https://www.funda.nl',
    selectors: {
      listingContainer: '[data-test-id="search-result-item"]',
      price: ['[data-test-id="price-rent"]', '.object-header__price'],
      // ... more selectors
    },
    searchParams: {
      cities: ['amsterdam', 'rotterdam'],
      priceRanges: [{ min: 0, max: 1000 }]
    }
  }
};
```

### Rate Limiting Configuration
```javascript
// utils/RateLimiter.js
const siteConfigs = {
  funda: {
    baseDelay: { min: 3000, max: 8000 },
    requestsPerMinute: 8,
    burstLimit: 3
  }
};
```

## 📈 Monitoring & Alerts

### Performance Metrics
- **Success Rate**: Percentage of successful scrapes
- **Response Time**: Average and max response times
- **Memory Usage**: Memory consumption tracking
- **Error Rate**: Error frequency and types
- **Throughput**: Listings per minute

### Alert Types
- **HIGH_ERROR_RATE**: Error rate > 30%
- **LOW_SUCCESS_RATE**: Success rate < 70%
- **HIGH_RESPONSE_TIME**: Response time > 30s
- **HIGH_MEMORY_USAGE**: Memory usage > 500MB

### Health Check
```javascript
const health = scraperService.getSystemHealth();
console.log(health.systemStatus); // HEALTHY, WARNING, CRITICAL
console.log(health.performance);
console.log(health.alerts);
```

## 🛠️ Maintenance

### Updating Selectors
When sites change their HTML structure:

1. Update selectors in `configs/scraperConfigs.js`
2. Test with a single scraper
3. Deploy configuration update

### Adding New Sites
1. Create new scraper class extending `BaseScraper`
2. Add configuration to `scraperConfigs.js`
3. Register in `ScraperService`

### Performance Tuning
- Adjust rate limiting in `RateLimiter.js`
- Modify error thresholds in `PerformanceMonitor.js`
- Update retry strategies in `ErrorRecovery.js`

## 🔄 Migration from V1

### Backward Compatibility
The new system maintains backward compatibility:

```javascript
// Old API still works
const { scrapeFunda } = require('./scraper');
await scrapeFunda();

// But new API is recommended
const { scraperService } = require('./ScraperServiceV2');
await scraperService.scrapeSite('funda');
```

### Migration Steps
1. Test new scrapers alongside old ones
2. Gradually migrate API calls
3. Monitor performance during transition
4. Remove old scrapers once stable

## 🚨 Troubleshooting

### Common Issues

**High Error Rate**
- Check if site structure changed
- Verify rate limiting isn't too aggressive
- Review error logs for patterns

**Low Success Rate**
- Check network connectivity
- Verify selectors are still valid
- Review anti-detection measures

**Memory Issues**
- Check for memory leaks in browser pool
- Verify pages are being closed properly
- Monitor image/resource loading

### Debug Mode
```javascript
// Enable detailed logging
process.env.SCRAPER_DEBUG = 'true';

// Get detailed session metrics
const metrics = performanceMonitor.getSessionMetrics(sessionId);
console.log(JSON.stringify(metrics, null, 2));
```

## 📝 API Reference

### ScraperService Methods
- `scrapeSite(siteName, options)` - Scrape single site
- `scrapeAll(options)` - Scrape all sites
- `getScraperStatus(siteName)` - Get scraper status
- `stopScraper(siteName)` - Stop running scraper
- `getSystemHealth()` - Get system health
- `exportMetrics(format)` - Export performance metrics

### Performance Monitor Methods
- `startSession(siteName)` - Start monitoring session
- `recordPageMetrics(sessionId, metrics)` - Record page metrics
- `endSession(sessionId, status)` - End monitoring session
- `getPerformanceSummary()` - Get performance summary
- `getAlerts()` - Get recent alerts

### Rate Limiter Methods
- `waitForDelay(siteName)` - Wait for appropriate delay
- `recordError(siteName, error)` - Record error for backoff
- `getStats(siteName)` - Get rate limiting stats
- `resetBackoff(siteName)` - Reset backoff level

## 🎯 Benefits

1. **Maintainability**: Modular, object-oriented design
2. **Reliability**: Robust error handling and recovery
3. **Performance**: Intelligent rate limiting and monitoring
4. **Scalability**: Easy to add new sites and features
5. **Observability**: Comprehensive metrics and alerting
6. **Flexibility**: Configurable selectors and parameters

The new system is production-ready and provides a solid foundation for reliable, scalable web scraping operations.
