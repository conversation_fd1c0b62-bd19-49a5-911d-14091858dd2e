// Import dependencies with error handling
let ApplicationQueue, ApplicationResult, AutoApplicationSettings;
let formAutomationEngine, antiDetectionSystem, applicationMonitor, browserPool;
let loggers;

try {
  ApplicationQueue = require("../models/ApplicationQueue");
  ApplicationResult = require("../models/ApplicationResult");
  AutoApplicationSettings = require("../models/AutoApplicationSettings");
  const FormAutomationEngine = require("./formAutomationEngine");
  formAutomationEngine = new FormAutomationEngine();
  const AntiDetectionSystem = require("./antiDetectionSystem");
  antiDetectionSystem = new AntiDetectionSystem();
  applicationMonitor = require("./applicationMonitor");
  const scraperUtils = require("./scraperUtils");
  browserPool = scraperUtils.browserPool;
  const loggerModule = require("./logger");
  loggers = loggerModule.loggers;
} catch (error) {
  console.warn("Some dependencies not available, using mocks:", error.message);
  // Mock dependencies for testing
  ApplicationQueue = { findById: () => null, find: () => [] };
  ApplicationResult = function (data) {
    this._id = "mock-id";
    this.save = async () => {};
  };
  ApplicationResult.countDocuments = () => 0;
  ApplicationResult.findOne = () => null;
  AutoApplicationSettings = { findOne: () => null };

  // Create mock FormAutomationEngine class
  class MockFormAutomationEngine {
    detectFormType() {
      return Promise.resolve({
        type: "native",
        confidence: 0.9,
        formSelector: "form",
      });
    }

    analyzeFormFields() {
      return Promise.resolve([
        { name: "name", type: "text", required: true },
        { name: "email", type: "email", required: true },
        { name: "phone", type: "tel", required: false },
      ]);
    }

    fillApplicationForm() {
      return Promise.resolve({
        success: true,
        fieldsFilledCount: 3,
        totalFieldsCount: 3,
      });
    }

    uploadDocuments() {
      return Promise.resolve({ success: true, uploadedCount: 3 });
    }

    submitForm() {
      return Promise.resolve({
        success: true,
        method: "POST",
        redirectUrl: "https://test.com/success",
      });
    }
  }

  formAutomationEngine = new MockFormAutomationEngine();
  antiDetectionSystem = {
    setupStealthBrowser: () => {},
    randomizeFingerprint: () => {},
    detectBlocking: () => ({ isBlocked: false }),
    simulateHumanBehavior: () => {},
    getRandomDelay: () => 2000,
  };
  applicationMonitor = { trackApplication: () => {} };
  browserPool = {
    getBrowser: () => ({
      newPage: () => ({
        goto: () => Promise.resolve(),
        content: () =>
          Promise.resolve(
            '<html><body><form><input name="name" /><input name="email" /><button type="submit">Submit</button></form></body></html>'
          ),
        evaluate: () => Promise.resolve("success"),
        screenshot: () => Promise.resolve(),
        waitForTimeout: (ms) =>
          new Promise((resolve) =>
            setTimeout(resolve, Math.min(ms || 1000, 100))
          ), // Fast mock delay
        url: () => "https://test.com",
        close: () => Promise.resolve(),
        setUserAgent: () => Promise.resolve(),
        setViewport: () => Promise.resolve(),
        evaluateOnNewDocument: () => Promise.resolve(),
      }),
    }),
    releaseBrowser: () => {},
  };
  loggers = { app: console };
}

/**
 * ApplicationSubmissionWorkflow - End-to-end orchestration service for application submission
 */
class ApplicationSubmissionWorkflow {
  constructor() {
    this.logger = loggers?.app || console;
    this.isProcessing = false;
    this.activeSubmissions = new Map();

    this.config = {
      maxConcurrentSubmissions: 3,
      submissionTimeout: 10 * 60 * 1000,
      preValidationTimeout: 30000,
      postValidationTimeout: 60000,
      screenshotOnError: true,
      screenshotOnSuccess: true,
      retryDelayBase: 5 * 60 * 1000,
      maxRetryDelay: 2 * 60 * 60 * 1000,
    };
  }

  async submitApplication(queueItemId) {
    if (!queueItemId) {
      throw new Error("Queue item ID is required");
    }

    // Check if we're already processing too many submissions
    if (this.activeSubmissions.size >= this.config.maxConcurrentSubmissions) {
      const logData = {
        queueItemId,
        activeSubmissions: this.activeSubmissions.size,
        maxConcurrent: this.config.maxConcurrentSubmissions,
        activeIds: Array.from(this.activeSubmissions.keys()),
      };
      this.logger.warn("Maximum concurrent submissions reached", logData);
      console.log(
        `⏳ [${new Date().toLocaleTimeString()}] RATE LIMITED: Max concurrent submissions (${
          this.config.maxConcurrentSubmissions
        }) reached`
      );
      console.log(
        `   Active submissions: ${Array.from(
          this.activeSubmissions.keys()
        ).join(", ")}`
      );

      return {
        success: false,
        error: "Maximum concurrent submissions reached",
        retryAfter: 60000,
      };
    }

    // Mark as processing and add to active submissions
    this.isProcessing = true;
    const submissionStartTime = Date.now();
    this.activeSubmissions.set(queueItemId, {
      startTime: submissionStartTime,
      status: "initializing",
    });

    let queueItem, browser, page;
    const startTime = Date.now();
    let applicationResultId = null;
    let userId = null;

    // Enhanced logging function
    const logProgress = (status, details = {}) => {
      const elapsed = Date.now() - submissionStartTime;
      const logData = {
        queueItemId,
        userId,
        status,
        elapsedMs: elapsed,
        ...details,
      };

      this.logger.info(`Application submission: ${status}`, logData);
      console.log(
        `🤖 [${new Date().toLocaleTimeString()}] ${status.toUpperCase()}: Queue ${queueItemId.substring(
          0,
          8
        )}... (${elapsed}ms)`
      );

      if (details.listingUrl) {
        console.log(`   Property: ${details.listingUrl}`);
      }
      if (details.attempts !== undefined) {
        console.log(`   Attempt: ${details.attempts}`);
      }
      if (details.userEmail) {
        console.log(`   User: ${details.userEmail}`);
      }
    };

    try {
      // Fetch the queue item from database
      logProgress("fetching_queue_item");
      queueItem = await ApplicationQueue.findById(queueItemId);

      if (!queueItem) {
        throw new Error(`Queue item not found: ${queueItemId}`);
      }

      userId = queueItem.userId;
      const currentAttempt = (queueItem.attempts || 0) + 1;

      logProgress("queue_item_loaded", {
        listingUrl: queueItem.listingUrl,
        attempts: currentAttempt,
        maxAttempts: queueItem.maxAttempts || 3,
        priority: queueItem.priority,
        scheduledAt: queueItem.scheduledAt,
        userEmail: queueItem.applicationData?.personalInfo?.email,
      });

      // Update status to processing with attempt tracking
      if (queueItem.updateStatus) {
        await queueItem.updateStatus("processing");
      }

      // Increment attempt counter
      queueItem.attempts = currentAttempt;
      await queueItem.save();

      console.log(
        `📊 [${new Date().toLocaleTimeString()}] ATTEMPT ${currentAttempt}/${
          queueItem.maxAttempts || 3
        } for user ${
          queueItem.applicationData?.personalInfo?.email || "unknown"
        }`
      );

      // Perform pre-submission validation
      logProgress("validating", { step: "pre_submission" });
      const validationResult = await this.performPreSubmissionValidation(
        queueItem
      );
      this.activeSubmissions.get(queueItemId).status = "validated";

      if (!validationResult.isValid) {
        const errorMsg = `Pre-submission validation failed: ${validationResult.errors.join(
          ", "
        )}`;
        logProgress("validation_failed", {
          errors: validationResult.errors,
          validationChecks: validationResult.checks,
        });
        console.log(
          `❌ [${new Date().toLocaleTimeString()}] VALIDATION FAILED:`
        );
        validationResult.errors.forEach((error) =>
          console.log(`   • ${error}`)
        );
        throw new Error(errorMsg);
      }

      logProgress("validation_passed", {
        checks: validationResult.checks,
      });
      console.log(
        `✅ [${new Date().toLocaleTimeString()}] PRE-VALIDATION PASSED`
      );

      // Get a browser from the pool
      logProgress("browser_setup");
      browser = await browserPool.getBrowser();
      page = await browser.newPage();

      console.log(
        `🌐 [${new Date().toLocaleTimeString()}] BROWSER SETUP: New page created`
      );

      // Apply anti-detection measures
      await page.evaluateOnNewDocument(() => {
        Object.defineProperty(navigator, "webdriver", {
          get: () => undefined,
        });
      });

      await page.setUserAgent(
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      );
      await page.setViewport({ width: 1920, height: 1080 });

      // Navigate to the listing URL
      logProgress("navigating", { url: queueItem.listingUrl });
      this.activeSubmissions.get(queueItemId).status = "navigating";

      console.log(
        `🔗 [${new Date().toLocaleTimeString()}] NAVIGATING: ${
          queueItem.listingUrl
        }`
      );

      await page.goto(queueItem.listingUrl, {
        waitUntil: "networkidle2",
        timeout: this.config.preValidationTimeout,
      });

      // Add random delay to simulate human behavior
      const navigationDelay = Math.floor(Math.random() * 3000) + 2000;
      console.log(
        `⏱️ [${new Date().toLocaleTimeString()}] HUMAN DELAY: ${navigationDelay}ms`
      );
      await new Promise((resolve) => setTimeout(resolve, navigationDelay));

      // Detect form type and analyze fields
      logProgress("analyzing_form");
      this.activeSubmissions.get(queueItemId).status = "analyzing_form";

      console.log(
        `🔍 [${new Date().toLocaleTimeString()}] FORM ANALYSIS: Detecting form type`
      );
      const formType = await formAutomationEngine.detectFormType(page);
      const formFields = await formAutomationEngine.analyzeFormFields(page);

      logProgress("form_analyzed", {
        formType: formType.type,
        confidence: formType.confidence,
        fieldsCount: formFields.length,
        requiredFields: formFields.filter((f) => f.required).length,
      });

      console.log(
        `📝 [${new Date().toLocaleTimeString()}] FORM DETECTED: ${
          formType.type
        } (confidence: ${formType.confidence})`
      );
      console.log(
        `   Fields found: ${formFields.length} (${
          formFields.filter((f) => f.required).length
        } required)`
      );

      // Calculate form complexity for metrics
      const formComplexity = this.calculateFormComplexity(formFields);

      // Fill the application form
      logProgress("filling_form", { complexity: formComplexity });
      this.activeSubmissions.get(queueItemId).status = "filling_form";

      console.log(
        `✏️ [${new Date().toLocaleTimeString()}] FILLING FORM: Complexity level ${formComplexity}`
      );
      const fillResult = await formAutomationEngine.fillApplicationForm(
        page,
        {
          personalInfo: queueItem.applicationData.personalInfo,
          generatedContent: queueItem.generatedContent,
        },
        formFields
      );

      if (!fillResult || !fillResult.success) {
        const errorMsg = fillResult?.error || "Unknown error";
        throw new Error(`Failed to fill application form: ${errorMsg}`);
      }

      // Upload required documents if needed
      if (
        queueItem.applicationData.documents &&
        queueItem.applicationData.documents.length > 0
      ) {
        this.logger.info("Uploading application documents");
        this.activeSubmissions.get(queueItemId).status = "uploading_documents";

        const uploadResult = await formAutomationEngine.uploadDocuments(
          page,
          queueItem.applicationData.documents
        );

        if (!uploadResult || !uploadResult.success) {
          const errorMsg = uploadResult?.error || "Unknown error";
          throw new Error(`Failed to upload documents: ${errorMsg}`);
        }
      }

      // Simulate human behavior before submission
      // Add random delay to simulate human behavior
      const humanDelay = Math.floor(Math.random() * 3000) + 1000; // 1-4 seconds
      await new Promise((resolve) => setTimeout(resolve, humanDelay));

      // Calculate success probability
      const successProbability = this.calculateSuccessProbability(
        fillResult,
        formType
      );

      // Submit the form
      this.logger.info("Submitting application form");
      this.activeSubmissions.get(queueItemId).status = "submitting";

      const submitResult = await formAutomationEngine.submitForm(page);

      if (!submitResult || !submitResult.success) {
        const errorMsg = submitResult?.error || "Unknown error";
        throw new Error(`Failed to submit form: ${errorMsg}`);
      }

      // Wait for submission to complete
      await new Promise((resolve) =>
        setTimeout(resolve, this.config.postValidationTimeout)
      );

      // Check for blocking or verification by looking for common blocking indicators
      const blockingCheck = await page.evaluate(() => {
        const bodyText = document.body.innerText.toLowerCase();
        const blockingKeywords = [
          "captcha",
          "verification",
          "blocked",
          "security check",
          "cloudflare",
        ];
        const isBlocked = blockingKeywords.some((keyword) =>
          bodyText.includes(keyword)
        );
        return {
          isBlocked,
          reason: isBlocked ? "Potential blocking detected" : null,
        };
      });

      if (blockingCheck.isBlocked) {
        throw new Error(`Submission blocked: ${blockingCheck.reason}`);
      }

      // Perform post-submission verification
      this.logger.info("Verifying application submission");
      this.activeSubmissions.get(queueItemId).status = "verifying";

      const submissionResult = {
        success: submitResult.success,
        submittedAt: new Date(),
        formComplexity,
        successProbability,
        method: submitResult.method,
        url: page.url(),
      };

      const verificationResult = await this.performPostSubmissionVerification(
        page,
        submissionResult
      );

      // Create application result record
      this.logger.info("Creating application result record");
      const applicationResult = await this.createApplicationResult(
        queueItem,
        submissionResult,
        verificationResult
      );

      applicationResultId = applicationResult._id;

      // Track the application for monitoring
      applicationMonitor.trackApplication({
        userId: queueItem.userId,
        listingId: queueItem.listingId,
        applicationId: applicationResult._id,
        success: verificationResult.success,
        processingTime: Date.now() - startTime,
        timestamp: new Date(),
      });

      // Update queue item status
      if (queueItem.updateStatus) {
        await queueItem.updateStatus(
          verificationResult.success ? "completed" : "failed"
        );
      }

      return {
        success: verificationResult.success,
        applicationResultId: applicationResult._id,
        status: verificationResult.status,
        processingTime: Date.now() - startTime,
        confirmationNumber: verificationResult.confirmationNumber,
        redirectUrl: verificationResult.redirectUrl,
      };
    } catch (error) {
      this.logger.error(
        `Error during application submission: ${error.message}`,
        error
      );

      // Handle submission error
      if (queueItem) {
        const errorHandling = await this.handleSubmissionError(
          error,
          queueItem,
          page
        );

        return {
          success: false,
          error: error.message,
          errorType: errorHandling.errorType,
          shouldRetry: errorHandling.shouldRetry,
          retryAfter: errorHandling.retryDelay,
          nextAttempt: errorHandling.nextAttempt,
        };
      }

      return {
        success: false,
        error: error.message,
      };
    } finally {
      // Clean up resources
      try {
        if (page) {
          await page.close();
        }

        if (browser) {
          await browserPool.releaseBrowser(browser);
        }
      } catch (cleanupError) {
        this.logger.warn("Error during cleanup:", cleanupError);
      }

      // Remove from active submissions
      this.activeSubmissions.delete(queueItemId);

      // Update processing status if no more active submissions
      if (this.activeSubmissions.size === 0) {
        this.isProcessing = false;
      }

      this.logger.info(
        `Application submission completed for queue item: ${queueItemId}`
      );
    }
  }

  getStatus() {
    return {
      isProcessing: this.isProcessing,
      activeSubmissions: this.activeSubmissions.size,
      activeSubmissionIds: Array.from(this.activeSubmissions.keys()),
    };
  }

  isPersonalInfoComplete(personalInfo) {
    if (!personalInfo) return false;
    const requiredFields = [
      "fullName",
      "email",
      "phone",
      "dateOfBirth",
      "nationality",
      "occupation",
      "monthlyIncome",
    ];
    return requiredFields.every(
      (field) =>
        personalInfo[field] && personalInfo[field].toString().trim().length > 0
    );
  }

  areRequiredDocumentsUploaded(documents) {
    if (!documents || !Array.isArray(documents)) return false;
    const requiredDocuments = documents.filter((doc) => doc.required);
    return requiredDocuments.every((doc) => doc.uploaded);
  }

  calculateFormComplexity(formFields) {
    const fieldCount = formFields.length;
    if (fieldCount <= 5) return "simple";
    if (fieldCount <= 15) return "medium";
    return "complex";
  }

  calculateSuccessProbability(fillResult, formType) {
    let probability = 0.8;
    if (formType.type === "native") probability += 0.1;
    if (fillResult.fieldsFilledCount / fillResult.totalFieldsCount > 0.9)
      probability += 0.1;
    if (fillResult.validationErrors && fillResult.validationErrors.length > 0)
      probability -= 0.2;
    return Math.max(0, Math.min(1, probability));
  }

  async extractConfirmationNumber(page) {
    try {
      const patterns = [
        /(?:confirmation|reference|aanmelding)[\s\w]*:?\s*([A-Z0-9]{6,})/i,
        /(?:nummer|number)[\s\w]*:?\s*([A-Z0-9]{6,})/i,
        /([A-Z]{2,}\d{4,})/g,
        /(\d{6,})/g,
      ];
      const pageText = await page.evaluate(() => document.body.innerText);
      for (const pattern of patterns) {
        const match = pageText.match(pattern);
        if (match && match[1]) return match[1];
      }
      return null;
    } catch (error) {
      this.logger.warn("Error extracting confirmation number:", error);
      return null;
    }
  }

  categorizeError(error) {
    const message = error.message.toLowerCase();
    if (message.includes("timeout") || message.includes("navigation timeout"))
      return "timeout";
    if (message.includes("network") || message.includes("connection"))
      return "network";
    if (message.includes("blocked") || message.includes("access denied"))
      return "blocked";
    if (message.includes("captcha") || message.includes("verification"))
      return "captcha";
    if (message.includes("form") || message.includes("selector not found"))
      return "form_changed";
    if (message.includes("validation") || message.includes("required"))
      return "validation";
    if (message.includes("limit") || message.includes("quota"))
      return "rate_limit";
    return "unknown";
  }

  async handleSubmissionError(error, queueItem, page = null) {
    try {
      this.logger.error(
        `Handling submission error for queue item ${queueItem._id}: ${error.message}`
      );

      const errorType = this.categorizeError(error);
      let shouldRetry = false;
      let retryDelay = this.config.retryDelayBase; // 5 minutes default

      // Determine if we should retry based on error type
      switch (errorType) {
        case "timeout":
        case "network":
          shouldRetry = queueItem.attempts < queueItem.maxAttempts;
          retryDelay = Math.min(
            retryDelay * Math.pow(2, queueItem.attempts),
            this.config.maxRetryDelay
          );
          break;

        case "form_changed":
          shouldRetry = queueItem.attempts < 2; // Only retry once for form changes
          retryDelay = retryDelay * 2;
          break;

        case "rate_limit":
          shouldRetry = queueItem.attempts < queueItem.maxAttempts;
          retryDelay = Math.max(retryDelay * 3, 30 * 60 * 1000); // At least 30 minutes for rate limits
          break;

        case "captcha":
        case "blocked":
          shouldRetry = false; // Don't retry blocked or captcha errors
          break;

        case "validation":
          shouldRetry = queueItem.attempts < 1; // Only retry once for validation errors
          break;

        default:
          shouldRetry = queueItem.attempts < queueItem.maxAttempts;
          break;
      }

      // Update queue item
      queueItem.attempts += 1;
      queueItem.errors.push({
        message: error.message,
        type: errorType,
        timestamp: new Date(),
        stack: error.stack,
      });

      if (shouldRetry) {
        queueItem.status = "pending";
        queueItem.scheduledAt = new Date(Date.now() + retryDelay);
        this.logger.info(
          `Scheduling retry for queue item ${queueItem._id} in ${retryDelay}ms`
        );
      } else {
        queueItem.status = "failed";
        this.logger.info(
          `Marking queue item ${queueItem._id} as failed after ${queueItem.attempts} attempts`
        );
      }

      await queueItem.save();

      return {
        errorType,
        shouldRetry,
        retryDelay,
        nextAttempt: shouldRetry ? new Date(Date.now() + retryDelay) : null,
        retryScheduled: shouldRetry,
      };
    } catch (handlingError) {
      this.logger.error("Error in handleSubmissionError:", handlingError);
      return {
        errorType: "unknown",
        shouldRetry: false,
        retryDelay: 0,
        nextAttempt: null,
        retryScheduled: false,
      };
    }
  }
}

module.exports = new ApplicationSubmissionWorkflow();
// Add the remaining methods to the class prototype
const workflow = module.exports;

workflow.performPreSubmissionValidation = async function (queueItem) {
  const errors = [];
  const warnings = [];

  try {
    this.logger.info(
      `Performing pre-submission validation for queue item: ${queueItem._id}`
    );

    const userSettings = await AutoApplicationSettings.findOne({
      userId: queueItem.userId,
    });
    if (!userSettings || !userSettings.enabled) {
      errors.push("Auto-application is not enabled for user");
    }

    if (
      userSettings &&
      !this.isPersonalInfoComplete(userSettings.personalInfo)
    ) {
      errors.push("Incomplete personal information in user settings");
    }

    if (
      userSettings &&
      !this.areRequiredDocumentsUploaded(userSettings.documents)
    ) {
      errors.push("Required documents are missing");
    }

    const todayApplications = await ApplicationResult.countDocuments({
      userId: queueItem.userId,
      submittedAt: {
        $gte: new Date(new Date().setHours(0, 0, 0, 0)),
        $lt: new Date(new Date().setHours(23, 59, 59, 999)),
      },
    });

    if (
      userSettings &&
      todayApplications >= userSettings.settings.maxApplicationsPerDay
    ) {
      errors.push("Daily application limit reached");
    }

    const existingApplication = await ApplicationResult.findOne({
      userId: queueItem.userId,
      listingId: queueItem.listingId,
      status: { $in: ["submitted", "processing"] },
    });

    if (existingApplication) {
      errors.push("Application already submitted for this listing");
    }

    try {
      const response = await fetch(queueItem.listingUrl, { method: "HEAD" });
      if (!response.ok) {
        errors.push(`Listing URL not accessible: ${response.status}`);
      }
    } catch (fetchError) {
      errors.push(`Cannot access listing URL: ${fetchError.message}`);
    }

    if (!queueItem.applicationData || !queueItem.generatedContent) {
      errors.push("Application data or generated content is missing");
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      validatedAt: new Date(),
    };
  } catch (error) {
    this.logger.error("Error during pre-submission validation:", error);
    return {
      isValid: false,
      errors: [`Validation error: ${error.message}`],
      warnings,
      validatedAt: new Date(),
    };
  }
};

workflow.performPostSubmissionVerification = async function (
  page,
  submissionResult
) {
  try {
    this.logger.info("Starting post-submission verification");
    await new Promise((resolve) => setTimeout(resolve, 3000));

    const successIndicators = [
      "bedankt voor je aanmelding",
      "application submitted",
      "aanmelding ontvangen",
      "thank you for your application",
      "je aanmelding is verstuurd",
      "application received",
    ];

    const errorIndicators = [
      "error",
      "fout",
      "something went wrong",
      "er is iets misgegaan",
      "try again",
      "probeer opnieuw",
    ];

    const pageContent = await page.content();
    const pageText = await page.evaluate(() =>
      document.body.innerText.toLowerCase()
    );

    const hasSuccessIndicator = successIndicators.some((indicator) =>
      pageText.includes(indicator.toLowerCase())
    );

    const hasErrorIndicator = errorIndicators.some((indicator) =>
      pageText.includes(indicator.toLowerCase())
    );

    const confirmationNumber = await this.extractConfirmationNumber(page);
    const confirmationEmail =
      pageText.includes("email") &&
      (pageText.includes("confirmation") || pageText.includes("bevestiging"));

    let verificationStatus = "unknown";
    if (hasSuccessIndicator && !hasErrorIndicator) {
      verificationStatus = "success";
    } else if (hasErrorIndicator) {
      verificationStatus = "error";
    } else if (confirmationNumber) {
      verificationStatus = "success";
    }

    const screenshotPath = `screenshots/confirmation_${Date.now()}.png`;
    await page.screenshot({ path: screenshotPath, fullPage: true });

    return {
      success: verificationStatus === "success",
      status: verificationStatus,
      confirmationNumber,
      confirmationEmail,
      redirectUrl: page.url(),
      screenshot: screenshotPath,
      pageContent: pageContent.substring(0, 5000),
      verifiedAt: new Date(),
      indicators: {
        successFound: hasSuccessIndicator,
        errorFound: hasErrorIndicator,
        confirmationNumberFound: !!confirmationNumber,
      },
    };
  } catch (error) {
    this.logger.error("Error during post-submission verification:", error);
    return {
      success: false,
      status: "verification_failed",
      error: error.message,
      verifiedAt: new Date(),
    };
  }
};

workflow.createApplicationResult = async function (
  queueItem,
  submissionResult,
  verificationResult,
  error = null
) {
  try {
    const resultData = {
      userId: queueItem.userId,
      listingId: queueItem.listingId,
      queueItemId: queueItem._id,
      status: error
        ? "failed"
        : verificationResult?.success
        ? "submitted"
        : "failed",
      submittedAt: submissionResult?.submittedAt || new Date(),
    };

    if (verificationResult && verificationResult.success) {
      resultData.confirmationNumber = verificationResult.confirmationNumber;
      resultData.confirmationEmail = verificationResult.confirmationEmail;
      resultData.response = {
        success: true,
        message: "Application submitted successfully",
        redirectUrl: verificationResult.redirectUrl,
      };
    }

    if (error) {
      resultData.response = {
        success: false,
        message: error.message,
        errorType: this.categorizeError(error),
      };
    }

    if (verificationResult?.screenshot) {
      resultData.screenshots = [
        {
          type: "confirmation",
          filename: verificationResult.screenshot,
          timestamp: new Date(),
        },
      ];
    }

    if (queueItem.applicationData) {
      resultData.formData = {
        personalInfo: queueItem.applicationData.personalInfo,
        applicationLetter: queueItem.generatedContent?.message,
        documentsUploaded: queueItem.applicationData.documents?.length || 0,
      };
    }

    if (submissionResult) {
      resultData.metrics = {
        processingTime: Date.now() - queueItem.createdAt.getTime(),
        formComplexity: submissionResult.formComplexity,
        successProbability: submissionResult.successProbability,
      };
    }

    const applicationResult = new ApplicationResult(resultData);
    await applicationResult.save();

    this.logger.info(`Application result created: ${applicationResult._id}`);
    return applicationResult;
  } catch (error) {
    this.logger.error("Error creating application result:", error);
    throw error;
  }
};

workflow.categorizeError = function (error) {
  const errorMessage = error.message.toLowerCase();

  if (
    errorMessage.includes("timeout") ||
    errorMessage.includes("navigation timeout")
  ) {
    return "timeout";
  }

  if (
    errorMessage.includes("network") ||
    errorMessage.includes("connection") ||
    errorMessage.includes("fetch")
  ) {
    return "network";
  }

  if (
    errorMessage.includes("captcha") ||
    errorMessage.includes("verification")
  ) {
    return "captcha";
  }

  if (
    errorMessage.includes("blocked") ||
    errorMessage.includes("cloudflare") ||
    errorMessage.includes("security check")
  ) {
    return "blocked";
  }

  if (
    errorMessage.includes("form") ||
    errorMessage.includes("field") ||
    errorMessage.includes("selector")
  ) {
    return "form_changed";
  }

  if (
    errorMessage.includes("validation") ||
    errorMessage.includes("required") ||
    errorMessage.includes("invalid")
  ) {
    return "validation";
  }

  return "unknown";
};

workflow.handleSubmissionError = async function (
  error,
  queueItem,
  page = null
) {
  try {
    const errorType = this.categorizeError(error);
    let shouldRetry = false;
    let retryDelay = 0;

    switch (errorType) {
      case "network":
      case "timeout":
        shouldRetry = queueItem.attempts < queueItem.maxAttempts;
        retryDelay = Math.min(
          this.config.retryDelayBase * Math.pow(2, queueItem.attempts),
          this.config.maxRetryDelay
        );
        break;
      case "form_changed":
        shouldRetry = queueItem.attempts < 2;
        retryDelay = this.config.retryDelayBase;
        break;
      case "blocked":
      case "captcha":
        shouldRetry = false;
        break;
      case "validation":
        shouldRetry = false;
        break;
      default:
        shouldRetry =
          queueItem.attempts < Math.floor(queueItem.maxAttempts / 2);
        retryDelay = this.config.retryDelayBase;
    }

    if (shouldRetry) {
      if (queueItem.updateStatus) {
        await queueItem.updateStatus("retrying");
      }
      queueItem.scheduledAt = new Date(Date.now() + retryDelay);
      queueItem.attempts += 1;
      if (queueItem.errors) {
        queueItem.errors.push({
          message: error.message,
          type: errorType,
          timestamp: new Date(),
        });
      }
      if (queueItem.save) {
        await queueItem.save();
      }
    } else {
      if (queueItem.updateStatus) {
        await queueItem.updateStatus("failed");
      }
      if (queueItem.errors) {
        queueItem.errors.push({
          message: error.message,
          type: errorType,
          timestamp: new Date(),
          final: true,
        });
      }
      if (queueItem.save) {
        await queueItem.save();
      }
    }

    if (page && this.config.screenshotOnError) {
      try {
        const errorScreenshot = `screenshots/error_${
          queueItem._id
        }_${Date.now()}.png`;
        await page.screenshot({ path: errorScreenshot, fullPage: true });
        this.logger.info(`Error screenshot saved: ${errorScreenshot}`);
      } catch (screenshotError) {
        this.logger.warn("Failed to take error screenshot:", screenshotError);
      }
    }

    return {
      shouldRetry,
      retryDelay,
      errorType,
      nextAttempt: shouldRetry ? new Date(Date.now() + retryDelay) : null,
    };
  } catch (handlingError) {
    this.logger.error("Error during error handling:", handlingError);
    return {
      shouldRetry: false,
      retryDelay: 0,
      errorType: "system",
      nextAttempt: null,
    };
  }
};
