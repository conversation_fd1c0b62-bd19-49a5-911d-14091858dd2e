const User = require('../models/User');
const SocialMatch = require('../models/SocialMatch');
const Message = require('../models/Message');
const { loggers } = require('./logger');
const cacheService = require('./cacheService');

class SocialMatchingService {
  constructor() {
    this.compatibilityWeights = {
      ageRange: 0.15,
      gender: 0.10,
      occupation: 0.10,
      cleanliness: 0.20,
      noiseLevel: 0.15,
      socialLevel: 0.15,
      smokingTolerance: 0.05,
      petTolerance: 0.05,
      guestPolicy: 0.05
    };
  }

  /**
   * Find potential roommates for a user
   * Requirement 9.1, 9.2, 9.3
   */
  async findPotentialRoommates(userId, filters = {}) {
    try {
      loggers.app.info(`Finding potential roommates for user ${userId}`);
      
      // Check cache first
      const cacheKey = `roommates:${userId}:${JSON.stringify(filters)}`;
      const cachedMatches = await cacheService.get(cacheKey);
      if (cachedMatches) {
        return JSON.parse(cachedMatches);
      }

      const currentUser = await User.findById(userId);
      if (!currentUser || !currentUser.isEligibleForSocialMatching) {
        throw new Error('User not eligible for social matching');
      }

      // Get existing matches to exclude
      const existingMatches = await SocialMatch.find({
        $or: [{ user1: userId }, { user2: userId }]
      }).select('user1 user2');

      const excludedUserIds = existingMatches.map(match => 
        match.user1.toString() === userId.toString() ? match.user2 : match.user1
      );
      excludedUserIds.push(userId); // Exclude self

      // Build query for potential matches
      const matchQuery = {
        _id: { $nin: excludedUserIds },
        'profile.socialPreferences.isVisible': true,
        'profile.socialPreferences.lookingForRoommate': true,
        emailVerified: true
      };

      // Apply filters
      if (filters.userType) {
        matchQuery['profile.userType'] = { $in: Array.isArray(filters.userType) ? filters.userType : [filters.userType] };
      }

      if (filters.ageRange) {
        const currentYear = new Date().getFullYear();
        const minBirthYear = currentYear - filters.ageRange.max;
        const maxBirthYear = currentYear - filters.ageRange.min;
        matchQuery['profile.dateOfBirth'] = {
          $gte: new Date(minBirthYear, 0, 1),
          $lte: new Date(maxBirthYear, 11, 31)
        };
      }

      if (filters.location) {
        matchQuery['preferences.location'] = new RegExp(filters.location, 'i');
      }

      // Find potential matches
      const potentialMatches = await User.find(matchQuery)
        .select('profile preferences tenantScore')
        .limit(50);

      // Calculate compatibility scores
      const scoredMatches = await Promise.all(
        potentialMatches.map(async (match) => {
          const compatibilityScore = this._calculateCompatibilityScore(currentUser, match);
          const matchedCriteria = this._getMatchedCriteria(currentUser, match);
          
          return {
            user: match,
            compatibilityScore,
            matchedCriteria,
            tenantScoreGrade: match.tenantScoreGrade
          };
        })
      );

      // Sort by compatibility score and filter by minimum threshold
      const minThreshold = filters.minCompatibility || 60;
      const filteredMatches = scoredMatches
        .filter(match => match.compatibilityScore >= minThreshold)
        .sort((a, b) => b.compatibilityScore - a.compatibilityScore)
        .slice(0, filters.limit || 20);

      // Cache results for 15 minutes
      await cacheService.set(cacheKey, JSON.stringify(filteredMatches), 900);

      loggers.app.info(`Found ${filteredMatches.length} potential roommates for user ${userId}`);
      return filteredMatches;

    } catch (error) {
      loggers.app.error('Error finding potential roommates:', error);
      throw error;
    }
  }

  /**
   * Create a match between two users
   * Requirement 9.3, 9.4
   */
  async createMatch(initiatorId, targetId) {
    try {
      loggers.app.info(`Creating match between ${initiatorId} and ${targetId}`);

      // Validate users exist and are eligible
      const [initiator, target] = await Promise.all([
        User.findById(initiatorId),
        User.findById(targetId)
      ]);

      if (!initiator || !target) {
        throw new Error('One or both users not found');
      }

      if (!initiator.isEligibleForSocialMatching || !target.isEligibleForSocialMatching) {
        throw new Error('One or both users not eligible for social matching');
      }

      // Check if match already exists
      const existingMatch = await SocialMatch.existsBetweenUsers(initiatorId, targetId);
      if (existingMatch) {
        throw new Error('Match already exists between these users');
      }

      // Calculate compatibility
      const compatibilityScore = this._calculateCompatibilityScore(initiator, target);
      const matchedCriteria = this._getMatchedCriteria(initiator, target);

      // Create the match
      const socialMatch = new SocialMatch({
        user1: initiatorId,
        user2: targetId,
        compatibilityScore,
        matchedCriteria,
        initiatedBy: initiatorId,
        status: 'pending'
      });

      await socialMatch.save();

      // Clear cache for both users
      await this._clearUserMatchCache(initiatorId);
      await this._clearUserMatchCache(targetId);

      loggers.app.info(`Match created successfully with ID ${socialMatch._id}`);
      return socialMatch;

    } catch (error) {
      loggers.app.error('Error creating match:', error);
      throw error;
    }
  }

  /**
   * Respond to a match (accept/reject)
   * Requirement 9.4
   */
  async respondToMatch(matchId, userId, response) {
    try {
      loggers.app.info(`User ${userId} responding to match ${matchId} with ${response}`);

      const match = await SocialMatch.findById(matchId);
      if (!match) {
        throw new Error('Match not found');
      }

      // Verify user is part of this match
      if (match.user1.toString() !== userId.toString() && match.user2.toString() !== userId.toString()) {
        throw new Error('User not authorized to respond to this match');
      }

      // Verify match is in pending status
      if (match.status !== 'pending') {
        throw new Error('Match is not in pending status');
      }

      // Update match status
      match.status = response === 'accept' ? 'accepted' : 'rejected';
      match.respondedAt = new Date();
      match.lastInteraction = new Date();

      await match.save();

      // Clear cache for both users
      await this._clearUserMatchCache(match.user1);
      await this._clearUserMatchCache(match.user2);

      loggers.app.info(`Match ${matchId} ${response}ed by user ${userId}`);
      return match;

    } catch (error) {
      loggers.app.error('Error responding to match:', error);
      throw error;
    }
  }

  /**
   * Get matches for a user
   * Requirement 9.3
   */
  async getMatches(userId, status = null, page = 1, limit = 20) {
    try {
      loggers.app.info(`Getting matches for user ${userId}, status: ${status}`);

      const skip = (page - 1) * limit;
      const matches = await SocialMatch.findMatchesForUser(userId, status)
        .skip(skip)
        .limit(limit);

      const total = await SocialMatch.countDocuments({
        $or: [{ user1: userId }, { user2: userId }],
        ...(status && { status })
      });

      return {
        matches,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      loggers.app.error('Error getting matches:', error);
      throw error;
    }
  }

  /**
   * Update social preferences for a user
   * Requirement 9.1, 9.2
   */
  async updateSocialPreferences(userId, preferences) {
    try {
      loggers.app.info(`Updating social preferences for user ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Validate preferences
      this._validateSocialPreferences(preferences);

      // Update preferences
      if (preferences.lookingForRoommate !== undefined) {
        user.profile.socialPreferences.lookingForRoommate = preferences.lookingForRoommate;
      }

      if (preferences.isVisible !== undefined) {
        user.profile.socialPreferences.isVisible = preferences.isVisible;
      }

      if (preferences.roommateCriteria) {
        Object.assign(user.profile.socialPreferences.roommateCriteria, preferences.roommateCriteria);
      }

      await user.save();

      // Clear user's match cache
      await this._clearUserMatchCache(userId);

      loggers.app.info(`Social preferences updated for user ${userId}`);
      return user.profile.socialPreferences;

    } catch (error) {
      loggers.app.error('Error updating social preferences:', error);
      throw error;
    }
  }

  /**
   * Toggle visibility for social matching
   * Requirement 9.2
   */
  async toggleVisibility(userId, isVisible) {
    try {
      loggers.app.info(`Toggling visibility for user ${userId} to ${isVisible}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      user.profile.socialPreferences.isVisible = isVisible;
      await user.save();

      // Clear user's match cache
      await this._clearUserMatchCache(userId);

      loggers.app.info(`Visibility toggled for user ${userId}`);
      return user.profile.socialPreferences;

    } catch (error) {
      loggers.app.error('Error toggling visibility:', error);
      throw error;
    }
  }

  /**
   * Report a user for inappropriate behavior
   * Requirement 9.5, 9.6
   */
  async reportUser(reporterId, reportedId, reason, description = '') {
    try {
      loggers.app.info(`User ${reporterId} reporting user ${reportedId} for ${reason}`);

      // Find the match between users
      const match = await SocialMatch.existsBetweenUsers(reporterId, reportedId);
      if (!match) {
        throw new Error('No match exists between these users');
      }

      // Add report to the match
      match.reportedBy.push({
        user: reporterId,
        reason,
        description,
        reportedAt: new Date()
      });

      await match.save();

      // Log the report for admin review
      loggers.app.warn(`User report filed: ${reporterId} reported ${reportedId} for ${reason}`, {
        matchId: match._id,
        reason,
        description
      });

      return { success: true, message: 'Report filed successfully' };

    } catch (error) {
      loggers.app.error('Error reporting user:', error);
      throw error;
    }
  }

  /**
   * Block a user
   * Requirement 9.5, 9.6
   */
  async blockUser(userId, blockedUserId) {
    try {
      loggers.app.info(`User ${userId} blocking user ${blockedUserId}`);

      // Find the match between users
      const match = await SocialMatch.existsBetweenUsers(userId, blockedUserId);
      if (!match) {
        throw new Error('No match exists between these users');
      }

      // Add block to the match
      match.blockedBy.push({
        user: userId,
        blockedAt: new Date()
      });

      // Update match status to blocked
      match.status = 'blocked';
      match.lastInteraction = new Date();

      await match.save();

      // Clear cache for both users
      await this._clearUserMatchCache(userId);
      await this._clearUserMatchCache(blockedUserId);

      loggers.app.info(`User ${blockedUserId} blocked by user ${userId}`);
      return { success: true, message: 'User blocked successfully' };

    } catch (error) {
      loggers.app.error('Error blocking user:', error);
      throw error;
    }
  }

  /**
   * Get compatibility score between two users
   * Private method for calculating compatibility
   */
  _calculateCompatibilityScore(user1, user2) {
    let totalScore = 0;
    let totalWeight = 0;

    const prefs1 = user1.profile.socialPreferences.roommateCriteria;
    const prefs2 = user2.profile.socialPreferences.roommateCriteria;

    // Age compatibility
    if (prefs1.ageRange && prefs2.ageRange && user1.profile.dateOfBirth && user2.profile.dateOfBirth) {
      const age1 = this._calculateAge(user1.profile.dateOfBirth);
      const age2 = this._calculateAge(user2.profile.dateOfBirth);
      
      const age1InRange2 = age1 >= prefs2.ageRange.min && age1 <= prefs2.ageRange.max;
      const age2InRange1 = age2 >= prefs1.ageRange.min && age2 <= prefs1.ageRange.max;
      
      if (age1InRange2 && age2InRange1) {
        totalScore += this.compatibilityWeights.ageRange * 100;
      } else if (age1InRange2 || age2InRange1) {
        totalScore += this.compatibilityWeights.ageRange * 50;
      }
      totalWeight += this.compatibilityWeights.ageRange;
    }

    // Gender compatibility (if specified)
    if (prefs1.gender && prefs2.gender) {
      if (prefs1.gender === 'any' || prefs2.gender === 'any' || prefs1.gender === prefs2.gender) {
        totalScore += this.compatibilityWeights.gender * 100;
      }
      totalWeight += this.compatibilityWeights.gender;
    }

    // Lifestyle compatibility
    const lifestyleFactors = ['cleanliness', 'noiseLevel', 'socialLevel'];
    lifestyleFactors.forEach(factor => {
      if (prefs1.lifestyle && prefs2.lifestyle && prefs1.lifestyle[factor] && prefs2.lifestyle[factor]) {
        const score = this._calculateLifestyleCompatibility(prefs1.lifestyle[factor], prefs2.lifestyle[factor]);
        totalScore += this.compatibilityWeights[factor] * score;
        totalWeight += this.compatibilityWeights[factor];
      }
    });

    // Boolean preferences (smoking, pets, etc.)
    const booleanFactors = ['smokingTolerance', 'petTolerance'];
    booleanFactors.forEach(factor => {
      if (prefs1.lifestyle && prefs2.lifestyle && 
          prefs1.lifestyle[factor] !== undefined && prefs2.lifestyle[factor] !== undefined) {
        if (prefs1.lifestyle[factor] === prefs2.lifestyle[factor]) {
          totalScore += this.compatibilityWeights[factor] * 100;
        }
        totalWeight += this.compatibilityWeights[factor];
      }
    });

    // Guest policy compatibility
    if (prefs1.lifestyle && prefs2.lifestyle && prefs1.lifestyle.guestPolicy && prefs2.lifestyle.guestPolicy) {
      const score = this._calculateGuestPolicyCompatibility(prefs1.lifestyle.guestPolicy, prefs2.lifestyle.guestPolicy);
      totalScore += this.compatibilityWeights.guestPolicy * score;
      totalWeight += this.compatibilityWeights.guestPolicy;
    }

    // Occupation compatibility
    if (prefs1.occupation && prefs2.occupation && user1.profile.employment && user2.profile.employment) {
      const occupation1 = user1.profile.employment.occupation;
      const occupation2 = user2.profile.employment.occupation;
      
      if (prefs1.occupation.includes(occupation2) || prefs2.occupation.includes(occupation1)) {
        totalScore += this.compatibilityWeights.occupation * 100;
      }
      totalWeight += this.compatibilityWeights.occupation;
    }

    // Return normalized score
    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
  }

  /**
   * Get matched criteria between two users
   */
  _getMatchedCriteria(user1, user2) {
    const matchedCriteria = [];
    const prefs1 = user1.profile.socialPreferences.roommateCriteria;
    const prefs2 = user2.profile.socialPreferences.roommateCriteria;

    // Check age range compatibility
    if (prefs1.ageRange && prefs2.ageRange && user1.profile.dateOfBirth && user2.profile.dateOfBirth) {
      const age1 = this._calculateAge(user1.profile.dateOfBirth);
      const age2 = this._calculateAge(user2.profile.dateOfBirth);
      
      if ((age1 >= prefs2.ageRange.min && age1 <= prefs2.ageRange.max) ||
          (age2 >= prefs1.ageRange.min && age2 <= prefs1.ageRange.max)) {
        matchedCriteria.push('age_range');
      }
    }

    // Check other criteria
    if (prefs1.gender === prefs2.gender || prefs1.gender === 'any' || prefs2.gender === 'any') {
      matchedCriteria.push('gender_preference');
    }

    if (prefs1.lifestyle && prefs2.lifestyle) {
      if (prefs1.lifestyle.cleanliness === prefs2.lifestyle.cleanliness) {
        matchedCriteria.push('cleanliness');
      }
      if (prefs1.lifestyle.noiseLevel === prefs2.lifestyle.noiseLevel) {
        matchedCriteria.push('noise_level');
      }
      if (prefs1.lifestyle.socialLevel === prefs2.lifestyle.socialLevel) {
        matchedCriteria.push('social_level');
      }
      if (prefs1.lifestyle.smokingTolerance === prefs2.lifestyle.smokingTolerance) {
        matchedCriteria.push('smoking_tolerance');
      }
      if (prefs1.lifestyle.petTolerance === prefs2.lifestyle.petTolerance) {
        matchedCriteria.push('pet_tolerance');
      }
      if (prefs1.lifestyle.guestPolicy === prefs2.lifestyle.guestPolicy) {
        matchedCriteria.push('guest_policy');
      }
    }

    return matchedCriteria;
  }

  /**
   * Calculate lifestyle compatibility score
   */
  _calculateLifestyleCompatibility(pref1, pref2) {
    const lifestyleOrder = {
      'very_clean': 4, 'clean': 3, 'moderate': 2, 'relaxed': 1,
      'very_quiet': 4, 'quiet': 3, 'lively': 1,
      'very_social': 4, 'social': 3, 'private': 1
    };

    const score1 = lifestyleOrder[pref1] || 2;
    const score2 = lifestyleOrder[pref2] || 2;
    const difference = Math.abs(score1 - score2);

    // Perfect match = 100, 1 level difference = 75, 2 levels = 50, 3 levels = 25
    return Math.max(0, 100 - (difference * 25));
  }

  /**
   * Calculate guest policy compatibility
   */
  _calculateGuestPolicyCompatibility(policy1, policy2) {
    const policyOrder = { 'strict': 3, 'moderate': 2, 'flexible': 1 };
    const score1 = policyOrder[policy1] || 2;
    const score2 = policyOrder[policy2] || 2;
    const difference = Math.abs(score1 - score2);

    return Math.max(0, 100 - (difference * 50));
  }

  /**
   * Calculate age from date of birth
   */
  _calculateAge(dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }

  /**
   * Validate social preferences
   */
  _validateSocialPreferences(preferences) {
    if (preferences.roommateCriteria && preferences.roommateCriteria.ageRange) {
      const { min, max } = preferences.roommateCriteria.ageRange;
      if (min && max && min > max) {
        throw new Error('Minimum age cannot be greater than maximum age');
      }
      if (min && (min < 18 || min > 100)) {
        throw new Error('Minimum age must be between 18 and 100');
      }
      if (max && (max < 18 || max > 100)) {
        throw new Error('Maximum age must be between 18 and 100');
      }
    }
  }

  /**
   * Send a message between matched users
   * Requirement 9.5
   */
  async sendMessage(matchId, senderId, content) {
    try {
      loggers.app.info(`Sending message in match ${matchId} from user ${senderId}`);

      // Validate match exists and user is part of it
      const match = await SocialMatch.findById(matchId);
      if (!match) {
        throw new Error('Match not found');
      }

      if (match.user1.toString() !== senderId.toString() && match.user2.toString() !== senderId.toString()) {
        throw new Error('User not authorized to send messages in this match');
      }

      // Check if match is active (accepted or pending)
      if (match.status === 'blocked' || match.status === 'rejected') {
        throw new Error('Cannot send messages in blocked or rejected matches');
      }

      // Determine recipient
      const recipientId = match.user1.toString() === senderId.toString() ? match.user2 : match.user1;

      // Validate message content
      if (!content || content.trim().length === 0) {
        throw new Error('Message content cannot be empty');
      }

      if (content.length > 1000) {
        throw new Error('Message content too long (max 1000 characters)');
      }

      // Create message
      const message = new Message({
        match: matchId,
        sender: senderId,
        recipient: recipientId,
        content: content.trim()
      });

      await message.save();

      // Update match last interaction
      match.lastInteraction = new Date();
      await match.save();

      // Populate sender info for response
      await message.populate('sender', 'profile.firstName profile.lastName profile.profilePicture');

      loggers.app.info(`Message sent successfully with ID ${message._id}`);
      return message;

    } catch (error) {
      loggers.app.error('Error sending message:', error);
      throw error;
    }
  }

  /**
   * Get conversation messages for a match
   * Requirement 9.5
   */
  async getConversation(matchId, userId, page = 1, limit = 50) {
    try {
      loggers.app.info(`Getting conversation for match ${matchId}, user ${userId}`);

      // Validate match exists and user is part of it
      const match = await SocialMatch.findById(matchId);
      if (!match) {
        throw new Error('Match not found');
      }

      if (match.user1.toString() !== userId.toString() && match.user2.toString() !== userId.toString()) {
        throw new Error('User not authorized to view this conversation');
      }

      // Get messages
      const messages = await Message.getConversation(matchId, page, limit);
      
      // Get total count
      const total = await Message.countDocuments({
        match: matchId,
        isDeleted: false
      });

      // Mark messages as read for the current user
      await Message.markAsRead(matchId, userId);

      return {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      loggers.app.error('Error getting conversation:', error);
      throw error;
    }
  }

  /**
   * Get unread message count for a user
   * Requirement 9.5
   */
  async getUnreadMessageCount(userId) {
    try {
      const count = await Message.getUnreadCount(userId);
      return { unreadCount: count };
    } catch (error) {
      loggers.app.error('Error getting unread message count:', error);
      throw error;
    }
  }

  /**
   * Delete a message
   * Requirement 9.5
   */
  async deleteMessage(messageId, userId) {
    try {
      loggers.app.info(`Deleting message ${messageId} by user ${userId}`);

      const message = await Message.findById(messageId);
      if (!message) {
        throw new Error('Message not found');
      }

      // Only sender can delete their own messages
      if (message.sender.toString() !== userId.toString()) {
        throw new Error('User not authorized to delete this message');
      }

      // Soft delete
      message.isDeleted = true;
      message.deletedAt = new Date();
      await message.save();

      loggers.app.info(`Message ${messageId} deleted successfully`);
      return { success: true, message: 'Message deleted successfully' };

    } catch (error) {
      loggers.app.error('Error deleting message:', error);
      throw error;
    }
  }

  /**
   * Get all conversations for a user
   * Requirement 9.5
   */
  async getUserConversations(userId, page = 1, limit = 20) {
    try {
      loggers.app.info(`Getting conversations for user ${userId}`);

      const skip = (page - 1) * limit;

      // Get matches with recent messages
      const matches = await SocialMatch.find({
        $or: [{ user1: userId }, { user2: userId }],
        status: { $in: ['pending', 'accepted'] }
      })
      .populate('user1', 'profile.firstName profile.lastName profile.profilePicture')
      .populate('user2', 'profile.firstName profile.lastName profile.profilePicture')
      .sort({ lastInteraction: -1 })
      .skip(skip)
      .limit(limit);

      // Get last message for each match
      const conversationsWithMessages = await Promise.all(
        matches.map(async (match) => {
          const lastMessage = await Message.findOne({
            match: match._id,
            isDeleted: false
          })
          .sort({ createdAt: -1 })
          .populate('sender', 'profile.firstName profile.lastName');

          const unreadCount = await Message.countDocuments({
            match: match._id,
            recipient: userId,
            readAt: { $exists: false },
            isDeleted: false
          });

          const otherUser = match.user1._id.toString() === userId.toString() ? match.user2 : match.user1;

          return {
            match,
            otherUser,
            lastMessage,
            unreadCount,
            compatibilityScore: match.compatibilityScore
          };
        })
      );

      const total = await SocialMatch.countDocuments({
        $or: [{ user1: userId }, { user2: userId }],
        status: { $in: ['pending', 'accepted'] }
      });

      return {
        conversations: conversationsWithMessages,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      loggers.app.error('Error getting user conversations:', error);
      throw error;
    }
  }

  /**
   * Clear user match cache
   */
  async _clearUserMatchCache(userId) {
    try {
      const pattern = `roommates:${userId}:*`;
      await cacheService.delPattern(pattern);
    } catch (error) {
      loggers.app.warn('Failed to clear user match cache:', error);
    }
  }
}

module.exports = new SocialMatchingService();