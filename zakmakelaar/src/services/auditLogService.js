const mongoose = require('mongoose');
const { loggers } = require('./logger');

/**
 * AuditLogService - Comprehensive audit logging for auto-application activities
 * 
 * This service provides:
 * - Detailed logging of all auto-application activities
 * - User action tracking and compliance monitoring
 * - Security event logging and alerting
 * - GDPR compliance audit trails
 */

// Audit Log Schema
const auditLogSchema = new mongoose.Schema({
  userId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  sessionId: { 
    type: String 
  },
  action: { 
    type: String, 
    required: true,
    enum: [
      // Auto-application actions
      'auto_application_enabled',
      'auto_application_disabled', 
      'auto_application_settings_updated',
      'personal_info_updated',
      'document_uploaded',
      'document_deleted',
      'application_submitted',
      'application_failed',
      'queue_processed',
      
      // Security actions
      'login_attempt',
      'login_success',
      'login_failed',
      'password_changed',
      'account_locked',
      'suspicious_activity',
      
      // Privacy actions
      'data_export_requested',
      'data_deletion_requested',
      'consent_given',
      'consent_withdrawn',
      'privacy_settings_updated',
      
      // Admin actions
      'admin_access',
      'user_impersonation',
      'system_configuration_changed',
      'bulk_operation_performed'
    ]
  },
  category: {
    type: String,
    required: true,
    enum: ['security', 'privacy', 'auto_application', 'system', 'user_action']
  },
  severity: {
    type: String,
    required: true,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  details: {
    // Flexible object to store action-specific details
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  metadata: {
    ipAddress: { type: String },
    userAgent: { type: String },
    requestId: { type: String },
    endpoint: { type: String },
    method: { type: String },
    statusCode: { type: Number },
    responseTime: { type: Number }, // in milliseconds
    dataSize: { type: Number }, // in bytes
    location: {
      country: { type: String },
      city: { type: String },
      coordinates: {
        lat: { type: Number },
        lng: { type: Number }
      }
    }
  },
  result: {
    type: String,
    enum: ['success', 'failure', 'partial', 'blocked'],
    default: 'success'
  },
  errorMessage: { type: String },
  timestamp: { 
    type: Date, 
    default: Date.now,
    index: true
  },
  retentionDate: { 
    type: Date
  }
});

// Indexes for performance and compliance
auditLogSchema.index({ userId: 1, timestamp: -1 });
auditLogSchema.index({ action: 1, timestamp: -1 });
auditLogSchema.index({ category: 1, severity: 1, timestamp: -1 });
auditLogSchema.index({ 'metadata.ipAddress': 1, timestamp: -1 });
auditLogSchema.index({ retentionDate: 1 }, { expireAfterSeconds: 0 });

// Virtual for human-readable timestamp
auditLogSchema.virtual('formattedTimestamp').get(function() {
  return this.timestamp.toISOString();
});

// Pre-save middleware to set retention date
auditLogSchema.pre('save', function(next) {
  if (!this.retentionDate) {
    // Set retention based on category and severity
    const retentionDays = this._getRetentionDays();
    this.retentionDate = new Date(Date.now() + (retentionDays * 24 * 60 * 60 * 1000));
  }
  next();
});

// Method to determine retention period
auditLogSchema.methods._getRetentionDays = function() {
  // GDPR and compliance-based retention periods
  switch (this.category) {
    case 'security':
      return this.severity === 'critical' ? 2555 : 1095; // 7 years for critical, 3 years for others
    case 'privacy':
      return 2555; // 7 years for privacy-related actions
    case 'auto_application':
      return 1095; // 3 years for application activities
    case 'system':
      return 365; // 1 year for system logs
    default:
      return 730; // 2 years default
  }
};

const AuditLog = mongoose.model('AuditLog', auditLogSchema);

class AuditLogService {
  constructor() {
    this.maxBatchSize = 100;
    this.batchTimeout = 5000; // 5 seconds
    this.pendingLogs = [];
    this.batchTimer = null;
  }

  /**
   * Log an audit event
   * @param {Object} logData - Audit log data
   * @returns {Promise<void>}
   */
  async log(logData) {
    try {
      const auditEntry = new AuditLog({
        userId: logData.userId,
        sessionId: logData.sessionId,
        action: logData.action,
        category: logData.category || this._categorizeAction(logData.action),
        severity: logData.severity || this._determineSeverity(logData.action),
        details: logData.details || {},
        metadata: {
          ipAddress: logData.ipAddress,
          userAgent: logData.userAgent,
          requestId: logData.requestId,
          endpoint: logData.endpoint,
          method: logData.method,
          statusCode: logData.statusCode,
          responseTime: logData.responseTime,
          dataSize: logData.dataSize,
          location: logData.location
        },
        result: logData.result || 'success',
        errorMessage: logData.errorMessage
      });

      // For high-severity events, log immediately
      if (auditEntry.severity === 'critical' || auditEntry.severity === 'high') {
        await auditEntry.save();
        await this._alertOnCriticalEvent(auditEntry);
      } else {
        // Batch low-priority logs for performance
        this._addToBatch(auditEntry);
      }

      // Also log to application logger for immediate visibility
      loggers.audit.info('Audit event', {
        userId: logData.userId,
        action: logData.action,
        category: auditEntry.category,
        severity: auditEntry.severity,
        result: auditEntry.result,
        timestamp: auditEntry.timestamp
      });

    } catch (error) {
      loggers.app.error('Failed to create audit log:', error);
      // Don't throw - audit logging should not break application flow
    }
  }

  /**
   * Log auto-application specific events
   * @param {string} userId - User ID
   * @param {string} action - Action performed
   * @param {Object} details - Action details
   * @param {Object} metadata - Request metadata
   */
  async logAutoApplication(userId, action, details = {}, metadata = {}) {
    await this.log({
      userId,
      action,
      category: 'auto_application',
      details,
      ...metadata
    });
  }

  /**
   * Log security events
   * @param {string} userId - User ID (optional for some security events)
   * @param {string} action - Security action
   * @param {Object} details - Security event details
   * @param {Object} metadata - Request metadata
   */
  async logSecurity(userId, action, details = {}, metadata = {}) {
    await this.log({
      userId,
      action,
      category: 'security',
      severity: this._getSecuritySeverity(action, details),
      details,
      ...metadata
    });
  }

  /**
   * Log privacy-related events
   * @param {string} userId - User ID
   * @param {string} action - Privacy action
   * @param {Object} details - Privacy event details
   * @param {Object} metadata - Request metadata
   */
  async logPrivacy(userId, action, details = {}, metadata = {}) {
    await this.log({
      userId,
      action,
      category: 'privacy',
      severity: 'high', // Privacy events are always high severity
      details,
      ...metadata
    });
  }

  /**
   * Get audit logs for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Audit logs
   */
  async getUserAuditLogs(userId, options = {}) {
    try {
      const {
        startDate,
        endDate,
        actions,
        categories,
        limit = 100,
        offset = 0
      } = options;

      const query = { userId };

      if (startDate || endDate) {
        query.timestamp = {};
        if (startDate) query.timestamp.$gte = new Date(startDate);
        if (endDate) query.timestamp.$lte = new Date(endDate);
      }

      if (actions && actions.length > 0) {
        query.action = { $in: actions };
      }

      if (categories && categories.length > 0) {
        query.category = { $in: categories };
      }

      const logs = await AuditLog.find(query)
        .sort({ timestamp: -1 })
        .limit(limit)
        .skip(offset)
        .lean();

      return logs.map(log => ({
        id: log._id,
        action: log.action,
        category: log.category,
        severity: log.severity,
        details: log.details,
        result: log.result,
        timestamp: log.timestamp,
        ipAddress: log.metadata?.ipAddress,
        userAgent: log.metadata?.userAgent
      }));

    } catch (error) {
      loggers.app.error('Failed to retrieve user audit logs:', error);
      throw new Error('Failed to retrieve audit logs');
    }
  }

  /**
   * Get system-wide audit logs (admin only)
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Audit logs with pagination
   */
  async getSystemAuditLogs(options = {}) {
    try {
      const {
        startDate,
        endDate,
        actions,
        categories,
        severities,
        userIds,
        limit = 100,
        offset = 0
      } = options;

      const query = {};

      if (startDate || endDate) {
        query.timestamp = {};
        if (startDate) query.timestamp.$gte = new Date(startDate);
        if (endDate) query.timestamp.$lte = new Date(endDate);
      }

      if (actions && actions.length > 0) {
        query.action = { $in: actions };
      }

      if (categories && categories.length > 0) {
        query.category = { $in: categories };
      }

      if (severities && severities.length > 0) {
        query.severity = { $in: severities };
      }

      if (userIds && userIds.length > 0) {
        query.userId = { $in: userIds };
      }

      const [logs, total] = await Promise.all([
        AuditLog.find(query)
          .populate('userId', 'email profile.firstName profile.lastName')
          .sort({ timestamp: -1 })
          .limit(limit)
          .skip(offset)
          .lean(),
        AuditLog.countDocuments(query)
      ]);

      return {
        logs: logs.map(log => ({
          id: log._id,
          userId: log.userId?._id,
          userEmail: log.userId?.email,
          userName: log.userId?.profile?.firstName && log.userId?.profile?.lastName
            ? `${log.userId.profile.firstName} ${log.userId.profile.lastName}`
            : log.userId?.email,
          action: log.action,
          category: log.category,
          severity: log.severity,
          details: log.details,
          result: log.result,
          timestamp: log.timestamp,
          ipAddress: log.metadata?.ipAddress,
          userAgent: log.metadata?.userAgent,
          location: log.metadata?.location
        })),
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      };

    } catch (error) {
      loggers.app.error('Failed to retrieve system audit logs:', error);
      throw new Error('Failed to retrieve system audit logs');
    }
  }

  /**
   * Generate compliance report
   * @param {Object} options - Report options
   * @returns {Promise<Object>} Compliance report
   */
  async generateComplianceReport(options = {}) {
    try {
      const {
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        endDate = new Date(),
        userId
      } = options;

      const matchStage = {
        timestamp: { $gte: startDate, $lte: endDate }
      };

      if (userId) {
        matchStage.userId = new mongoose.Types.ObjectId(userId);
      }

      const pipeline = [
        { $match: matchStage },
        {
          $group: {
            _id: {
              category: '$category',
              action: '$action',
              result: '$result'
            },
            count: { $sum: 1 },
            firstOccurrence: { $min: '$timestamp' },
            lastOccurrence: { $max: '$timestamp' }
          }
        },
        {
          $group: {
            _id: '$_id.category',
            actions: {
              $push: {
                action: '$_id.action',
                result: '$_id.result',
                count: '$count',
                firstOccurrence: '$firstOccurrence',
                lastOccurrence: '$lastOccurrence'
              }
            },
            totalEvents: { $sum: '$count' }
          }
        }
      ];

      const results = await AuditLog.aggregate(pipeline);

      const report = {
        reportPeriod: {
          startDate,
          endDate
        },
        userId,
        categories: {},
        summary: {
          totalEvents: 0,
          securityEvents: 0,
          privacyEvents: 0,
          autoApplicationEvents: 0,
          failedEvents: 0
        }
      };

      results.forEach(category => {
        report.categories[category._id] = {
          totalEvents: category.totalEvents,
          actions: category.actions
        };

        report.summary.totalEvents += category.totalEvents;

        switch (category._id) {
          case 'security':
            report.summary.securityEvents += category.totalEvents;
            break;
          case 'privacy':
            report.summary.privacyEvents += category.totalEvents;
            break;
          case 'auto_application':
            report.summary.autoApplicationEvents += category.totalEvents;
            break;
        }

        // Count failed events
        category.actions.forEach(action => {
          if (action.result === 'failure') {
            report.summary.failedEvents += action.count;
          }
        });
      });

      return report;

    } catch (error) {
      loggers.app.error('Failed to generate compliance report:', error);
      throw new Error('Failed to generate compliance report');
    }
  }

  /**
   * Clean up expired audit logs
   * @returns {Promise<number>} Number of deleted logs
   */
  async cleanupExpiredLogs() {
    try {
      const result = await AuditLog.deleteMany({
        retentionDate: { $lt: new Date() }
      });

      loggers.app.info(`Cleaned up ${result.deletedCount} expired audit logs`);
      return result.deletedCount;

    } catch (error) {
      loggers.app.error('Failed to cleanup expired audit logs:', error);
      throw error;
    }
  }

  // Private helper methods

  /**
   * Categorize action based on action name
   * @private
   */
  _categorizeAction(action) {
    if (action.includes('login') || action.includes('password') || action.includes('suspicious')) {
      return 'security';
    }
    if (action.includes('privacy') || action.includes('consent') || action.includes('data_')) {
      return 'privacy';
    }
    if (action.includes('auto_application') || action.includes('application_') || action.includes('document_')) {
      return 'auto_application';
    }
    if (action.includes('admin') || action.includes('system')) {
      return 'system';
    }
    return 'user_action';
  }

  /**
   * Determine severity based on action
   * @private
   */
  _determineSeverity(action) {
    const criticalActions = ['account_locked', 'suspicious_activity', 'data_deletion_requested'];
    const highActions = ['login_failed', 'password_changed', 'consent_withdrawn', 'admin_access'];
    const mediumActions = ['auto_application_enabled', 'document_uploaded', 'personal_info_updated'];

    if (criticalActions.includes(action)) return 'critical';
    if (highActions.includes(action)) return 'high';
    if (mediumActions.includes(action)) return 'medium';
    return 'low';
  }

  /**
   * Get security-specific severity
   * @private
   */
  _getSecuritySeverity(action, details) {
    if (action === 'login_failed' && details.consecutiveFailures > 3) {
      return 'critical';
    }
    if (action === 'suspicious_activity') {
      return 'critical';
    }
    if (action.includes('failed')) {
      return 'high';
    }
    return 'medium';
  }

  /**
   * Add log to batch for bulk processing
   * @private
   */
  _addToBatch(auditEntry) {
    this.pendingLogs.push(auditEntry);

    if (this.pendingLogs.length >= this.maxBatchSize) {
      this._processBatch();
    } else if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => this._processBatch(), this.batchTimeout);
    }
  }

  /**
   * Process batched logs
   * @private
   */
  async _processBatch() {
    if (this.pendingLogs.length === 0) return;

    const logsToProcess = [...this.pendingLogs];
    this.pendingLogs = [];

    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    try {
      await AuditLog.insertMany(logsToProcess);
      loggers.app.debug(`Processed batch of ${logsToProcess.length} audit logs`);
    } catch (error) {
      loggers.app.error('Failed to process audit log batch:', error);
      // Re-add failed logs to pending (with limit to prevent infinite growth)
      if (this.pendingLogs.length < this.maxBatchSize * 2) {
        this.pendingLogs.unshift(...logsToProcess);
      }
    }
  }

  /**
   * Alert on critical security events
   * @private
   */
  async _alertOnCriticalEvent(auditEntry) {
    if (auditEntry.severity === 'critical') {
      loggers.security.error('Critical security event detected', {
        userId: auditEntry.userId,
        action: auditEntry.action,
        details: auditEntry.details,
        ipAddress: auditEntry.metadata?.ipAddress,
        timestamp: auditEntry.timestamp
      });

      // Here you could integrate with alerting systems like:
      // - Email notifications
      // - Slack/Teams webhooks
      // - PagerDuty/OpsGenie
      // - SMS alerts
    }
  }
}

module.exports = new AuditLogService();