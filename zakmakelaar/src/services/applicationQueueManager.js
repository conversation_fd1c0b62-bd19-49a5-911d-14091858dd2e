const ApplicationQueue = require("../models/ApplicationQueue");
const AutoApplicationSettings = require("../models/AutoApplicationSettings");
const RateLimitingService = require("./rateLimitingService");
const ErrorHandlingService = require("./errorHandlingService");
const { logger } = require("./logger");

/**
 * ApplicationQueueManager - Manages the priority-based queue system for auto-applications
 * Handles rate limiting, random delays, retry mechanisms, and queue monitoring
 */
class ApplicationQueueManager {
  constructor() {
    this.isProcessing = false;
    this.processingInterval = null;
    this.rateLimitingService = new RateLimitingService();
    this.errorHandlingService = new ErrorHandlingService();
    this.queueStats = {
      processed: 0,
      successful: 0,
      failed: 0,
      retries: 0,
      averageProcessingTime: 0,
    };
  }

  /**
   * Initialize the queue manager and start processing
   */
  async initialize() {
    try {
      logger.info("Initializing ApplicationQueueManager");

      // Reset any stuck processing items
      await this.resetStuckItems();

      // Start the queue processing loop
      this.startProcessing();

      // Schedule cleanup of expired items
      this.scheduleCleanup();

      logger.info("ApplicationQueueManager initialized successfully");
    } catch (error) {
      logger.error("Failed to initialize ApplicationQueueManager:", error);
      throw error;
    }
  }

  /**
   * Add an application to the queue with priority calculation
   */
  async addToQueue(applicationData, priority = null) {
    try {
      logger.debug("AddToQueue called", {
        applicationData: {
          userId: applicationData.userId,
          listingId: applicationData.listingId,
          listingUrl: applicationData.listingUrl,
          hasPersonalInfo: !!applicationData.personalInfo,
          hasPreferences: !!applicationData.preferences,
          hasDocuments: !!applicationData.documents,
          hasListingSnapshot: !!applicationData.listingSnapshot,
        },
        providedPriority: priority,
      });

      const {
        userId,
        listingId,
        listingUrl,
        personalInfo,
        preferences,
        documents,
        listingSnapshot,
      } = applicationData;

      if (!userId) {
        const error = new Error("userId is required");
        logger.debug("AddToQueue validation failed - missing userId");
        throw error;
      }
      if (!listingId) {
        const error = new Error("listingId is required");
        logger.debug("AddToQueue validation failed - missing listingId");
        throw error;
      }
      if (!listingUrl) {
        const error = new Error("listingUrl is required");
        logger.debug("AddToQueue validation failed - missing listingUrl");
        throw error;
      }

      logger.debug("Checking for existing application", {
        userId,
        listingId,
      });

      // Check if application already exists for this user and listing
      const existingApplication = await ApplicationQueue.findOne({
        userId,
        listingId,
      });
      if (existingApplication) {
        logger.debug("Application already exists, returning existing", {
          existingApplicationId: existingApplication._id,
          userId,
          listingId,
          status: existingApplication.status,
        });
        logger.warn(
          `Application already exists for user ${userId} and listing ${listingId}`
        );
        return existingApplication;
      }

      logger.debug("Calculating priority for new application", {
        userId,
        listingId,
        providedPriority: priority,
      });

      // Calculate priority if not provided
      if (priority === null) {
        try {
          priority = await this.calculatePriority(applicationData);
          logger.debug("Priority calculated successfully", {
            userId,
            listingId,
            calculatedPriority: priority,
          });
        } catch (error) {
          logger.debug("Priority calculation failed, using default", {
            userId,
            listingId,
            error: error.message,
            defaultPriority: 5,
          });
          priority = 5; // Default priority
        }
      }

      logger.debug("Checking rate limits for user", { userId });

      // Check rate limits using the new rate limiting service
      try {
        const rateLimitCheck = await this.rateLimitingService.checkRateLimit(
          userId
        );

        logger.debug("Rate limit check completed", {
          userId,
          allowed: rateLimitCheck.allowed,
          reason: rateLimitCheck.reason,
          scheduledDelay: rateLimitCheck.scheduledDelay,
        });

        if (!rateLimitCheck.allowed) {
          const error = new Error(
            `Rate limit exceeded: ${rateLimitCheck.reason} - ${
              rateLimitCheck.message || ""
            }`
          );
          logger.debug("Rate limit check failed", {
            userId,
            reason: rateLimitCheck.reason,
            message: rateLimitCheck.message,
          });
          throw error;
        }

        // Use intelligent scheduling from rate limiting service
        const schedulingDelay =
          rateLimitCheck.scheduledDelay || this.generateRandomDelay();
        const scheduledAt = new Date(Date.now() + schedulingDelay);

        logger.debug("Creating queue item with scheduling", {
          userId,
          listingId,
          priority,
          schedulingDelay,
          scheduledAt: scheduledAt.toISOString(),
        });

        // Create queue item
        const queueItem = new ApplicationQueue({
          userId,
          listingId,
          listingUrl,
          priority,
          scheduledAt,
          randomDelay: schedulingDelay,
          applicationData: {
            personalInfo: personalInfo || {},
            preferences: preferences || {},
            documents: documents || [],
          },
          listingSnapshot: listingSnapshot || {},
          status: "pending",
        });

        logger.debug("Saving queue item to database", {
          userId,
          listingId,
          queueItemId: queueItem._id,
        });

        await queueItem.save();

        logger.debug("Recording application attempt in rate limiting service", {
          userId,
        });

        // Record application attempt in rate limiting service
        await this.rateLimitingService.recordApplicationAttempt(
          userId,
          applicationData
        );

        logger.debug("Queue item created successfully", {
          queueItemId: queueItem._id,
          userId,
          listingId,
          priority,
        });

        logger.info(
          `Added application to queue: ${queueItem._id} for user ${userId}, listing ${listingId}, priority ${priority}`
        );

        return queueItem;
      } catch (rateLimitError) {
        logger.debug(
          "Rate limit check failed, proceeding with fallback scheduling",
          {
            userId,
            error: rateLimitError.message,
          }
        );

        // If rate limiting fails, proceed with default scheduling
        const fallbackDelay = this.generateRandomDelay();
        const scheduledAt = new Date(Date.now() + fallbackDelay);

        logger.debug("Creating queue item with fallback scheduling", {
          userId,
          listingId,
          priority: priority || 5,
          fallbackDelay,
          scheduledAt: scheduledAt.toISOString(),
        });

        const queueItem = new ApplicationQueue({
          userId,
          listingId,
          listingUrl,
          priority: priority || 5,
          scheduledAt,
          randomDelay: fallbackDelay,
          applicationData: {
            personalInfo: personalInfo || {},
            preferences: preferences || {},
            documents: documents || [],
          },
          listingSnapshot: listingSnapshot || {},
          status: "pending",
        });

        await queueItem.save();

        logger.debug("Fallback queue item created successfully", {
          queueItemId: queueItem._id,
          userId,
          listingId,
        });

        logger.info(
          `Added application to queue (rate limit bypass): ${queueItem._id} for user ${userId}, listing ${listingId}`
        );

        return queueItem;
      }
    } catch (error) {
      logger.error("Failed to add application to queue:", error);
      logger.debug("AddToQueue failed with error", {
        errorMessage: error.message,
        errorStack: error.stack,
        applicationData: {
          userId: applicationData?.userId,
          listingId: applicationData?.listingId,
          listingUrl: applicationData?.listingUrl,
        },
      });
      throw error;
    }
  }

  /**
   * Start the queue processing loop
   */
  startProcessing() {
    if (this.isProcessing) {
      logger.warn("Queue processing already started");
      return;
    }

    this.isProcessing = true;
    logger.info("Starting queue processing");

    // Process queue every 30 seconds
    this.processingInterval = setInterval(async () => {
      try {
        await this.processQueue();
      } catch (error) {
        logger.error("Error in queue processing loop:", error);
      }
    }, 30000);
  }

  /**
   * Stop the queue processing loop
   */
  stopProcessing() {
    if (!this.isProcessing) {
      return;
    }

    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    logger.info("Stopped queue processing");
  }

  /**
   * Process the next items in the queue
   */
  async processQueue() {
    try {
      logger.debug("Starting queue processing cycle");

      // Get ready items from queue (respecting rate limits)
      const readyItems = await this.getReadyItems();

      logger.debug("Queue processing analysis", {
        readyItemsCount: readyItems.length,
        queueStats: this.queueStats,
      });

      if (readyItems.length === 0) {
        logger.debug("No ready items found, skipping processing cycle");
        return;
      }

      logger.info(`Processing ${readyItems.length} ready queue items`);
      logger.debug("Ready items details", {
        items: readyItems.map((item) => ({
          id: item._id,
          userId: item.userId,
          listingId: item.listingId,
          priority: item.priority,
          scheduledAt: item.scheduledAt,
          attempts: item.attempts,
          status: item.status,
        })),
      });

      // Process items concurrently but with rate limiting
      logger.debug("Starting concurrent processing of ready items");
      const processingPromises = readyItems.map((item) =>
        this.processQueueItem(item)
      );
      const results = await Promise.allSettled(processingPromises);

      // Log processing results
      const successCount = results.filter(
        (r) => r.status === "fulfilled"
      ).length;
      const failureCount = results.filter(
        (r) => r.status === "rejected"
      ).length;

      logger.debug("Processing cycle completed", {
        totalProcessed: results.length,
        successful: successCount,
        failed: failureCount,
        failures: results
          .filter((r) => r.status === "rejected")
          .map((r) => r.reason?.message || "Unknown error"),
      });

      // Update queue statistics
      logger.debug("Updating queue statistics");
      await this.updateQueueStats();
    } catch (error) {
      logger.error("Error processing queue:", error);
      logger.debug("Queue processing failed", {
        errorMessage: error.message,
        errorStack: error.stack,
      });
    }
  }

  /**
   * Get ready items from queue respecting rate limits
   */
  async getReadyItems(limit = 5) {
    try {
      logger.debug("Getting ready items from queue", { limit });

      // Get pending items sorted by priority and scheduled time
      const readyItems = await ApplicationQueue.findReadyItems(limit);

      logger.debug("Initial ready items found", {
        totalFound: readyItems.length,
        items: readyItems.map((item) => ({
          id: item._id,
          userId: item.userId,
          priority: item.priority,
          scheduledAt: item.scheduledAt,
        })),
      });

      // Filter items based on rate limits using the rate limiting service
      const filteredItems = [];
      const rateLimitChecks = [];

      for (const item of readyItems) {
        logger.debug("Checking rate limits for queue item", {
          itemId: item._id,
          userId: item.userId,
        });

        const rateLimitCheck = await this.rateLimitingService.checkRateLimit(
          item.userId
        );
        rateLimitChecks.push({
          itemId: item._id,
          userId: item.userId,
          allowed: rateLimitCheck.allowed,
          reason: rateLimitCheck.reason,
        });

        if (rateLimitCheck.allowed) {
          filteredItems.push(item);
          logger.debug("Item passed rate limit check", {
            itemId: item._id,
            userId: item.userId,
          });
        } else {
          logger.info(
            `Skipping item ${item._id} due to rate limit: ${rateLimitCheck.reason}`
          );
          logger.debug("Item failed rate limit check", {
            itemId: item._id,
            userId: item.userId,
            reason: rateLimitCheck.reason,
          });
        }
      }

      logger.debug("Rate limit filtering completed", {
        originalCount: readyItems.length,
        filteredCount: filteredItems.length,
        rateLimitChecks,
      });

      return filteredItems;
    } catch (error) {
      logger.error("Error getting ready items:", error);
      logger.debug("GetReadyItems failed", {
        errorMessage: error.message,
        errorStack: error.stack,
      });
      return [];
    }
  }

  /**
   * Process a single queue item
   */
  async processQueueItem(queueItem) {
    const startTime = Date.now();

    try {
      logger.info(`Processing queue item: ${queueItem._id}`);
      logger.debug("Queue item processing started", {
        itemId: queueItem._id,
        userId: queueItem.userId,
        listingId: queueItem.listingId,
        priority: queueItem.priority,
        attempts: queueItem.attempts,
        status: queueItem.status,
        scheduledAt: queueItem.scheduledAt,
      });

      // Update status to processing
      logger.debug("Updating queue item status to processing", {
        itemId: queueItem._id,
      });
      await queueItem.updateStatus("processing");

      // Apply additional random delay to mimic human behavior
      const additionalDelay = Math.floor(Math.random() * 30000) + 10000; // 10-40 seconds
      logger.debug("Applying human-like delay before processing", {
        itemId: queueItem._id,
        delayMs: additionalDelay,
      });
      await this.sleep(additionalDelay);

      // Integrate with the actual AutoApplicationService for real form automation
      logger.debug(
        "Starting real application processing with AutoApplicationService",
        {
          itemId: queueItem._id,
        }
      );
      const processingResult = await this.processWithAutoApplicationService(
        queueItem
      );

      logger.debug("Application processing completed", {
        itemId: queueItem._id,
        success: processingResult.success,
        hasError: !!processingResult.error,
        hasResponseText: !!processingResult.responseText,
        statusCode: processingResult.statusCode,
      });

      if (processingResult.success) {
        logger.debug("Processing succeeded, updating status to completed", {
          itemId: queueItem._id,
        });
        await queueItem.updateStatus("completed");
        this.queueStats.successful++;

        // Record successful completion
        logger.debug(
          "Recording successful completion in rate limiting service",
          {
            itemId: queueItem._id,
            userId: queueItem.userId,
          }
        );
        await this.rateLimitingService.recordApplicationCompletion(
          queueItem.userId,
          true
        );

        logger.info(`Successfully processed application: ${queueItem._id}`);
        logger.debug("Queue item processing completed successfully", {
          itemId: queueItem._id,
          processingTimeMs: Date.now() - startTime,
        });
      } else {
        logger.debug("Processing failed, analyzing failure type", {
          itemId: queueItem._id,
          error: processingResult.error?.message,
        });

        // Check if the failure is due to Funda rate limiting
        const rateLimitDetection =
          this.rateLimitingService.detectFundaRateLimit(
            processingResult.responseText || "",
            processingResult.responseHeaders || {},
            processingResult.statusCode || 200
          );

        logger.debug("Rate limit detection analysis", {
          itemId: queueItem._id,
          isRateLimit: rateLimitDetection.isRateLimit,
          reason: rateLimitDetection.reason,
          recommendedAction: rateLimitDetection.recommendedAction,
          pauseDuration: rateLimitDetection.pauseDuration,
        });

        if (rateLimitDetection.isRateLimit) {
          logger.warn(
            `Funda rate limit detected for application ${queueItem._id}:`,
            rateLimitDetection
          );

          // Pause user based on detection severity
          if (rateLimitDetection.recommendedAction === "pause_user") {
            logger.debug("Pausing user due to rate limit detection", {
              itemId: queueItem._id,
              userId: queueItem.userId,
              reason: rateLimitDetection.reason,
              pauseDuration: rateLimitDetection.pauseDuration,
            });

            await this.rateLimitingService.pauseUser(
              queueItem.userId,
              `Funda rate limit: ${rateLimitDetection.reason}`,
              rateLimitDetection.pauseDuration
            );
          } else if (
            rateLimitDetection.recommendedAction === "pause_user_manual"
          ) {
            logger.debug("Pausing user for manual intervention", {
              itemId: queueItem._id,
              userId: queueItem.userId,
              reason: rateLimitDetection.reason,
              pauseDuration: rateLimitDetection.pauseDuration,
            });

            await this.rateLimitingService.pauseUser(
              queueItem.userId,
              `Manual intervention required: ${rateLimitDetection.reason}`,
              rateLimitDetection.pauseDuration,
              true
            );
          }
        }

        // Record failed completion
        logger.debug("Recording failed completion in rate limiting service", {
          itemId: queueItem._id,
          userId: queueItem.userId,
        });
        await this.rateLimitingService.recordApplicationCompletion(
          queueItem.userId,
          false
        );

        // Handle failure with retry logic
        logger.debug("Handling processing failure with retry logic", {
          itemId: queueItem._id,
          error: processingResult.error?.message,
        });
        await this.handleProcessingFailure(queueItem, processingResult.error);
      }

      this.queueStats.processed++;
      logger.debug("Queue statistics updated", {
        itemId: queueItem._id,
        totalProcessed: this.queueStats.processed,
        successful: this.queueStats.successful,
        failed: this.queueStats.failed,
      });
    } catch (error) {
      logger.error(`Error processing queue item ${queueItem._id}:`, error);
      logger.debug("Queue item processing error occurred", {
        itemId: queueItem._id,
        userId: queueItem.userId,
        errorMessage: error.message,
        errorStack: error.stack,
        processingTimeMs: Date.now() - startTime,
      });

      // Use comprehensive error handling
      logger.debug("Using comprehensive error handling service", {
        itemId: queueItem._id,
        attemptNumber: queueItem.attempts + 1,
      });

      const recoveryResult = await this.errorHandlingService.handleError(
        error,
        {
          service: "ApplicationQueueManager",
          method: "processQueueItem",
          queueItemId: queueItem._id,
          userId: queueItem.userId,
          attemptNumber: queueItem.attempts + 1,
          operation: "process_queue_item",
        }
      );

      logger.debug("Error handling service response", {
        itemId: queueItem._id,
        retryScheduled: recoveryResult.retryScheduled,
        action: recoveryResult.action,
      });

      // If error handling didn't handle the retry, fall back to original logic
      if (!recoveryResult.retryScheduled) {
        logger.debug(
          "Error handling service did not schedule retry, using fallback logic",
          {
            itemId: queueItem._id,
          }
        );
        await this.handleProcessingFailure(queueItem, error);
      }
    }
  }

  /**
   * Handle processing failure with retry mechanism
   */
  async handleProcessingFailure(queueItem, error) {
    try {
      logger.warn(
        `Processing failed for queue item ${queueItem._id}: ${error.message}`
      );
      logger.debug("Handling processing failure", {
        itemId: queueItem._id,
        userId: queueItem.userId,
        currentAttempts: queueItem.attempts,
        errorMessage: error.message,
        errorType: error.name,
      });

      // Increment attempt counter
      logger.debug("Incrementing attempt counter", {
        itemId: queueItem._id,
        currentAttempts: queueItem.attempts,
        nextAttempt: queueItem.attempts + 1,
      });

      await queueItem.incrementAttempt();

      logger.debug("Checking if item can retry", {
        itemId: queueItem._id,
        attempts: queueItem.attempts,
        canRetry: queueItem.canRetry,
      });

      if (queueItem.canRetry) {
        // Schedule retry with exponential backoff
        const backoffDelay = this.calculateExponentialBackoff(
          queueItem.attempts
        );

        logger.debug("Calculating exponential backoff for retry", {
          itemId: queueItem._id,
          attempts: queueItem.attempts,
          backoffDelayMinutes: backoffDelay,
        });

        await queueItem.scheduleRetry(backoffDelay);

        this.queueStats.retries++;

        logger.info(
          `Scheduled retry for queue item ${queueItem._id}, attempt ${queueItem.attempts}`
        );
        logger.debug("Retry scheduled successfully", {
          itemId: queueItem._id,
          nextAttempt: queueItem.attempts,
          retryDelayMinutes: backoffDelay,
          totalRetries: this.queueStats.retries,
        });
      } else {
        // Max attempts reached, mark as failed
        logger.debug("Max attempts reached, marking item as failed", {
          itemId: queueItem._id,
          maxAttempts: queueItem.maxAttempts || 3,
          currentAttempts: queueItem.attempts,
        });

        await queueItem.updateStatus("failed", error);
        this.queueStats.failed++;

        logger.error(
          `Max attempts reached for queue item ${queueItem._id}, marking as failed`
        );
        logger.debug("Item marked as failed", {
          itemId: queueItem._id,
          attempts: queueItem.attempts,
          totalFailed: this.queueStats.failed,
        });
      }
    } catch (retryError) {
      logger.error(
        `Error handling processing failure for ${queueItem._id}:`,
        retryError
      );
      logger.debug("Retry handling failed", {
        itemId: queueItem._id,
        originalError: error.message,
        retryError: retryError.message,
        retryErrorStack: retryError.stack,
      });
    }
  }

  /**
   * Calculate exponential backoff delay in minutes
   */
  calculateExponentialBackoff(attempts) {
    const baseDelay = 5; // 5 minutes
    const maxDelay = 120; // 2 hours
    const backoffDelay = Math.min(
      baseDelay * Math.pow(2, attempts - 1),
      maxDelay
    );

    // Add jitter (±20%)
    const jitter = backoffDelay * 0.2 * (Math.random() - 0.5);
    return Math.max(1, Math.floor(backoffDelay + jitter));
  }

  /**
   * Generate random delay to mimic human behavior (2-10 minutes)
   */
  generateRandomDelay() {
    const minDelay = 2 * 60 * 1000; // 2 minutes
    const maxDelay = 10 * 60 * 1000; // 10 minutes
    return Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay;
  }

  /**
   * Calculate priority for an application based on various factors
   */
  async calculatePriority(applicationData) {
    let priority = 0;
    const priorityFactors = {};

    try {
      const { listingSnapshot, userId } = applicationData;

      logger.debug("Starting priority calculation", {
        userId,
        hasListingSnapshot: !!listingSnapshot,
        listingData: listingSnapshot
          ? {
              price: listingSnapshot.price,
              availableFrom: listingSnapshot.availableFrom,
              propertyType: listingSnapshot.propertyType,
              scrapedAt: listingSnapshot.scrapedAt,
            }
          : null,
      });

      // Base priority factors
      if (listingSnapshot) {
        // Higher priority for lower priced properties (more competitive)
        if (listingSnapshot.price && listingSnapshot.price < 1500) {
          const priceBonus = 20;
          priority += priceBonus;
          priorityFactors.lowPrice = priceBonus;
          logger.debug("Applied low price bonus", {
            userId,
            price: listingSnapshot.price,
            bonus: priceBonus,
            newPriority: priority,
          });
        } else if (listingSnapshot.price && listingSnapshot.price < 2000) {
          const priceBonus = 10;
          priority += priceBonus;
          priorityFactors.mediumPrice = priceBonus;
          logger.debug("Applied medium price bonus", {
            userId,
            price: listingSnapshot.price,
            bonus: priceBonus,
            newPriority: priority,
          });
        }

        // Higher priority for properties available soon
        if (listingSnapshot.availableFrom) {
          const daysUntilAvailable = Math.ceil(
            (new Date(listingSnapshot.availableFrom) - new Date()) /
              (1000 * 60 * 60 * 24)
          );

          logger.debug("Calculating availability bonus", {
            userId,
            availableFrom: listingSnapshot.availableFrom,
            daysUntilAvailable,
          });

          if (daysUntilAvailable <= 7) {
            const availabilityBonus = 15;
            priority += availabilityBonus;
            priorityFactors.immediateAvailability = availabilityBonus;
            logger.debug("Applied immediate availability bonus", {
              userId,
              daysUntilAvailable,
              bonus: availabilityBonus,
              newPriority: priority,
            });
          } else if (daysUntilAvailable <= 30) {
            const availabilityBonus = 10;
            priority += availabilityBonus;
            priorityFactors.nearAvailability = availabilityBonus;
            logger.debug("Applied near availability bonus", {
              userId,
              daysUntilAvailable,
              bonus: availabilityBonus,
              newPriority: priority,
            });
          }
        }

        // Higher priority for popular property types
        if (listingSnapshot.propertyType === "apartment") {
          const typeBonus = 5;
          priority += typeBonus;
          priorityFactors.apartmentType = typeBonus;
          logger.debug("Applied apartment type bonus", {
            userId,
            propertyType: listingSnapshot.propertyType,
            bonus: typeBonus,
            newPriority: priority,
          });
        }
      }

      // User-specific priority factors
      logger.debug("Fetching user settings for priority calculation", {
        userId,
      });
      const userSettings = await AutoApplicationSettings.findOne({ userId });

      if (userSettings) {
        logger.debug("User settings retrieved", {
          userId,
          hasSettings: true,
          requireManualReview: userSettings.settings?.requireManualReview,
        });

        if (
          userSettings.settings &&
          userSettings.settings.requireManualReview === false
        ) {
          const autoSubmitBonus = 10;
          priority += autoSubmitBonus; // Higher priority for users who allow auto-submit
          priorityFactors.autoSubmit = autoSubmitBonus;
          logger.debug("Applied auto-submit bonus", {
            userId,
            bonus: autoSubmitBonus,
            newPriority: priority,
          });
        }
      } else {
        logger.debug("No user settings found", { userId });
      }

      // Time-based priority (newer listings get higher priority)
      if (listingSnapshot && listingSnapshot.scrapedAt) {
        const hoursOld =
          (new Date() - new Date(listingSnapshot.scrapedAt)) / (1000 * 60 * 60);

        logger.debug("Calculating recency bonus", {
          userId,
          scrapedAt: listingSnapshot.scrapedAt,
          hoursOld: Math.round(hoursOld * 100) / 100,
        });

        if (hoursOld < 1) {
          const recencyBonus = 25;
          priority += recencyBonus; // Very new listing
          priorityFactors.veryNew = recencyBonus;
          logger.debug("Applied very new listing bonus", {
            userId,
            hoursOld,
            bonus: recencyBonus,
            newPriority: priority,
          });
        } else if (hoursOld < 6) {
          const recencyBonus = 15;
          priority += recencyBonus; // Recent listing
          priorityFactors.recent = recencyBonus;
          logger.debug("Applied recent listing bonus", {
            userId,
            hoursOld,
            bonus: recencyBonus,
            newPriority: priority,
          });
        } else if (hoursOld < 24) {
          const recencyBonus = 5;
          priority += recencyBonus; // Day-old listing
          priorityFactors.dayOld = recencyBonus;
          logger.debug("Applied day-old listing bonus", {
            userId,
            hoursOld,
            bonus: recencyBonus,
            newPriority: priority,
          });
        }
      }

      const finalPriority = Math.max(0, priority);

      logger.debug("Priority calculation completed", {
        userId,
        finalPriority,
        priorityFactors,
        totalBonuses: Object.values(priorityFactors).reduce(
          (sum, bonus) => sum + bonus,
          0
        ),
      });

      return finalPriority;
    } catch (error) {
      logger.error("Error calculating priority:", error);
      logger.debug("Priority calculation failed", {
        userId: applicationData?.userId,
        errorMessage: error.message,
        errorStack: error.stack,
      });
      return 0;
    }
  }

  /**
   * Get user's queue items
   */
  async getUserQueue(userId, options = {}) {
    try {
      const { status, limit = 20, offset = 0 } = options;

      // Build query
      const query = { userId };
      if (status) {
        query.status = status;
      }

      // Get queue items with pagination
      const applications = await ApplicationQueue.find(query)
        .sort({ priority: -1, scheduledAt: 1 })
        .limit(parseInt(limit))
        .skip(parseInt(offset))
        .lean();

      // Get total count for pagination
      const total = await ApplicationQueue.countDocuments(query);

      return {
        applications,
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: total > parseInt(offset) + parseInt(limit),
      };
    } catch (error) {
      logger.error(`Error getting user queue for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Pause the queue for a specific user or globally
   */
  async pauseQueue(userId = null, reason = "Manual pause") {
    try {
      if (userId) {
        // Pause queue for specific user
        await ApplicationQueue.updateMany(
          { userId, status: "pending" },
          { status: "paused", "metadata.pauseReason": reason }
        );
        logger.info(`Paused queue for user ${userId}: ${reason}`);
      } else {
        // Pause entire queue
        this.stopProcessing();
        await ApplicationQueue.updateMany(
          { status: "pending" },
          { status: "paused", "metadata.pauseReason": reason }
        );
        logger.info(`Paused entire queue: ${reason}`);
      }
    } catch (error) {
      logger.error("Error pausing queue:", error);
      throw error;
    }
  }

  /**
   * Resume the queue for a specific user or globally
   */
  async resumeQueue(userId = null) {
    try {
      if (userId) {
        // Resume queue for specific user
        await ApplicationQueue.updateMany(
          { userId, status: "paused" },
          { status: "pending", $unset: { "metadata.pauseReason": 1 } }
        );
        logger.info(`Resumed queue for user ${userId}`);
      } else {
        // Resume entire queue
        await ApplicationQueue.updateMany(
          { status: "paused" },
          { status: "pending", $unset: { "metadata.pauseReason": 1 } }
        );
        this.startProcessing();
        logger.info("Resumed entire queue");
      }
    } catch (error) {
      logger.error("Error resuming queue:", error);
      throw error;
    }
  }

  /**
   * Get queue status and statistics
   */
  async getQueueStatus() {
    try {
      const stats = await ApplicationQueue.getQueueStats();
      const readyCount = await ApplicationQueue.countDocuments({
        status: "pending",
        scheduledAt: { $lte: new Date() },
        $or: [
          { delayUntil: { $exists: false } },
          { delayUntil: { $lte: new Date() } },
        ],
      });

      return {
        isProcessing: this.isProcessing,
        queueStats: this.queueStats,
        statusBreakdown: stats,
        readyToProcess: readyCount,
        rateLimitingStatus: this.rateLimitingService.getRateLimitingStatus(),
      };
    } catch (error) {
      logger.error("Error getting queue status:", error);
      throw error;
    }
  }

  /**
   * Remove an application from the queue
   * @param {string} queueId - Queue item ID to remove
   * @param {string} userId - User ID (for authorization)
   */
  async removeFromQueue(queueId, userId) {
    try {
      logger.info(`Attempting to remove queue item: ${queueId} for user: ${userId}`);
      
      // Find the queue item and verify ownership
      const queueItem = await ApplicationQueue.findById(queueId);
      
      if (!queueItem) {
        const error = new Error("Queue item not found");
        error.statusCode = 404;
        logger.warn(`Queue item not found: ${queueId}`);
        throw error;
      }
      
      // Check if user owns this queue item
      if (queueItem.userId.toString() !== userId) {
        const error = new Error("Access denied - you can only remove your own queue items");
        error.statusCode = 403;
        logger.warn(`Access denied - user ${userId} tried to remove queue item ${queueId} owned by ${queueItem.userId}`);
        throw error;
      }
      
      // Handle processing items by cancelling them instead of removing
      if (queueItem.status === "processing") {
        logger.info(`Cancelling processing queue item: ${queueId}`);
        await ApplicationQueue.findByIdAndUpdate(queueId, {
          status: "cancelled",
          statusReason: "Cancelled by user during processing",
          completedAt: new Date()
        });
        
        return {
          success: true,
          message: "Processing application cancelled successfully",
          removedItem: {
            id: queueItem._id,
            listingId: queueItem.listingId,
            status: "cancelled"
          }
        };
      }
      
      // Remove the queue item for non-processing items
      await ApplicationQueue.findByIdAndDelete(queueId);
      
      logger.info(`Successfully removed queue item: ${queueId} for user: ${userId}`);
      
      return {
        success: true,
        message: "Application removed from queue successfully",
        removedItem: {
          id: queueItem._id,
          listingId: queueItem.listingId,
          status: queueItem.status
        }
      };
    } catch (error) {
      logger.error(`Error removing queue item ${queueId}:`, error);
      
      // If it's already a properly formatted error, re-throw it
      if (error.statusCode) {
        throw error;
      }
      
      // Otherwise, wrap in a generic error
      const genericError = new Error(`Failed to remove queue item: ${error.message}`);
      genericError.statusCode = 500;
      throw genericError;
    }
  }

  /**
   * Update rate limits configuration
   */
  updateRateLimits(newLimits) {
    try {
      this.rateLimitingService.updateRateLimits(newLimits);
      logger.info("Updated rate limits via rate limiting service");
    } catch (error) {
      logger.error("Error updating rate limits:", error);
      throw error;
    }
  }

  /**
   * Reset any items stuck in processing state
   */
  async resetStuckItems() {
    try {
      const stuckThreshold = new Date(Date.now() - 30 * 60 * 1000); // 30 minutes ago

      const result = await ApplicationQueue.updateMany(
        {
          status: "processing",
          updatedAt: { $lt: stuckThreshold },
        },
        {
          status: "pending",
          $unset: { processedAt: 1 },
        }
      );

      if (result.modifiedCount > 0) {
        logger.info(`Reset ${result.modifiedCount} stuck processing items`);
      }
    } catch (error) {
      logger.error("Error resetting stuck items:", error);
    }
  }

  /**
   * Schedule cleanup of expired items
   */
  scheduleCleanup() {
    // Run cleanup every hour
    setInterval(async () => {
      try {
        await this.cleanupExpiredItems();
      } catch (error) {
        logger.error("Error in cleanup task:", error);
      }
    }, 60 * 60 * 1000);
  }

  /**
   * Clean up expired queue items
   */
  async cleanupExpiredItems() {
    try {
      const expiredItems = await ApplicationQueue.findExpiredItems();

      for (const item of expiredItems) {
        await item.updateStatus("cancelled", new Error("Item expired"));
      }

      if (expiredItems.length > 0) {
        logger.info(`Cleaned up ${expiredItems.length} expired queue items`);
      }
    } catch (error) {
      logger.error("Error cleaning up expired items:", error);
    }
  }

  /**
   * Update queue statistics
   */
  async updateQueueStats() {
    try {
      const stats = await ApplicationQueue.aggregate([
        {
          $group: {
            _id: null,
            totalProcessed: {
              $sum: {
                $cond: [{ $in: ["$status", ["completed", "failed"]] }, 1, 0],
              },
            },
            successful: {
              $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
            },
            failed: { $sum: { $cond: [{ $eq: ["$status", "failed"] }, 1, 0] } },
            avgProcessingTime: { $avg: "$metadata.processingTime" },
          },
        },
      ]);

      if (stats.length > 0) {
        const stat = stats[0];
        this.queueStats.processed = stat.totalProcessed || 0;
        this.queueStats.successful = stat.successful || 0;
        this.queueStats.failed = stat.failed || 0;
        this.queueStats.averageProcessingTime = stat.avgProcessingTime || 0;
      }
    } catch (error) {
      logger.error("Error updating queue stats:", error);
    }
  }

  /**
   * Process application with AutoApplicationService (real form automation)
   */
  async processWithAutoApplicationService(queueItem) {
    const startTime = Date.now();

    try {
      // Get AutoApplicationService instance
      const autoApplicationService = require("./autoApplicationService");

      // Get user and auto settings
      const User = require("../models/User");
      const AutoApplicationSettings = require("../models/AutoApplicationSettings");

      const user = await User.findById(queueItem.userId);
      if (!user) {
        throw new Error("User not found");
      }

      const autoSettings = await AutoApplicationSettings.findByUserId(
        queueItem.userId
      );
      if (!autoSettings || !autoSettings.enabled) {
        throw new Error("Auto-application not enabled for user");
      }

      // Construct application object (same as AutoApplicationService does)
      const application = {
        _id: queueItem._id,
        userId: queueItem.userId,
        propertyUrl: queueItem.listingUrl, // Map listingUrl to propertyUrl
        status: "processing",
        generatedContent: queueItem.generatedContent,
        toObject: () => ({
          _id: queueItem._id,
          userId: queueItem.userId,
          propertyUrl: queueItem.listingUrl,
          status: "processing",
          generatedContent: queueItem.generatedContent,
        }),
      };

      // Construct userSettings (same as AutoApplicationService does)
      const userSettings = {
        formData: {
          firstName:
            autoSettings.personalInfo.fullName?.split(" ")[0] || "User",
          lastName:
            autoSettings.personalInfo.fullName?.split(" ").slice(1).join(" ") ||
            "",
          email: autoSettings.personalInfo.email || user.email,
          phone: autoSettings.personalInfo.phone || "",
          age: autoSettings.personalInfo.age?.toString() || "25",
          occupation: autoSettings.personalInfo.occupation || "Professional",
          monthlyIncome:
            autoSettings.personalInfo.monthlyIncome?.toString() || "3000",
          additionalInfo: "Automated application via ZakMakelaar platform.",
        },
        messageTemplates: {
          default:
            application.generatedContent?.message ||
            `Dear landlord,\n\nI am interested in this property and would like to schedule a viewing.\n\nBest regards,\n${
              autoSettings.personalInfo.fullName || user.name || user.email
            }`,
        },
      };

      // Use FormAutomationService (same as AutoApplicationService does)
      const FormAutomationService = require("./formAutomationService");
      const formAutomation = new FormAutomationService();

      logger.debug("Calling FormAutomationService.submitApplication", {
        itemId: queueItem._id,
        propertyUrl: application.propertyUrl,
        hasUserSettings: !!userSettings,
      });

      const result = await formAutomation.submitApplication(
        {
          ...application.toObject(),
          propertyUrl: queueItem.listingUrl, // Ensure we use the correct URL
        },
        userSettings
      );

      // Calculate processing time
      const processingTime = Date.now() - startTime;
      await queueItem.updateMetadata({ processingTime });

      // Update queue item with automation result
      if (result) {
        queueItem.automationResult = result;
        await queueItem.save();
      }

      // Clean up form automation
      await formAutomation.cleanup();

      return {
        success: result?.success || false,
        result: result,
        processingTime,
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      await queueItem.updateMetadata({ processingTime });

      logger.error("AutoApplicationService processing failed:", {
        itemId: queueItem._id,
        errorMessage: error.message,
        processingTime,
      });

      return {
        success: false,
        error: error,
        processingTime,
      };
    }
  }

  /**
   * Utility method to sleep for specified milliseconds
   */
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Shutdown the queue manager gracefully
   */
  async shutdown() {
    try {
      logger.info("Shutting down ApplicationQueueManager");

      this.stopProcessing();

      // Clear any remaining intervals
      if (this.processingInterval) {
        clearInterval(this.processingInterval);
      }

      logger.info("ApplicationQueueManager shutdown complete");
    } catch (error) {
      logger.error("Error during ApplicationQueueManager shutdown:", error);
    }
  }
}

module.exports = ApplicationQueueManager;

// If this file is run directly, initialize and start the queue manager
if (require.main === module) {
  const connectDB = require("../config/database").connectDB;

  async function startQueueManager() {
    try {
      console.log("🚀 Starting Application Queue Manager...");

      // Connect to database
      await connectDB();

      // Create and initialize the queue manager
      const queueManager = new ApplicationQueueManager();
      await queueManager.initialize();

      console.log("✅ Application Queue Manager started successfully!");
      console.log("🔄 Processing queue items...");

      // Handle graceful shutdown
      process.on("SIGINT", async () => {
        console.log("\n⏹️ Shutting down Application Queue Manager...");
        await queueManager.shutdown();
        process.exit(0);
      });

      process.on("SIGTERM", async () => {
        console.log("\n⏹️ Shutting down Application Queue Manager...");
        await queueManager.shutdown();
        process.exit(0);
      });
    } catch (error) {
      console.error("❌ Failed to start Application Queue Manager:", error);
      process.exit(1);
    }
  }

  startQueueManager();
}
