# Performance Optimization for Unified Schema Transformation

This document describes the performance optimizations implemented for the unified schema transformation pipeline.

## Overview

The performance optimization system enhances the transformation pipeline with:

1. **Caching** for frequently used transformation rules and mappings
2. **Concurrent processing** support for multiple scrapers
3. **Database query optimization** for unified schema data
4. **Memory usage optimization** for large batches
5. **Performance monitoring** and visualization

## Components

### 1. Optimized Schema Transformer

The `OptimizedSchemaTransformer` class wraps the base `SchemaTransformer` with performance enhancements:

- **Caching**: Stores transformation results to avoid redundant processing
- **Concurrent processing**: Uses worker threads for parallel transformation
- **Memory optimization**: Reduces memory footprint for large batches

```javascript
// Example usage
const baseTransformer = new SchemaTransformer(registry);
const optimizedTransformer = new OptimizedSchemaTransformer(baseTransformer, {
  cache: {
    stdTTL: 600, // 10 minutes
    maxKeys: 5000 // Store up to 5000 cached transformations
  },
  concurrent: {
    maxWorkers: 4, // Use 4 worker threads
    minBatchSize: 10 // Use concurrent processing for batches of 10 or more
  }
});

// Transform with caching
const result = await optimizedTransformer.transform(rawData, 'funda.nl', {
  useCache: true
});

// Batch transform with concurrent processing
const batchResult = await optimizedTransformer.batchTransform(items, 'funda.nl', {
  useConcurrent: true,
  useCache: true
});
```

### 2. Transformation Cache

The `TransformationCache` class provides efficient caching for transformation results:

- Uses `node-cache` for in-memory caching
- Configurable TTL (Time To Live) for cached items
- Cache statistics for monitoring

### 3. Concurrent Transformation Manager

The `ConcurrentTransformationManager` class enables parallel processing:

- Uses Node.js worker threads for CPU-intensive transformations
- Automatically scales based on available CPU cores
- Optimizes batch sizes for efficient processing

### 4. Database Optimizer

The `DatabaseOptimizer` class optimizes database operations:

- Creates optimal indexes for common query patterns
- Optimizes queries for better performance
- Reduces document size for storage efficiency

```javascript
// Create optimal indexes
await DatabaseOptimizer.createOptimalIndexes(PropertyModel);

// Optimize a query
const { query, options } = DatabaseOptimizer.optimizeQuery({
  location: 'Amsterdam',
  propertyType: 'apartment'
});

// Optimize a document for storage
const optimizedDoc = DatabaseOptimizer.optimizeForStorage(document);
```

### 5. Performance Monitor

The `PerformanceMonitor` class tracks performance metrics:

- Transformation speed and throughput
- Memory usage
- Cache hit rate
- Source-specific metrics

## Performance Dashboard

A real-time performance dashboard is available at `/performance-monitoring` to visualize:

- Transformation rate and duration
- Memory usage
- Cache efficiency
- Source distribution
- System status

## API Endpoints

Performance monitoring endpoints:

- `GET /api/monitoring/performance`: Get current performance metrics
- `POST /api/monitoring/performance/reset`: Reset performance metrics
- `GET /api/monitoring/database-stats`: Get database statistics
- `GET /api/monitoring/transformer-stats`: Get transformer statistics
- `POST /api/monitoring/transformer/clear-cache`: Clear transformation cache

## Performance Requirements

The optimized system meets the following requirements:

1. **WHEN data transformation occurs THEN the system SHALL complete processing within 100ms per property record**
   - Achieved through caching and optimized transformation logic

2. **WHEN multiple scrapers run concurrently THEN the system SHALL handle schema normalization without resource contention**
   - Achieved through worker threads and concurrent processing

3. **WHEN large batches of data are processed THEN the system SHALL maintain memory usage below 500MB for transformation operations**
   - Achieved through memory optimization and efficient data handling

## Running Performance Tests

```bash
npm run test:performance
```

This will run the performance tests to verify that the optimized transformation pipeline meets the requirements.

## Best Practices

1. **Use caching for repetitive transformations**: Enable caching for scrapers that frequently encounter the same properties
2. **Use concurrent processing for large batches**: Enable concurrent processing for batches of 10 or more items
3. **Monitor memory usage**: Keep an eye on memory usage in the performance dashboard
4. **Optimize database queries**: Use the `DatabaseOptimizer` for queries that involve unified schema data
5. **Clear cache periodically**: Clear the transformation cache if memory usage becomes too high