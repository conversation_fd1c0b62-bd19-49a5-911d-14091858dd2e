const User = require('../models/User');
const documentVaultService = require('./documentVaultService');
const { loggers } = require('./logger');

/**
 * UserProfileService - Manages user profiles and auto-application settings
 * 
 * This service provides:
 * - Personal information collection and validation
 * - Document requirement checking and upload validation
 * - Profile completeness calculation and user guidance
 * - Auto-application settings management
 */
class UserProfileService {
  constructor() {
    this.REQUIRED_PERSONAL_INFO_FIELDS = [
      'fullName', 'email', 'phone', 'dateOfBirth', 'nationality', 
      'occupation', 'employer', 'monthlyIncome', 'moveInDate', 
      'leaseDuration', 'numberOfOccupants'
    ];
    
    this.REQUIRED_DOCUMENT_TYPES = [
      'income_proof', 'employment_contract', 'bank_statement', 'id_document'
    ];
    
    this.OPTIONAL_DOCUMENT_TYPES = [
      'rental_reference'
    ];
  }

  /**
   * Initialize auto-application profile for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Initialized profile data
   */
  async initializeAutoApplicationProfile(userId) {
    try {
      loggers.app.info(`Initializing auto-application profile for user ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Initialize auto-application structure if it doesn't exist
      if (!user.autoApplication) {
        user.autoApplication = {
          enabled: false,
          settings: {
            maxApplicationsPerDay: 5,
            applicationTemplate: 'professional',
            autoSubmit: false,
            requireManualReview: true,
            notificationPreferences: {
              immediate: true,
              daily: true,
              weekly: false
            }
          },
          criteria: {},
          personalInfo: {},
          requiredDocuments: this.REQUIRED_DOCUMENT_TYPES.map(type => ({
            type,
            required: true,
            uploaded: false,
            lastChecked: new Date()
          })),
          profileCompleteness: {
            personalInfo: 0,
            documents: 0,
            overall: 0,
            lastCalculated: new Date()
          },
          applicationHistory: {
            totalApplications: 0,
            successfulApplications: 0,
            dailyApplicationCount: 0,
            dailyResetDate: new Date()
          }
        };
      }

      // Calculate initial completeness
      await this.calculateProfileCompleteness(userId);

      await user.save();

      loggers.app.info(`Auto-application profile initialized for user ${userId}`);
      
      return this.getAutoApplicationProfile(userId);

    } catch (error) {
      loggers.app.error(`Error initializing auto-application profile for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get auto-application profile for a user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Auto-application profile data
   */
  async getAutoApplicationProfile(userId) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.autoApplication) {
        return await this.initializeAutoApplicationProfile(userId);
      }

      // Calculate current completeness
      await this.calculateProfileCompleteness(userId);

      return {
        enabled: user.autoApplication.enabled,
        settings: user.autoApplication.settings,
        criteria: user.autoApplication.criteria,
        personalInfo: user.autoApplication.personalInfo,
        requiredDocuments: user.autoApplication.requiredDocuments,
        profileCompleteness: user.autoApplication.profileCompleteness,
        applicationHistory: user.autoApplication.applicationHistory,
        isReady: user.isAutoApplicationReady,
        canApplyToday: user.canApplyToday,
        successRate: user.autoApplicationSuccessRate
      };

    } catch (error) {
      loggers.app.error(`Error getting auto-application profile for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update personal information for auto-application
   * @param {string} userId - User ID
   * @param {Object} personalInfo - Personal information to update
   * @returns {Promise<Object>} Updated profile data
   */
  async updatePersonalInfo(userId, personalInfo) {
    try {
      loggers.app.info(`Updating personal info for user ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.autoApplication) {
        await this.initializeAutoApplicationProfile(userId);
        return await this.updatePersonalInfo(userId, personalInfo);
      }

      // Validate personal information
      const validationErrors = this.validatePersonalInfo(personalInfo);
      if (validationErrors.length > 0) {
        throw new Error(`Validation errors: ${validationErrors.join(', ')}`);
      }

      // Update personal info
      user.autoApplication.personalInfo = {
        ...user.autoApplication.personalInfo,
        ...personalInfo
      };

      // Recalculate completeness
      await this.calculateProfileCompleteness(userId);

      await user.save();

      loggers.app.info(`Personal info updated for user ${userId}`);
      
      return this.getAutoApplicationProfile(userId);

    } catch (error) {
      loggers.app.error(`Error updating personal info for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update auto-application settings
   * @param {string} userId - User ID
   * @param {Object} settings - Settings to update
   * @returns {Promise<Object>} Updated profile data
   */
  async updateSettings(userId, settings) {
    try {
      loggers.app.info(`Updating auto-application settings for user ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.autoApplication) {
        await this.initializeAutoApplicationProfile(userId);
        return await this.updateSettings(userId, settings);
      }

      // Validate settings
      const validationErrors = this.validateSettings(settings);
      if (validationErrors.length > 0) {
        throw new Error(`Validation errors: ${validationErrors.join(', ')}`);
      }

      // Update settings
      user.autoApplication.settings = {
        ...user.autoApplication.settings,
        ...settings
      };

      await user.save();

      loggers.app.info(`Auto-application settings updated for user ${userId}`);
      
      return this.getAutoApplicationProfile(userId);

    } catch (error) {
      loggers.app.error(`Error updating settings for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Update application criteria
   * @param {string} userId - User ID
   * @param {Object} criteria - Criteria to update
   * @returns {Promise<Object>} Updated profile data
   */
  async updateCriteria(userId, criteria) {
    try {
      loggers.app.info(`Updating application criteria for user ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.autoApplication) {
        await this.initializeAutoApplicationProfile(userId);
        return await this.updateCriteria(userId, criteria);
      }

      // Validate criteria
      const validationErrors = this.validateCriteria(criteria);
      if (validationErrors.length > 0) {
        throw new Error(`Validation errors: ${validationErrors.join(', ')}`);
      }

      // Update criteria
      user.autoApplication.criteria = {
        ...user.autoApplication.criteria,
        ...criteria
      };

      await user.save();

      loggers.app.info(`Application criteria updated for user ${userId}`);
      
      return this.getAutoApplicationProfile(userId);

    } catch (error) {
      loggers.app.error(`Error updating criteria for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Check and update document requirements
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Document status and requirements
   */
  async checkDocumentRequirements(userId) {
    try {
      loggers.app.info(`Checking document requirements for user ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.autoApplication) {
        await this.initializeAutoApplicationProfile(userId);
        return await this.checkDocumentRequirements(userId);
      }

      // Get user's uploaded documents
      const userDocuments = await documentVaultService.getDocuments(userId);
      
      // Update document status
      for (const requiredDoc of user.autoApplication.requiredDocuments) {
        const uploadedDoc = userDocuments.find(doc => doc.type === requiredDoc.type);
        
        requiredDoc.uploaded = !!uploadedDoc;
        requiredDoc.documentId = uploadedDoc ? uploadedDoc.id : null;
        requiredDoc.lastChecked = new Date();
      }

      // Recalculate completeness
      await this.calculateProfileCompleteness(userId);

      await user.save();

      const missingDocuments = user.autoApplication.requiredDocuments
        .filter(doc => doc.required && !doc.uploaded)
        .map(doc => doc.type);

      const result = {
        requiredDocuments: user.autoApplication.requiredDocuments,
        missingDocuments,
        allRequiredUploaded: missingDocuments.length === 0,
        documentCompleteness: user.autoApplication.profileCompleteness.documents
      };

      loggers.app.info(`Document requirements checked for user ${userId}: ${missingDocuments.length} missing`);
      
      return result;

    } catch (error) {
      loggers.app.error(`Error checking document requirements for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Calculate profile completeness percentage
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Completeness scores
   */
  async calculateProfileCompleteness(userId) {
    try {
      const user = await User.findById(userId);
      if (!user || !user.autoApplication) {
        throw new Error('User or auto-application profile not found');
      }

      // Calculate personal info completeness
      const personalInfoScore = this.calculatePersonalInfoCompleteness(user.autoApplication.personalInfo);
      
      // Calculate document completeness
      const documentScore = this.calculateDocumentCompleteness(user.autoApplication.requiredDocuments);
      
      // Calculate overall completeness (weighted average)
      const overallScore = Math.round((personalInfoScore * 0.6) + (documentScore * 0.4));

      // Update completeness scores
      user.autoApplication.profileCompleteness = {
        personalInfo: personalInfoScore,
        documents: documentScore,
        overall: overallScore,
        lastCalculated: new Date()
      };

      await user.save();

      return user.autoApplication.profileCompleteness;

    } catch (error) {
      loggers.app.error(`Error calculating profile completeness for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get profile guidance for user
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Guidance and recommendations
   */
  async getProfileGuidance(userId) {
    try {
      const user = await User.findById(userId);
      if (!user || !user.autoApplication) {
        throw new Error('User or auto-application profile not found');
      }

      const guidance = {
        nextSteps: [],
        warnings: [],
        recommendations: [],
        completeness: user.autoApplication.profileCompleteness
      };

      // Check personal info completeness
      const missingPersonalInfo = this.getMissingPersonalInfoFields(user.autoApplication.personalInfo);
      if (missingPersonalInfo.length > 0) {
        guidance.nextSteps.push({
          type: 'personal_info',
          title: 'Complete Personal Information',
          description: `Please provide: ${missingPersonalInfo.join(', ')}`,
          priority: 'high',
          fields: missingPersonalInfo
        });
      }

      // Check document requirements
      const missingDocuments = user.autoApplication.requiredDocuments
        .filter(doc => doc.required && !doc.uploaded)
        .map(doc => doc.type);

      if (missingDocuments.length > 0) {
        guidance.nextSteps.push({
          type: 'documents',
          title: 'Upload Required Documents',
          description: `Please upload: ${missingDocuments.join(', ')}`,
          priority: 'high',
          documents: missingDocuments
        });
      }

      // Check if ready for auto-application
      if (user.isAutoApplicationReady) {
        guidance.recommendations.push({
          type: 'ready',
          title: 'Ready for Auto-Application',
          description: 'Your profile is complete! You can now enable auto-application.',
          action: 'enable_auto_application'
        });
      } else {
        const completeness = user.autoApplication.profileCompleteness.overall;
        if (completeness < 50) {
          guidance.warnings.push({
            type: 'low_completeness',
            title: 'Profile Incomplete',
            description: 'Your profile is less than 50% complete. Auto-application will not be effective.',
            severity: 'high'
          });
        } else if (completeness < 80) {
          guidance.warnings.push({
            type: 'medium_completeness',
            title: 'Profile Partially Complete',
            description: 'Complete your profile to improve auto-application success rate.',
            severity: 'medium'
          });
        }
      }

      // Application history recommendations
      if (user.autoApplication.applicationHistory.totalApplications > 10) {
        const successRate = user.autoApplicationSuccessRate;
        if (successRate < 20) {
          guidance.recommendations.push({
            type: 'improve_success_rate',
            title: 'Improve Application Success Rate',
            description: 'Consider updating your application template or criteria to improve success rate.',
            action: 'review_template'
          });
        }
      }

      return guidance;

    } catch (error) {
      loggers.app.error(`Error getting profile guidance for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Enable/disable auto-application
   * @param {string} userId - User ID
   * @param {boolean} enabled - Enable/disable status
   * @returns {Promise<Object>} Updated profile data
   */
  async setAutoApplicationEnabled(userId, enabled) {
    try {
      loggers.app.info(`${enabled ? 'Enabling' : 'Disabling'} auto-application for user ${userId}`);

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (!user.autoApplication) {
        await this.initializeAutoApplicationProfile(userId);
        return await this.setAutoApplicationEnabled(userId, enabled);
      }

      // Check if profile is ready before enabling
      if (enabled && !user.isAutoApplicationReady) {
        const guidance = await this.getProfileGuidance(userId);
        throw new Error(`Profile not ready for auto-application. Missing: ${guidance.nextSteps.map(step => step.title).join(', ')}`);
      }

      user.autoApplication.enabled = enabled;
      await user.save();

      loggers.app.info(`Auto-application ${enabled ? 'enabled' : 'disabled'} for user ${userId}`);
      
      return this.getAutoApplicationProfile(userId);

    } catch (error) {
      loggers.app.error(`Error setting auto-application enabled status for user ${userId}:`, error);
      throw error;
    }
  }

  // Private helper methods

  /**
   * Validate personal information
   * @private
   */
  validatePersonalInfo(personalInfo) {
    const errors = [];

    if (personalInfo.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(personalInfo.email)) {
      errors.push('Invalid email format');
    }

    if (personalInfo.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(personalInfo.phone.replace(/[\s\-\(\)]/g, ''))) {
      errors.push('Invalid phone number format');
    }

    if (personalInfo.dateOfBirth) {
      const birthDate = new Date(personalInfo.dateOfBirth);
      const age = (new Date() - birthDate) / (365.25 * 24 * 60 * 60 * 1000);
      if (age < 18 || age > 100) {
        errors.push('Age must be between 18 and 100 years');
      }
    }

    if (personalInfo.monthlyIncome && personalInfo.monthlyIncome < 0) {
      errors.push('Monthly income cannot be negative');
    }

    if (personalInfo.leaseDuration && (personalInfo.leaseDuration < 1 || personalInfo.leaseDuration > 60)) {
      errors.push('Lease duration must be between 1 and 60 months');
    }

    if (personalInfo.numberOfOccupants && (personalInfo.numberOfOccupants < 1 || personalInfo.numberOfOccupants > 10)) {
      errors.push('Number of occupants must be between 1 and 10');
    }

    return errors;
  }

  /**
   * Validate settings
   * @private
   */
  validateSettings(settings) {
    const errors = [];

    if (settings.maxApplicationsPerDay && (settings.maxApplicationsPerDay < 1 || settings.maxApplicationsPerDay > 20)) {
      errors.push('Max applications per day must be between 1 and 20');
    }

    if (settings.applicationTemplate && !['professional', 'casual', 'student', 'expat'].includes(settings.applicationTemplate)) {
      errors.push('Invalid application template');
    }

    return errors;
  }

  /**
   * Validate criteria
   * @private
   */
  validateCriteria(criteria) {
    const errors = [];

    if (criteria.maxPrice && criteria.maxPrice < 0) {
      errors.push('Max price cannot be negative');
    }

    if (criteria.minRooms && criteria.maxRooms && criteria.minRooms > criteria.maxRooms) {
      errors.push('Min rooms cannot be greater than max rooms');
    }

    return errors;
  }

  /**
   * Calculate personal info completeness percentage
   * @private
   */
  calculatePersonalInfoCompleteness(personalInfo) {
    if (!personalInfo) return 0;

    const completedFields = this.REQUIRED_PERSONAL_INFO_FIELDS.filter(field => {
      const value = personalInfo[field];
      return value !== null && value !== undefined && value !== '';
    });

    return Math.round((completedFields.length / this.REQUIRED_PERSONAL_INFO_FIELDS.length) * 100);
  }

  /**
   * Calculate document completeness percentage
   * @private
   */
  calculateDocumentCompleteness(requiredDocuments) {
    if (!requiredDocuments || requiredDocuments.length === 0) return 0;

    const requiredDocs = requiredDocuments.filter(doc => doc.required);
    const uploadedDocs = requiredDocs.filter(doc => doc.uploaded);

    return Math.round((uploadedDocs.length / requiredDocs.length) * 100);
  }

  /**
   * Get missing personal info fields
   * @private
   */
  getMissingPersonalInfoFields(personalInfo) {
    if (!personalInfo) return this.REQUIRED_PERSONAL_INFO_FIELDS;

    return this.REQUIRED_PERSONAL_INFO_FIELDS.filter(field => {
      const value = personalInfo[field];
      return value === null || value === undefined || value === '';
    });
  }
}

module.exports = new UserProfileService();