const { catchAsync } = require('../middleware/errorHandler');
const { 
  formatAgentStatus, 
  validateAgentConfig,
  calculateAutonomyLevel,
  calculateAutonomyRate
} = require('../utils/agentUtils');

// Directly import the scraper service
const scraperService = require('../services/scraper');

// Get current agent status
const getAgentStatus = catchAsync(async (req, res) => {
  const status = scraperService.getAgentStatus();
  const metrics = scraperService.getScrapingMetrics();
  
  const formattedStatus = formatAgentStatus(
    metrics,
    status.isRunning,
    status.currentTask
  );
  
  res.status(200).json({
    status: 'success',
    data: formattedStatus,
  });
});

// Start the agent
const startAgent = catchAsync(async (req, res) => {
  const result = await scraperService.startAgent();
  const status = scraperService.getAgentStatus();
  const metrics = scraperService.getScrapingMetrics();
  
  const formattedStatus = formatAgentStatus(
    metrics,
    status.isRunning,
    status.currentTask
  );
  
  res.status(200).json({
    status: 'success',
    message: result.message,
    data: formattedStatus,
  });
});

// Stop the agent
const stopAgent = catchAsync(async (req, res) => {
  const result = await scraperService.stopAgent();
  const status = scraperService.getAgentStatus();
  const metrics = scraperService.getScrapingMetrics();
  
  const formattedStatus = formatAgentStatus(
    metrics,
    status.isRunning,
    status.currentTask
  );
  
  res.status(200).json({
    status: 'success',
    message: result.message,
    data: formattedStatus,
  });
});

// Update agent configuration
const updateAgentConfig = catchAsync(async (req, res) => {
  const { config } = req.body;
  
  if (!config) {
    return res.status(400).json({
      status: 'error',
      message: 'No configuration provided',
    });
  }
  
  // Validate the configuration
  const { valid, errors } = validateAgentConfig(config);
  if (!valid) {
    return res.status(400).json({
      status: 'error',
      message: 'Invalid configuration',
      errors,
    });
  }
  
  // Update the configuration
  const result = await scraperService.updateAgentConfig(config);
  const status = scraperService.getAgentStatus();
  const metrics = scraperService.getScrapingMetrics();
  
  const formattedStatus = formatAgentStatus(
    metrics,
    status.isRunning,
    status.currentTask
  );
  
  res.status(200).json({
    status: 'success',
    message: result.message,
    data: formattedStatus,
  });
});

// Get agent metrics
const getAgentMetrics = catchAsync(async (req, res) => {
  const metrics = scraperService.getScrapingMetrics();
  
  res.status(200).json({
    status: 'success',
    data: {
      ...metrics,
      autonomyLevel: calculateAutonomyLevel(metrics),
      autonomyRate: calculateAutonomyRate(metrics),
    },
  });
});

// Health check endpoint
const healthCheck = catchAsync(async (req, res) => {
  const status = scraperService.getAgentStatus();
  const metrics = scraperService.getScrapingMetrics();
  
  const autonomyLevel = calculateAutonomyLevel(metrics);
  const autonomyRate = calculateAutonomyRate(metrics);
  
  res.status(200).json({
    status: 'success',
    data: {
      status: status.isRunning ? 'active' : 'inactive',
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      metrics: {
        ...metrics,
        autonomyLevel,
        autonomyRate,
      },
    },
  });
});

// Export all controller functions
module.exports = {
  getAgentStatus,
  startAgent,
  stopAgent,
  updateAgentConfig,
  getAgentMetrics,
  healthCheck
};
