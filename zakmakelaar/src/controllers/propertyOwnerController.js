const propertyOwnerService = require('../services/propertyOwnerService');
const { loggers } = require('../services/logger');

/**
 * PropertyOwnerController - <PERSON>les HTTP requests for property owner management
 * 
 * This controller provides endpoints for:
 * - Property owner registration and verification (Requirements 10.1, 10.2)
 * - Property management (Requirements 10.3, 10.4)
 * - Tenant screening and applicant ranking (Requirements 10.4, 10.5, 10.6)
 */
class PropertyOwnerController {
  
  /**
   * Register user as property owner
   * POST /api/property-owner/register
   * Requirements: 10.1, 10.2
   */
  async registerPropertyOwner(req, res, next) {
    try {
      const userId = req.user._id;
      const registrationData = req.body;

      loggers.app.info(`Property owner registration request from user ${userId}`);

      // Validate required fields
      const requiredFields = ['businessRegistration'];
      const missingFields = requiredFields.filter(field => !registrationData[field]);
      
      if (missingFields.length > 0) {
        return res.status(400).json({
          status: 'error',
          message: 'Missing required fields',
          missingFields
        });
      }

      const result = await propertyOwnerService.registerAsPropertyOwner(userId, registrationData);

      res.status(201).json({
        status: 'success',
        message: 'Property owner registration completed successfully',
        data: result
      });

    } catch (error) {
      loggers.app.error('Error in registerPropertyOwner:', error);
      next(error);
    }
  }

  /**
   * Get property owner profile
   * GET /api/property-owner/profile
   * Requirements: 10.1, 10.2
   */
  async getOwnerProfile(req, res, next) {
    try {
      const userId = req.user._id;

      loggers.app.info(`Getting property owner profile for user ${userId}`);

      const profile = await propertyOwnerService.getOwnerProfile(userId);

      res.status(200).json({
        status: 'success',
        data: profile
      });

    } catch (error) {
      loggers.app.error('Error in getOwnerProfile:', error);
      next(error);
    }
  }

  /**
   * Update property owner profile
   * PUT /api/property-owner/profile
   * Requirements: 10.1, 10.2
   */
  async updateOwnerProfile(req, res, next) {
    try {
      const userId = req.user._id;
      const profileData = req.body;

      loggers.app.info(`Updating property owner profile for user ${userId}`);

      const updatedProfile = await propertyOwnerService.updateOwnerProfile(userId, profileData);

      res.status(200).json({
        status: 'success',
        message: 'Profile updated successfully',
        data: updatedProfile
      });

    } catch (error) {
      loggers.app.error('Error in updateOwnerProfile:', error);
      next(error);
    }
  }

  /**
   * Verify property owner business registration
   * POST /api/property-owner/verify
   * Requirements: 10.2, 10.3
   */
  async verifyPropertyOwner(req, res, next) {
    try {
      const userId = req.user._id;
      const { documents = [] } = req.body;

      loggers.app.info(`Property owner verification request from user ${userId}`);

      const verificationResult = await propertyOwnerService.verifyPropertyOwner(userId, documents);

      res.status(200).json({
        status: 'success',
        message: 'Property owner verification completed',
        data: verificationResult
      });

    } catch (error) {
      loggers.app.error('Error in verifyPropertyOwner:', error);
      next(error);
    }
  }

  /**
   * Get property owner dashboard
   * GET /api/property-owner/dashboard
   * Requirements: 10.4, 10.5
   */
  async getDashboard(req, res, next) {
    try {
      const userId = req.user._id;

      loggers.app.info(`Dashboard request from property owner ${userId}`);

      const dashboardData = await propertyOwnerService.getPropertyOwnerDashboard(userId);

      res.status(200).json({
        status: 'success',
        data: dashboardData
      });

    } catch (error) {
      loggers.app.error('Error in getDashboard:', error);
      next(error);
    }
  }

  /**
   * Screen tenants for a property
   * POST /api/property-owner/screen-tenants/:propertyId
   * Requirements: 10.4, 10.5, 10.6
   */
  async screenTenants(req, res, next) {
    try {
      const { propertyId } = req.params;
      const { applicationIds = [] } = req.body;

      loggers.app.info(`Tenant screening request for property ${propertyId}`);

      const screeningResult = await propertyOwnerService.screenTenants(propertyId, applicationIds);

      res.status(200).json({
        status: 'success',
        message: 'Tenant screening completed',
        data: screeningResult
      });

    } catch (error) {
      loggers.app.error('Error in screenTenants:', error);
      next(error);
    }
  }

  /**
   * Rank applicants for a property
   * POST /api/property-owner/rank-applicants/:propertyId
   * Requirements: 10.5, 10.6
   */
  async rankApplicants(req, res, next) {
    try {
      const { propertyId } = req.params;
      const criteria = req.body;

      loggers.app.info(`Applicant ranking request for property ${propertyId}`);

      const rankingResult = await propertyOwnerService.rankApplicants(propertyId, criteria);

      res.status(200).json({
        status: 'success',
        message: 'Applicant ranking completed',
        data: rankingResult
      });

    } catch (error) {
      loggers.app.error('Error in rankApplicants:', error);
      next(error);
    }
  }

  /**
   * Get properties for property owner
   * GET /api/property-owner/properties
   * Requirements: 10.3, 10.4
   */
  async getProperties(req, res, next) {
    try {
      const userId = req.user._id;

      loggers.app.info(`Properties request from property owner ${userId}`);

      const result = await propertyOwnerService.getProperties(userId);

      res.status(200).json({
        status: 'success',
        message: 'Properties retrieved successfully',
        data: result.data,
        total: result.total
      });

    } catch (error) {
      loggers.app.error('Error in getProperties:', error);
      next(error);
    }
  }

  /**
   * Get single property details for property owner
   * GET /api/property-owner/properties/:propertyId
   * Requirements: 10.3, 10.4
   */
  async getPropertyDetails(req, res, next) {
    try {
      const userId = req.user._id;
      const { propertyId } = req.params;

      loggers.app.info(`Property details request for ${propertyId} from property owner ${userId}`);

      const result = await propertyOwnerService.getPropertyDetails(userId, propertyId);

      res.status(200).json({
        status: 'success',
        message: 'Property details retrieved successfully',
        data: result.data
      });

    } catch (error) {
      loggers.app.error('Error in getPropertyDetails:', error);
      next(error);
    }
  }

  /**
   * Manage properties
   * POST /api/property-owner/properties - Add property
   * PUT /api/property-owner/properties/:propertyId - Update property
   * DELETE /api/property-owner/properties/:propertyId - Remove property
   * Requirements: 10.3, 10.4
   */
  async manageProperties(req, res, next) {
    try {
      const userId = req.user._id;
      const { method } = req;
      const { propertyId } = req.params;

      let action, propertyData;

      switch (method) {
        case 'POST':
          action = 'add';
          propertyData = req.body;
          break;
        case 'PUT':
          action = 'update';
          propertyData = { ...req.body, propertyId };
          break;
        case 'DELETE':
          action = 'remove';
          propertyData = { propertyId };
          break;
        default:
          return res.status(405).json({
            status: 'error',
            message: 'Method not allowed'
          });
      }

      loggers.app.info(`Property management request: ${action} from user ${userId}`);

      const result = await propertyOwnerService.manageProperties(userId, action, propertyData);

      res.status(200).json({
        status: 'success',
        message: `Property ${action} completed successfully`,
        data: result
      });

    } catch (error) {
      loggers.app.error('Error in manageProperties:', error);
      next(error);
    }
  }

  /**
   * Activate property listing
   * PUT /api/property-owner/properties/:propertyId/activate
   * Requirements: 10.3, 10.4
   */
  async activateProperty(req, res, next) {
    try {
      const userId = req.user._id;
      const { propertyId } = req.params;

      loggers.app.info(`Property activation request for ${propertyId} from user ${userId}`);

      const result = await propertyOwnerService.manageProperties(userId, 'activate', { propertyId });

      res.status(200).json({
        status: 'success',
        message: 'Property activated successfully',
        data: result
      });

    } catch (error) {
      loggers.app.error('Error in activateProperty:', error);
      next(error);
    }
  }

  /**
   * Deactivate property listing
   * PUT /api/property-owner/properties/:propertyId/deactivate
   * Requirements: 10.3, 10.4
   */
  async deactivateProperty(req, res, next) {
    try {
      const userId = req.user._id;
      const { propertyId } = req.params;

      loggers.app.info(`Property deactivation request for ${propertyId} from user ${userId}`);

      const result = await propertyOwnerService.manageProperties(userId, 'deactivate', { propertyId });

      res.status(200).json({
        status: 'success',
        message: 'Property deactivated successfully',
        data: result
      });

    } catch (error) {
      loggers.app.error('Error in deactivateProperty:', error);
      next(error);
    }
  }

  /**
   * Validate property for activation
   * GET /api/property-owner/properties/:propertyId/validate
   * Requirements: 10.3, 10.4
   */
  async validateProperty(req, res, next) {
    try {
      const userId = req.user._id;
      const { propertyId } = req.params;

      loggers.app.info(`Property validation request for ${propertyId} from user ${userId}`);

      const result = await propertyOwnerService.validatePropertyForActivation(userId, propertyId);

      res.status(200).json({
        status: 'success',
        message: 'Property validation completed',
        data: result
      });

    } catch (error) {
      loggers.app.error('Error in validateProperty:', error);
      next(error);
    }
  }

  /**
   * Generate property report
   * GET /api/property-owner/properties/:propertyId/report?type=comprehensive|screening|performance
   * Requirements: 10.6
   */
  async generatePropertyReport(req, res, next) {
    try {
      const { propertyId } = req.params;
      const { type = 'comprehensive' } = req.query;

      loggers.app.info(`Property report request for ${propertyId}, type: ${type}`);

      const report = await propertyOwnerService.generatePropertyReport(propertyId, type);

      res.status(200).json({
        status: 'success',
        message: 'Property report generated successfully',
        data: report
      });

    } catch (error) {
      loggers.app.error('Error in generatePropertyReport:', error);
      next(error);
    }
  }

  /**
   * Get property owner verification status
   * GET /api/property-owner/verification-status
   * Requirements: 10.2
   */
  async getVerificationStatus(req, res, next) {
    try {
      const userId = req.user._id;

      loggers.app.info(`Verification status request from user ${userId}`);

      // Get user data to check verification status
      const User = require('../models/User');
      const user = await User.findById(userId).select('propertyOwner');

      if (!user || !user.propertyOwner.isPropertyOwner) {
        return res.status(404).json({
          status: 'error',
          message: 'User is not registered as a property owner'
        });
      }

      res.status(200).json({
        status: 'success',
        data: {
          isPropertyOwner: user.propertyOwner.isPropertyOwner,
          verificationStatus: user.propertyOwner.verificationStatus,
          businessRegistration: user.propertyOwner.businessRegistration,
          taxNumber: user.propertyOwner.taxNumber ? '***' + user.propertyOwner.taxNumber.slice(-4) : null,
          bankAccount: user.propertyOwner.bankAccount ? '***' + user.propertyOwner.bankAccount.slice(-4) : null,
          properties: user.propertyOwner.properties.length
        }
      });

    } catch (error) {
      loggers.app.error('Error in getVerificationStatus:', error);
      next(error);
    }
  }

  /**
   * Get all applications for property owner
   * GET /api/property-owner/applications
   * Requirements: 10.4, 10.5
   */
  async getAllApplications(req, res, next) {
    try {
      const userId = req.user.id;
      const { status, page = 1, limit = 50 } = req.query;

      loggers.app.info(`All applications request for property owner ${userId}`);

      // Import models
      const Application = require('../models/Application');
      const Property = require('../models/Property');

      // First, get all properties owned by this user
      console.log('🔍 DEBUG: Looking for properties owned by user:', userId);
      const ownerProperties = await Property.find({ 'owner.userId': userId }).select('_id title');
      console.log('🏠 DEBUG: Found properties:', ownerProperties.map(p => ({ id: p._id.toString(), title: p.title })));
      const propertyIds = ownerProperties.map(prop => prop._id);
      console.log('📋 DEBUG: Property IDs for query:', propertyIds.map(id => id.toString()));

      if (propertyIds.length === 0) {
        return res.status(200).json({
          status: 'success',
          success: true,
          data: [],
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: 0,
            pages: 0
          }
        });
      }

      // Build query for applications
      let query = {
        'property.propertyId': { $in: propertyIds }
      };

      // Add status filter if provided
      if (status) {
        query.status = status;
      }

      console.log('🔍 DEBUG: Application query:', JSON.stringify(query, null, 2));

      // Get applications with pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      const applications = await Application.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit))
        .lean();

      console.log('📋 DEBUG: Raw applications from database:', applications.length);
      applications.forEach((app, index) => {
        console.log(`   ${index + 1}. App ID: ${app._id}, Property ID: ${app.property?.propertyId}, Applicant: ${app.applicant?.snapshot?.email}, Status: ${app.status}`);
      });

      // Get total count for pagination
      const total = await Application.countDocuments(query);
      console.log('📊 DEBUG: Total applications matching query:', total);

      // Transform applications to match frontend expectations
      const transformedApplications = applications.map(app => ({
        _id: app._id.toString(),
        id: app._id.toString(),
        applicantName: app.applicant?.snapshot?.name || 'Unknown Applicant',
        applicantEmail: app.applicant?.snapshot?.email || 'No email provided',
        applicantPhone: app.applicant?.snapshot?.phone || 'No phone provided',
        propertyId: app.property?.propertyId?.toString() || '',
        propertyAddress: app.property?.snapshot?.address || 'Address not available',
        applicationDate: app.submittedAt ? app.submittedAt.toISOString().split('T')[0] : app.createdAt.toISOString().split('T')[0],
        status: app.status === 'submitted' ? 'pending' : app.status, // Map 'submitted' to 'pending' for frontend
        creditScore: app.screening?.overallScore || app.applicant?.snapshot?.tenantScore || null,
        incomeVerified: app.screening?.results?.incomeVerification?.verified || false,
        backgroundCheckPassed: app.screening?.results?.backgroundCheck?.completed && 
                               (!app.screening?.results?.backgroundCheck?.issues || 
                                app.screening?.results?.backgroundCheck?.issues.length === 0),
        documents: app.documents?.map(doc => ({
          id: doc._id?.toString() || Math.random().toString(36).substr(2, 9),
          name: doc.originalName || doc.filename || 'Document',
          type: doc.type || 'other',
          url: `/documents/${doc.filename}` || '#'
        })) || [],
        // Additional fields that might be useful
        moveInDate: app.applicationData?.moveInDate,
        monthlyIncome: app.applicationData?.employment?.monthlyIncome,
        personalMessage: app.applicationData?.personalMessage,
        submittedAt: app.submittedAt,
        completenessScore: app.completenessScore
      }));

      loggers.app.info(`Found ${transformedApplications.length} applications for property owner ${userId}`);

      res.status(200).json({
        status: 'success',
        success: true,
        data: transformedApplications,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });

    } catch (error) {
      loggers.app.error('Error in getAllApplications:', error);
      next(error);
    }
  }

  /**
   * Get applications for a property
   * GET /api/property-owner/properties/:propertyId/applications
   * Requirements: 10.4, 10.5
   */
  async getPropertyApplications(req, res, next) {
    try {
      const { propertyId } = req.params;
      const { status, page = 1, limit = 20 } = req.query;

      loggers.app.info(`Applications request for property ${propertyId}`);

      // Mock application data - in production would query Application model
      const applications = [
        {
          id: 'app1',
          applicantName: 'John Doe',
          applicantEmail: '<EMAIL>',
          tenantScore: 85,
          status: 'submitted',
          appliedAt: new Date(),
          moveInDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        },
        {
          id: 'app2',
          applicantName: 'Jane Smith',
          applicantEmail: '<EMAIL>',
          tenantScore: 72,
          status: 'under_review',
          appliedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
          moveInDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000)
        }
      ];

      const filteredApplications = status 
        ? applications.filter(app => app.status === status)
        : applications;

      res.status(200).json({
        status: 'success',
        data: {
          applications: filteredApplications,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: filteredApplications.length,
            pages: Math.ceil(filteredApplications.length / limit)
          }
        }
      });

    } catch (error) {
      loggers.app.error('Error in getPropertyApplications:', error);
      next(error);
    }
  }

  /**
   * Update application status
   * PUT /api/property-owner/applications/:applicationId/status
   * Requirements: 10.5, 10.6
   */
  async updateApplicationStatus(req, res, next) {
    try {
      const { applicationId } = req.params;
      const { status, reason, notes } = req.body;
      const userId = req.user._id;

      loggers.app.info(`Application status update request for ${applicationId} to ${status}`);

      // Import models
      const Application = require('../models/Application');
      const Property = require('../models/Property');

      // Validate status
      const validStatuses = ['pending', 'under_review', 'screening', 'shortlisted', 'interview', 'approved', 'conditional', 'rejected'];
      if (!validStatuses.includes(status)) {
        return res.status(400).json({
          status: 'error',
          message: 'Invalid application status'
        });
      }

      // Find the application
      const application = await Application.findById(applicationId);
      if (!application) {
        return res.status(404).json({
          status: 'error',
          message: 'Application not found'
        });
      }

      // Verify that the property belongs to this owner
      const property = await Property.findById(application.property.propertyId);
      if (!property || property.owner.userId.toString() !== userId.toString()) {
        return res.status(403).json({
          status: 'error',
          message: 'Unauthorized: You do not own this property'
        });
      }

      // Store previous status
      const previousStatus = application.status;

      // Map frontend status to backend status
      let backendStatus = status;
      if (status === 'pending') {
        backendStatus = 'submitted'; // Map 'pending' back to 'submitted' in database
      }

      // Update application status using the model method
      await application.updateStatus(backendStatus, reason, userId);

      // Add notes if provided
      if (notes) {
        application.statusHistory[application.statusHistory.length - 1].notes = notes;
        await application.save();
      }

      loggers.app.info(`Application ${applicationId} status updated from ${previousStatus} to ${backendStatus}`);

      const result = {
        applicationId,
        previousStatus,
        newStatus: status, // Return the frontend status
        updatedBy: userId,
        updatedAt: new Date(),
        reason,
        notes
      };

      res.status(200).json({
        status: 'success',
        message: 'Application status updated successfully',
        data: result
      });

    } catch (error) {
      loggers.app.error('Error in updateApplicationStatus:', error);
      next(error);
    }
  }

  /**
   * Get property owner statistics
   * GET /api/property-owner/statistics
   * Requirements: 10.6
   */
  async getStatistics(req, res, next) {
    try {
      const userId = req.user._id;
      const { period = '30d' } = req.query;

      loggers.app.info(`Statistics request from property owner ${userId} for period ${period}`);

      // Mock statistics - in production would calculate from actual data
      const statistics = {
        period,
        properties: {
          total: 5,
          active: 4,
          rented: 3,
          vacant: 1
        },
        applications: {
          total: 45,
          pending: 8,
          approved: 12,
          rejected: 15,
          withdrawn: 10
        },
        screening: {
          averageScore: 73.5,
          totalScreened: 35,
          approvalRate: 0.34,
          averageProcessingTime: 3.2 // days
        },
        financial: {
          totalRevenue: 12500,
          averageRent: 2500,
          occupancyRate: 0.85,
          renewalRate: 0.78
        },
        performance: {
          averageTimeToRent: 18, // days
          viewingToApplicationRate: 0.45,
          applicationToApprovalRate: 0.27
        }
      };

      res.status(200).json({
        status: 'success',
        data: statistics
      });

    } catch (error) {
      loggers.app.error('Error in getStatistics:', error);
      next(error);
    }
  }

  /**
   * Upload property images
   * POST /api/property-owner/properties/:propertyId/images
   */
  async uploadPropertyImages(req, res, next) {
    try {
      const userId = req.user._id;
      const propertyId = req.params.propertyId;
      const files = req.files;

      loggers.app.info(`Image upload request from user ${userId} for property ${propertyId}`);

      if (!files || files.length === 0) {
        return res.status(400).json({
          status: 'error',
          message: 'No images provided'
        });
      }

      // Process uploaded files
      const uploadedImages = files.map(file => ({
        url: `/uploads/property-images/${file.filename}`,
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype
      }));

      loggers.app.info(`Successfully uploaded ${uploadedImages.length} images for property ${propertyId}`);

      res.status(200).json({
        status: 'success',
        message: 'Images uploaded successfully',
        data: {
          images: uploadedImages
        }
      });

    } catch (error) {
      loggers.app.error('Error in uploadPropertyImages:', error);
      next(error);
    }
  }
}

module.exports = new PropertyOwnerController();