const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const app = require('../../index');
const User = require('../../models/User');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationQueue = require('../../models/ApplicationQueue');
const ApplicationResult = require('../../models/ApplicationResult');
const jwt = require('jsonwebtoken');
const config = require('../../config/config');

describe('Auto Application API Integration Tests', () => {
  let mongoServer;
  let testUser;
  let authToken;

  beforeAll(async () => {
    // Start in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear all collections
    await User.deleteMany({});
    await AutoApplicationSettings.deleteMany({});
    await ApplicationQueue.deleteMany({});
    await ApplicationResult.deleteMany({});

    // Create test user
    testUser = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      role: 'user'
    });

    // Generate auth token
    authToken = jwt.sign(
      { id: testUser._id, email: testUser.email },
      config.jwtSecret,
      { expiresIn: '1h' }
    );
  });

  describe('POST /api/auto-application/enable', () => {
    it('should enable auto-application for user', async () => {
      const response = await request(app)
        .post('/api/auto-application/enable')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Auto-application enabled successfully');
      expect(response.body.data).toBeDefined();
    });

    it('should return 401 without authentication', async () => {
      await request(app)
        .post('/api/auto-application/enable')
        .expect(401);
    });
  });

  describe('POST /api/auto-application/disable', () => {
    beforeEach(async () => {
      // Create auto-application settings
      await AutoApplicationSettings.create({
        userId: testUser._id,
        isEnabled: true,
        maxApplicationsPerDay: 5,
        delayBetweenApplications: 30
      });
    });

    it('should disable auto-application for user', async () => {
      const response = await request(app)
        .post('/api/auto-application/disable')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Auto-application disabled successfully');
    });
  });

  describe('GET /api/auto-application/settings', () => {
    beforeEach(async () => {
      await AutoApplicationSettings.create({
        userId: testUser._id,
        isEnabled: true,
        maxApplicationsPerDay: 5,
        delayBetweenApplications: 30,
        targetCriteria: {
          maxPrice: 2000,
          minRooms: 2,
          propertyTypes: ['apartment']
        }
      });
    });

    it('should get user auto-application settings', async () => {
      const response = await request(app)
        .get('/api/auto-application/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.isEnabled).toBe(true);
      expect(response.body.data.maxApplicationsPerDay).toBe(5);
      expect(response.body.data.targetCriteria.maxPrice).toBe(2000);
    });
  });

  describe('POST /api/auto-application/settings', () => {
    it('should update auto-application settings', async () => {
      const settingsData = {
        isEnabled: true,
        maxApplicationsPerDay: 10,
        delayBetweenApplications: 45,
        targetCriteria: {
          maxPrice: 2500,
          minRooms: 3,
          propertyTypes: ['apartment', 'house']
        }
      };

      const response = await request(app)
        .post('/api/auto-application/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(settingsData)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.maxApplicationsPerDay).toBe(10);
      expect(response.body.data.targetCriteria.maxPrice).toBe(2500);
    });

    it('should validate settings data', async () => {
      const invalidData = {
        isEnabled: 'not-boolean',
        maxApplicationsPerDay: 25 // exceeds maximum
      };

      await request(app)
        .post('/api/auto-application/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });
  });

  describe('GET /api/auto-application/history', () => {
    beforeEach(async () => {
      // Create test application results
      await ApplicationResult.create([
        {
          userId: testUser._id,
          listingId: new mongoose.Types.ObjectId(),
          listingUrl: 'https://example.com/listing1',
          status: 'success',
          submittedAt: new Date(),
          responseTime: 1500
        },
        {
          userId: testUser._id,
          listingId: new mongoose.Types.ObjectId(),
          listingUrl: 'https://example.com/listing2',
          status: 'failed',
          errorDetails: { message: 'Form submission failed' }
        }
      ]);
    });

    it('should get application history', async () => {
      const response = await request(app)
        .get('/api/auto-application/history')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.applications).toHaveLength(2);
      expect(response.body.data.total).toBe(2);
      expect(response.body.data.summary).toBeDefined();
      expect(response.body.data.summary.totalApplications).toBe(2);
    });

    it('should filter history by status', async () => {
      const response = await request(app)
        .get('/api/auto-application/history?status=success')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.applications).toHaveLength(1);
      expect(response.body.data.applications[0].status).toBe('success');
    });

    it('should paginate results', async () => {
      const response = await request(app)
        .get('/api/auto-application/history?limit=1&offset=1')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.applications).toHaveLength(1);
      expect(response.body.data.limit).toBe(1);
      expect(response.body.data.offset).toBe(1);
    });
  });

  describe('GET /api/auto-application/documents', () => {
    it('should get user documents for auto-application', async () => {
      const response = await request(app)
        .get('/api/auto-application/documents')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.documents).toBeDefined();
      expect(response.body.data.requiredDocuments).toBeDefined();
      expect(response.body.data.completeness).toBeDefined();
    });

    it('should filter documents by type', async () => {
      const response = await request(app)
        .get('/api/auto-application/documents?type=income_proof')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
    });
  });

  describe('POST /api/auto-application/documents/upload', () => {
    it('should validate document upload parameters', async () => {
      await request(app)
        .post('/api/auto-application/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('documentType', 'invalid_type')
        .expect(400);
    });

    it('should require files for upload', async () => {
      const response = await request(app)
        .post('/api/auto-application/documents/upload')
        .set('Authorization', `Bearer ${authToken}`)
        .field('documentType', 'income_proof')
        .expect(400);

      expect(response.body.message).toBe('No files uploaded');
    });
  });

  describe('GET /api/auto-application/status', () => {
    beforeEach(async () => {
      await AutoApplicationSettings.create({
        userId: testUser._id,
        isEnabled: true,
        maxApplicationsPerDay: 5
      });

      await ApplicationQueue.create({
        userId: testUser._id,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://example.com/listing',
        status: 'pending',
        priority: 5
      });
    });

    it('should get real-time auto-application status', async () => {
      const response = await request(app)
        .get('/api/auto-application/status')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.isEnabled).toBe(true);
      expect(response.body.data.currentQueue).toBeDefined();
      expect(response.body.data.todaysActivity).toBeDefined();
      expect(response.body.data.systemHealth).toBeDefined();
    });
  });

  describe('GET /api/auto-application/queue', () => {
    beforeEach(async () => {
      await ApplicationQueue.create([
        {
          userId: testUser._id,
          listingId: new mongoose.Types.ObjectId(),
          listingUrl: 'https://example.com/listing1',
          status: 'pending',
          priority: 5
        },
        {
          userId: testUser._id,
          listingId: new mongoose.Types.ObjectId(),
          listingUrl: 'https://example.com/listing2',
          status: 'processing',
          priority: 8
        }
      ]);
    });

    it('should get user application queue', async () => {
      const response = await request(app)
        .get('/api/auto-application/queue')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.applications).toHaveLength(2);
    });

    it('should filter queue by status', async () => {
      const response = await request(app)
        .get('/api/auto-application/queue?status=pending')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.applications).toHaveLength(1);
      expect(response.body.data.applications[0].status).toBe('pending');
    });
  });

  describe('POST /api/auto-application/queue', () => {
    it('should add listing to application queue', async () => {
      const queueData = {
        listingId: new mongoose.Types.ObjectId().toString(),
        listingUrl: 'https://example.com/listing',
        priority: 7
      };

      const response = await request(app)
        .post('/api/auto-application/queue')
        .set('Authorization', `Bearer ${authToken}`)
        .send(queueData)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Application added to queue successfully');
      expect(response.body.data.listingUrl).toBe(queueData.listingUrl);
    });

    it('should validate queue data', async () => {
      const invalidData = {
        listingId: 'invalid-id',
        listingUrl: 'not-a-url'
      };

      await request(app)
        .post('/api/auto-application/queue')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidData)
        .expect(400);
    });
  });

  describe('GET /api/auto-application/stats', () => {
    beforeEach(async () => {
      await ApplicationResult.create([
        {
          userId: testUser._id,
          listingId: new mongoose.Types.ObjectId(),
          status: 'success',
          submittedAt: new Date(),
          responseTime: 1200
        },
        {
          userId: testUser._id,
          listingId: new mongoose.Types.ObjectId(),
          status: 'failed',
          responseTime: 800
        }
      ]);
    });

    it('should get application statistics', async () => {
      const response = await request(app)
        .get('/api/auto-application/stats')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.totalApplications).toBe(2);
      expect(response.body.data.successfulApplications).toBe(1);
      expect(response.body.data.failedApplications).toBe(1);
      expect(response.body.data.successRate).toBe(50);
    });
  });

  describe('POST /api/auto-application/pause', () => {
    beforeEach(async () => {
      await AutoApplicationSettings.create({
        userId: testUser._id,
        isEnabled: true
      });
    });

    it('should pause auto-application processing', async () => {
      const response = await request(app)
        .post('/api/auto-application/pause')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ duration: 60, reason: 'Testing pause functionality' })
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Auto-application paused successfully');
    });
  });

  describe('POST /api/auto-application/resume', () => {
    beforeEach(async () => {
      await AutoApplicationSettings.create({
        userId: testUser._id,
        isEnabled: true,
        isPaused: true
      });
    });

    it('should resume auto-application processing', async () => {
      const response = await request(app)
        .post('/api/auto-application/resume')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Auto-application resumed successfully');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid user ID in token', async () => {
      const invalidToken = jwt.sign(
        { id: 'invalid-id', email: '<EMAIL>' },
        config.jwtSecret,
        { expiresIn: '1h' }
      );

      await request(app)
        .get('/api/auto-application/settings')
        .set('Authorization', `Bearer ${invalidToken}`)
        .expect(500);
    });

    it('should handle expired token', async () => {
      const expiredToken = jwt.sign(
        { id: testUser._id, email: testUser.email },
        config.jwtSecret,
        { expiresIn: '-1h' }
      );

      await request(app)
        .get('/api/auto-application/settings')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);
    });

    it('should handle malformed token', async () => {
      await request(app)
        .get('/api/auto-application/settings')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });

  describe('Rate Limiting', () => {
    it('should respect API rate limits', async () => {
      // This test would need to be implemented based on your rate limiting configuration
      // For now, we'll just verify the endpoint responds correctly
      const response = await request(app)
        .get('/api/auto-application/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
    });
  });
});