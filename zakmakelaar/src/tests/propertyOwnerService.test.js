const mongoose = require('mongoose');
const propertyOwnerService = require('../services/propertyOwnerService');
const User = require('../models/User');
const Property = require('../models/Property');
const Application = require('../models/Application');
const tenantScoringService = require('../services/tenantScoringService');
const aiService = require('../services/aiService');
const cacheService = require('../services/cacheService');

// Mock dependencies
jest.mock('../services/tenantScoringService');
jest.mock('../services/aiService');
jest.mock('../services/cacheService');
jest.mock('../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn()
    }
  }
}));

describe('PropertyOwnerService', () => {
  let testUser;
  let testPropertyOwner;

  beforeAll(async () => {
    // Connect to test database
    const mongoUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/zakmakelaar_test';
    await mongoose.connect(mongoUri);
  });

  beforeEach(async () => {
    // Clear test data
    await User.deleteMany({});
    await Property.deleteMany({});
    await Application.deleteMany({});

    // Create test user with unique email
    const timestamp = Date.now();
    testUser = new User({
      email: `test${timestamp}@example.com`,
      password: 'password123',
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        userType: ['property_owner']
      }
    });
    await testUser.save();

    // Create test property owner with unique email
    testPropertyOwner = new User({
      email: `owner${timestamp}@example.com`,
      password: 'password123',
      profile: {
        firstName: 'Jane',
        lastName: 'Smith',
        userType: ['property_owner']
      },
      propertyOwner: {
        isPropertyOwner: true,
        verificationStatus: 'verified',
        businessRegistration: '********',
        taxNumber: 'NL********9B12',
        bankAccount: '******************',
        properties: []
      }
    });
    await testPropertyOwner.save();

    // Reset mocks
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('registerAsPropertyOwner', () => {
    it('should successfully register a user as property owner', async () => {
      // Requirements: 10.1, 10.2
      const registrationData = {
        businessRegistration: '********',
        taxNumber: 'NL9********B12',
        bankAccount: 'NL98WXYZ9********0',
        businessName: 'Test Property Management B.V.',
        businessAddress: 'Test Street 123, 1234AB Amsterdam',
        businessPhone: '+***********'
      };

      const result = await propertyOwnerService.registerAsPropertyOwner(testUser._id, registrationData);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Property owner registration completed');
      expect(result.verificationStatus).toBe('pending');

      // Verify user was updated
      const updatedUser = await User.findById(testUser._id);
      expect(updatedUser.propertyOwner.isPropertyOwner).toBe(true);
      expect(updatedUser.propertyOwner.businessRegistration).toBe('********');
      expect(updatedUser.profile.userType).toContain('property_owner');
    });

    it('should validate business registration format', async () => {
      // Requirements: 10.2
      const invalidRegistrationData = {
        businessRegistration: '123', // Invalid format
        taxNumber: 'NL9********B12',
        bankAccount: 'NL98WXYZ9********0'
      };

      await expect(
        propertyOwnerService.registerAsPropertyOwner(testUser._id, invalidRegistrationData)
      ).rejects.toThrow('Invalid KvK number format');
    });

    it('should validate Dutch VAT number format', async () => {
      // Requirements: 10.2
      const invalidRegistrationData = {
        businessRegistration: '********',
        taxNumber: 'INVALID_VAT', // Invalid format
        bankAccount: 'NL98WXYZ9********0'
      };

      await expect(
        propertyOwnerService.registerAsPropertyOwner(testUser._id, invalidRegistrationData)
      ).rejects.toThrow('Invalid Dutch VAT number format');
    });

    it('should validate Dutch IBAN format', async () => {
      // Requirements: 10.2
      const invalidRegistrationData = {
        businessRegistration: '********',
        taxNumber: 'NL9********B12',
        bankAccount: 'INVALID_IBAN' // Invalid format
      };

      await expect(
        propertyOwnerService.registerAsPropertyOwner(testUser._id, invalidRegistrationData)
      ).rejects.toThrow('Invalid Dutch IBAN format');
    });

    it('should prevent duplicate property owner registration', async () => {
      // Requirements: 10.1
      const registrationData = {
        businessRegistration: '********',
        taxNumber: 'NL9********B12',
        bankAccount: 'NL98WXYZ9********0'
      };

      await expect(
        propertyOwnerService.registerAsPropertyOwner(testPropertyOwner._id, registrationData)
      ).rejects.toThrow('User is already registered as a property owner');
    });
  });

  describe('verifyPropertyOwner', () => {
    it('should successfully verify property owner', async () => {
      // Requirements: 10.2, 10.3
      const verificationDocuments = [
        { documentId: 'doc1', type: 'business_registration' },
        { documentId: 'doc2', type: 'tax_certificate' }
      ];

      const result = await propertyOwnerService.verifyPropertyOwner(testPropertyOwner._id, verificationDocuments);

      expect(result.overallStatus).toBeDefined();
      expect(result.businessRegistration).toBeDefined();
      expect(result.taxNumber).toBeDefined();
      expect(result.bankAccount).toBeDefined();
      expect(result.verificationScore).toBeGreaterThanOrEqual(0);
    });

    it('should return cached verification results', async () => {
      // Requirements: 10.2
      const cachedResult = {
        overallStatus: 'verified',
        verificationScore: 85,
        businessRegistration: { valid: true }
      };

      cacheService.get.mockResolvedValue(cachedResult);

      const result = await propertyOwnerService.verifyPropertyOwner(testPropertyOwner._id);

      expect(result).toEqual(cachedResult);
      expect(cacheService.get).toHaveBeenCalledWith(`property_owner_verification:${testPropertyOwner._id}`);
    });

    it('should reject verification for non-property owner', async () => {
      // Requirements: 10.2
      await expect(
        propertyOwnerService.verifyPropertyOwner(testUser._id)
      ).rejects.toThrow('User is not registered as a property owner');
    });
  });

  describe('getPropertyOwnerDashboard', () => {
    it('should return comprehensive dashboard data', async () => {
      // Requirements: 10.4, 10.5
      const result = await propertyOwnerService.getPropertyOwnerDashboard(testPropertyOwner._id);

      expect(result.owner).toBeDefined();
      expect(result.owner.id.toString()).toBe(testPropertyOwner._id.toString());
      expect(result.owner.verificationStatus).toBe('verified');

      expect(result.properties).toBeDefined();
      expect(result.properties.total).toBeGreaterThanOrEqual(0);

      expect(result.applications).toBeDefined();
      expect(result.screening).toBeDefined();
      expect(result.performance).toBeDefined();
      expect(result.lastUpdated).toBeDefined();
    });

    it('should reject dashboard access for non-property owner', async () => {
      // Requirements: 10.4
      await expect(
        propertyOwnerService.getPropertyOwnerDashboard(testUser._id)
      ).rejects.toThrow('User is not a property owner');
    });
  });

  describe('screenTenants', () => {
    beforeEach(() => {
      // Mock tenant scoring service
      tenantScoringService.calculateTenantScore.mockResolvedValue({
        overallScore: 85,
        components: {
          incomeStability: 90,
          rentalHistory: 80,
          creditworthiness: 85,
          employment: 90,
          references: 75
        },
        verificationLevel: 'verified'
      });

      // Mock AI service
      aiService.openai = {
        chat: {
          completions: {
            create: jest.fn().mockResolvedValue({
              choices: [{
                message: {
                  content: 'Strong candidate with excellent tenant score. Recommend approval.'
                }
              }]
            })
          }
        }
      };
    });

    it('should successfully screen tenants for a property', async () => {
      // Requirements: 10.4, 10.5, 10.6
      const propertyId = 'prop123';
      const applicationIds = ['app1', 'app2'];

      const result = await propertyOwnerService.screenTenants(propertyId, applicationIds);

      expect(result.propertyId).toBe(propertyId);
      expect(result.screenedApplications).toBeDefined();
      expect(result.screeningReport).toBeDefined();
      expect(result.screenedAt).toBeDefined();
    });

    it('should handle empty application list', async () => {
      // Requirements: 10.4
      const propertyId = 'prop123';
      const applicationIds = [];

      // Mock the internal method to return empty applications
      const originalMethod = propertyOwnerService._getApplicationsForScreening;
      propertyOwnerService._getApplicationsForScreening = jest.fn().mockResolvedValue([]);

      const result = await propertyOwnerService.screenTenants(propertyId, applicationIds);

      expect(result.screenedApplications).toHaveLength(0);
      expect(result.message).toBe('No applications found for screening');

      // Restore original method
      propertyOwnerService._getApplicationsForScreening = originalMethod;
    });
  });

  describe('rankApplicants', () => {
    beforeEach(() => {
      // Mock tenant scoring service
      tenantScoringService.calculateTenantScore.mockResolvedValue({
        overallScore: 85,
        components: {
          incomeStability: 90,
          rentalHistory: 80,
          creditworthiness: 85,
          employment: 90,
          references: 75
        }
      });
    });

    it('should successfully rank applicants for a property', async () => {
      // Requirements: 10.5, 10.6
      const propertyId = 'prop123';
      const criteria = {
        priorityFactors: {
          tenantScore: 0.4,
          applicationCompleteness: 0.3,
          responseTime: 0.2,
          ownerPreference: 0.1
        },
        minimumScore: 60
      };

      // Mock the internal methods to return proper data
      const mockApplications = [
        {
          _id: 'app1',
          applicantId: testUser._id,
          propertyId,
          status: 'submitted'
        }
      ];

      const originalGetApplications = propertyOwnerService._getApplicationsForScreening;
      const originalValidateProperty = propertyOwnerService._validatePropertyOwnership;
      
      propertyOwnerService._getApplicationsForScreening = jest.fn().mockResolvedValue(mockApplications);
      propertyOwnerService._validatePropertyOwnership = jest.fn().mockResolvedValue({
        _id: propertyId,
        title: `Property ${propertyId}`,
        location: 'Amsterdam',
        price: '€2,500'
      });

      const result = await propertyOwnerService.rankApplicants(propertyId, criteria);

      expect(result.propertyId).toBe(propertyId);
      expect(result.rankedApplicants).toBeDefined();
      expect(result.rankingCriteria).toEqual(criteria);
      expect(result.rankedAt).toBeDefined();

      // Restore original methods
      propertyOwnerService._getApplicationsForScreening = originalGetApplications;
      propertyOwnerService._validatePropertyOwnership = originalValidateProperty;
    });

    it('should handle empty application list for ranking', async () => {
      // Requirements: 10.5
      const propertyId = 'prop123';

      // Mock the internal methods to return empty data
      const originalGetApplications = propertyOwnerService._getApplicationsForScreening;
      const originalValidateProperty = propertyOwnerService._validatePropertyOwnership;
      
      propertyOwnerService._getApplicationsForScreening = jest.fn().mockResolvedValue([]);
      propertyOwnerService._validatePropertyOwnership = jest.fn().mockResolvedValue({
        _id: propertyId,
        title: `Property ${propertyId}`,
        location: 'Amsterdam',
        price: '€2,500'
      });

      const result = await propertyOwnerService.rankApplicants(propertyId);

      expect(result.rankedApplicants).toHaveLength(0);
      expect(result.message).toBe('No applications found for ranking');

      // Restore original methods
      propertyOwnerService._getApplicationsForScreening = originalGetApplications;
      propertyOwnerService._validatePropertyOwnership = originalValidateProperty;
    });
  });

  describe('manageProperties', () => {
    it('should list properties for owner', async () => {
      // Requirements: 10.3, 10.4
      const result = await propertyOwnerService.manageProperties(testPropertyOwner._id, 'list');

      expect(Array.isArray(result)).toBe(true);
    });

    it('should add new property', async () => {
      // Requirements: 10.3
      const propertyData = {
        title: 'Test Property',
        address: 'Test Street 123',
        rent: 2500,
        propertyType: 'apartment'
      };

      const result = await propertyOwnerService.manageProperties(testPropertyOwner._id, 'add', propertyData);

      expect(result.success).toBe(true);
      expect(result.propertyId).toBeDefined();
      expect(result.message).toBe('Property added successfully');
    });

    it('should update existing property', async () => {
      // Requirements: 10.3
      const propertyData = {
        propertyId: 'prop123',
        title: 'Updated Property',
        rent: 2800
      };

      const result = await propertyOwnerService.manageProperties(testPropertyOwner._id, 'update', propertyData);

      expect(result.success).toBe(true);
      expect(result.propertyId).toBe('prop123');
      expect(result.message).toBe('Property updated successfully');
    });

    it('should remove property', async () => {
      // Requirements: 10.3
      const propertyData = { propertyId: 'prop123' };

      const result = await propertyOwnerService.manageProperties(testPropertyOwner._id, 'remove', propertyData);

      expect(result.success).toBe(true);
      expect(result.propertyId).toBe('prop123');
      expect(result.message).toBe('Property removed successfully');
    });

    it('should activate property', async () => {
      // Requirements: 10.3
      const propertyData = { propertyId: 'prop123' };

      const result = await propertyOwnerService.manageProperties(testPropertyOwner._id, 'activate', propertyData);

      expect(result.success).toBe(true);
      expect(result.status).toBe('active');
    });

    it('should deactivate property', async () => {
      // Requirements: 10.3
      const propertyData = { propertyId: 'prop123' };

      const result = await propertyOwnerService.manageProperties(testPropertyOwner._id, 'deactivate', propertyData);

      expect(result.success).toBe(true);
      expect(result.status).toBe('inactive');
    });

    it('should reject invalid action', async () => {
      // Requirements: 10.3
      await expect(
        propertyOwnerService.manageProperties(testPropertyOwner._id, 'invalid_action')
      ).rejects.toThrow('Invalid property management action: invalid_action');
    });
  });

  describe('generatePropertyReport', () => {
    it('should generate comprehensive report', async () => {
      // Requirements: 10.6
      const propertyId = 'prop123';
      const reportType = 'comprehensive';

      const result = await propertyOwnerService.generatePropertyReport(propertyId, reportType);

      expect(result.propertyId).toBe(propertyId);
      expect(result.reportType).toBe(reportType);
      expect(result.property).toBeDefined();
      expect(result.generatedAt).toBeDefined();
      expect(result.summary).toBeDefined();
      expect(result.marketAnalysis).toBeDefined();
      expect(result.recommendations).toBeDefined();
    });

    it('should generate screening report', async () => {
      // Requirements: 10.6
      const propertyId = 'prop123';
      const reportType = 'screening';

      const result = await propertyOwnerService.generatePropertyReport(propertyId, reportType);

      expect(result.propertyId).toBe(propertyId);
      expect(result.reportType).toBe(reportType);
      expect(result.totalApplications).toBeDefined();
      expect(result.averageScore).toBeDefined();
    });

    it('should generate performance report', async () => {
      // Requirements: 10.6
      const propertyId = 'prop123';
      const reportType = 'performance';

      const result = await propertyOwnerService.generatePropertyReport(propertyId, reportType);

      expect(result.propertyId).toBe(propertyId);
      expect(result.reportType).toBe(reportType);
      expect(result.occupancyRate).toBeDefined();
      expect(result.financialPerformance).toBeDefined();
    });

    it('should reject invalid report type', async () => {
      // Requirements: 10.6
      const propertyId = 'prop123';
      const invalidReportType = 'invalid_type';

      await expect(
        propertyOwnerService.generatePropertyReport(propertyId, invalidReportType)
      ).rejects.toThrow('Invalid report type: invalid_type');
    });
  });

  // Note: Business Registration Validation is tested through the public API methods above

  describe('AI Integration', () => {
    it('should integrate with AI service for tenant evaluation', async () => {
      // Requirements: 10.4, 10.5
      const mockAIResponse = {
        choices: [{
          message: {
            content: 'Based on the tenant score of 85/100, this applicant shows strong financial stability and rental history. Recommend approval with standard lease terms.'
          }
        }]
      };

      aiService.openai = {
        chat: {
          completions: {
            create: jest.fn().mockResolvedValue(mockAIResponse)
          }
        }
      };

      const application = { applicantId: testUser._id };
      const property = { title: 'Test Property', location: 'Amsterdam', price: '€2,500' };
      const screeningResult = { overallScore: 85 };

      const result = await propertyOwnerService._getAIRecommendation(application, property, screeningResult);

      expect(result).toContain('Recommend approval');
      expect(aiService.openai.chat.completions.create).toHaveBeenCalled();
    });

    it('should handle AI service errors gracefully', async () => {
      // Requirements: 10.4, 10.5
      aiService.openai = {
        chat: {
          completions: {
            create: jest.fn().mockRejectedValue(new Error('AI service unavailable'))
          }
        }
      };

      const application = { applicantId: testUser._id };
      const property = { title: 'Test Property' };
      const screeningResult = { overallScore: 85 };

      const result = await propertyOwnerService._getAIRecommendation(application, property, screeningResult);

      expect(result).toBe('Unable to generate AI recommendation at this time.');
    });
  });

  describe('Caching', () => {
    it('should cache verification results', async () => {
      // Requirements: 10.2
      const verificationData = {
        overallStatus: 'verified',
        verificationScore: 85
      };

      await propertyOwnerService._cacheVerification(testPropertyOwner._id, verificationData);

      expect(cacheService.set).toHaveBeenCalledWith(
        `property_owner_verification:${testPropertyOwner._id}`,
        verificationData,
        86400 // 24 hours
      );
    });

    it('should retrieve cached verification results', async () => {
      // Requirements: 10.2
      const cachedData = {
        overallStatus: 'verified',
        verificationScore: 85
      };

      cacheService.get.mockResolvedValue(cachedData);

      const result = await propertyOwnerService._getCachedVerification(testPropertyOwner._id);

      expect(result).toEqual(cachedData);
      expect(cacheService.get).toHaveBeenCalledWith(`property_owner_verification:${testPropertyOwner._id}`);
    });
  });

  describe('Error Handling', () => {
    it('should handle user not found error', async () => {
      // Requirements: 10.1
      const nonExistentUserId = new mongoose.Types.ObjectId();
      const registrationData = {
        businessRegistration: '********'
      };

      await expect(
        propertyOwnerService.registerAsPropertyOwner(nonExistentUserId, registrationData)
      ).rejects.toThrow('User not found');
    });

    it('should handle database connection errors', async () => {
      // Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6
      // Mock database error
      const originalFindById = User.findById;
      User.findById = jest.fn().mockRejectedValue(new Error('Database connection failed'));

      await expect(
        propertyOwnerService.getPropertyOwnerDashboard(testPropertyOwner._id)
      ).rejects.toThrow('Database connection failed');

      // Restore original method
      User.findById = originalFindById;
    });
  });

  describe('Performance', () => {
    it('should handle large numbers of applications efficiently', async () => {
      // Requirements: 10.5, 10.6
      const propertyId = 'prop123';
      const startTime = Date.now();

      // Mock large number of applications
      const mockApplications = Array.from({ length: 100 }, (_, i) => ({
        _id: `app${i}`,
        applicantId: `user${i}`,
        status: 'submitted'
      }));

      // Mock the internal method to return mock applications
      propertyOwnerService._getApplicationsForScreening = jest.fn().mockResolvedValue(mockApplications);

      const result = await propertyOwnerService.screenTenants(propertyId);

      const duration = Date.now() - startTime;
      
      // Should complete within reasonable time (5 seconds for 100 applications)
      expect(duration).toBeLessThan(5000);
      expect(result.screenedApplications).toBeDefined();
    });
  });
});

describe('PropertyOwnerService Integration Tests', () => {
  let testUser;
  let testProperty;

  beforeAll(async () => {
    const mongoUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/zakmakelaar_test';
    await mongoose.connect(mongoUri);
  });

  beforeEach(async () => {
    await User.deleteMany({});
    await Property.deleteMany({});
    await Application.deleteMany({});

    testUser = new User({
      email: '<EMAIL>',
      password: 'password123',
      profile: {
        firstName: 'Integration',
        lastName: 'Test',
        userType: ['property_owner']
      }
    });
    await testUser.save();
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  it('should complete full property owner workflow', async () => {
    // Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6
    
    // Step 1: Register as property owner
    const registrationData = {
      businessRegistration: '********',
      taxNumber: 'NL9********B12',
      bankAccount: 'NL98WXYZ9********0',
      businessName: 'Integration Test Properties B.V.'
    };

    const registrationResult = await propertyOwnerService.registerAsPropertyOwner(testUser._id, registrationData);
    expect(registrationResult.success).toBe(true);

    // Step 2: Verify property owner
    const verificationResult = await propertyOwnerService.verifyPropertyOwner(testUser._id);
    expect(verificationResult.overallStatus).toBeDefined();

    // Step 3: Add property
    const propertyData = {
      title: 'Integration Test Property',
      address: 'Test Street 123',
      rent: 2500,
      propertyType: 'apartment'
    };

    const addPropertyResult = await propertyOwnerService.manageProperties(testUser._id, 'add', propertyData);
    expect(addPropertyResult.success).toBe(true);

    // Step 4: Get dashboard
    const dashboardResult = await propertyOwnerService.getPropertyOwnerDashboard(testUser._id);
    expect(dashboardResult.owner.id.toString()).toBe(testUser._id.toString());
    expect(dashboardResult.properties.total).toBeGreaterThanOrEqual(0);

    // Step 5: Generate report
    const reportResult = await propertyOwnerService.generatePropertyReport(addPropertyResult.propertyId, 'comprehensive');
    expect(reportResult.propertyId).toBe(addPropertyResult.propertyId);
    expect(reportResult.reportType).toBe('comprehensive');
  });
});