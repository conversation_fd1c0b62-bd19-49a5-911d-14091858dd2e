/**
 * Transformation Functions Tests
 * 
 * This file contains unit tests for all transformation functions used in the schema transformer.
 */

const { SchemaTransformer } = require('../services/schemaTransformer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { testDataSets, rawScraperData } = require('./testData/unifiedSchemaTestData');

describe('Transformation Functions', () => {
  let registry;
  let transformer;
  
  beforeEach(() => {
    registry = new FieldMappingRegistry();
    transformer = new SchemaTransformer(registry);
  });
  
  describe('Price Transformation Functions', () => {
    test('normalizePrice should convert string price to number', () => {
      // Register the transformation function
      registry.registerTransformation('normalizePrice', transformer.transformations.normalizePrice);
      
      // Test various price formats
      expect(registry.executeTransformation('normalizePrice', '€ 1.500 per maand')).toBe(1500);
      expect(registry.executeTransformation('normalizePrice', '€1500')).toBe(1500);
      expect(registry.executeTransformation('normalizePrice', '1500')).toBe(1500);
      expect(registry.executeTransformation('normalizePrice', '1.500')).toBe(1500);
      expect(registry.executeTransformation('normalizePrice', '1,500')).toBe(1500);
      expect(registry.executeTransformation('normalizePrice', '€ 1.500,-')).toBe(1500);
      expect(registry.executeTransformation('normalizePrice', '€ 1.500,00')).toBe(1500);
    });
    
    test('normalizePrice should handle special price strings', () => {
      registry.registerTransformation('normalizePrice', transformer.transformations.normalizePrice);
      
      // Test special cases
      expect(registry.executeTransformation('normalizePrice', 'Prijs op aanvraag')).toBe('Prijs op aanvraag');
      expect(registry.executeTransformation('normalizePrice', 'n.o.t.k.')).toBe('n.o.t.k.');
      expect(registry.executeTransformation('normalizePrice', 'Op aanvraag')).toBe('Op aanvraag');
    });
    
    test('normalizePrice should handle numeric input', () => {
      registry.registerTransformation('normalizePrice', transformer.transformations.normalizePrice);
      
      // Test numeric input
      expect(registry.executeTransformation('normalizePrice', 1500)).toBe(1500);
      expect(registry.executeTransformation('normalizePrice', 1500.50)).toBe(1500.5);
    });
    
    test('normalizePrice should handle invalid input', () => {
      registry.registerTransformation('normalizePrice', transformer.transformations.normalizePrice);
      
      // Test invalid input
      expect(registry.executeTransformation('normalizePrice', null)).toBeNull();
      expect(registry.executeTransformation('normalizePrice', undefined)).toBeUndefined();
      expect(registry.executeTransformation('normalizePrice', '')).toBe('');
      expect(registry.executeTransformation('normalizePrice', 'invalid')).toBe('invalid');
    });
    
    test('formatPriceString should format price as string', () => {
      registry.registerTransformation('formatPriceString', transformer.transformations.formatPriceString);
      
      // Test various inputs
      expect(registry.executeTransformation('formatPriceString', 1500)).toBe('€ 1.500');
      expect(registry.executeTransformation('formatPriceString', '1500')).toBe('€ 1.500');
      expect(registry.executeTransformation('formatPriceString', '€ 1.500 per maand')).toBe('€ 1.500');
    });
  });
  
  describe('Size Transformation Functions', () => {
    test('normalizeSize should handle various size formats', () => {
      registry.registerTransformation('normalizeSize', transformer.transformations.normalizeSize);
      
      // Test various size formats
      expect(registry.executeTransformation('normalizeSize', '85 m²')).toBe('85 m²');
      expect(registry.executeTransformation('normalizeSize', '85m²')).toBe('85 m²');
      expect(registry.executeTransformation('normalizeSize', '85')).toBe('85 m²');
      expect(registry.executeTransformation('normalizeSize', '85 m2')).toBe('85 m²');
      expect(registry.executeTransformation('normalizeSize', '85 vierkante meter')).toBe('85 m²');
    });
    
    test('extractNumericSize should extract numeric value from size string', () => {
      registry.registerTransformation('extractNumericSize', transformer.transformations.extractNumericSize);
      
      // Test various size formats
      expect(registry.executeTransformation('extractNumericSize', '85 m²')).toBe(85);
      expect(registry.executeTransformation('extractNumericSize', '85m²')).toBe(85);
      expect(registry.executeTransformation('extractNumericSize', '85')).toBe(85);
      expect(registry.executeTransformation('extractNumericSize', '85 m2')).toBe(85);
      expect(registry.executeTransformation('extractNumericSize', '85 vierkante meter')).toBe(85);
      expect(registry.executeTransformation('extractNumericSize', '85+')).toBe(85);
      expect(registry.executeTransformation('extractNumericSize', 'ca. 85m²')).toBe(85);
    });
    
    test('extractNumericSize should handle invalid input', () => {
      registry.registerTransformation('extractNumericSize', transformer.transformations.extractNumericSize);
      
      // Test invalid input
      expect(registry.executeTransformation('extractNumericSize', null)).toBeNull();
      expect(registry.executeTransformation('extractNumericSize', undefined)).toBeUndefined();
      expect(registry.executeTransformation('extractNumericSize', '')).toBeNull();
      expect(registry.executeTransformation('extractNumericSize', 'invalid')).toBeNull();
    });
  });
  
  describe('Room Transformation Functions', () => {
    test('normalizeRooms should handle various room formats', () => {
      registry.registerTransformation('normalizeRooms', transformer.transformations.normalizeRooms);
      
      // Test various room formats
      expect(registry.executeTransformation('normalizeRooms', '3')).toBe('3');
      expect(registry.executeTransformation('normalizeRooms', 3)).toBe(3);
      expect(registry.executeTransformation('normalizeRooms', '3+')).toBe('3+');
      expect(registry.executeTransformation('normalizeRooms', '3 kamers')).toBe('3');
      expect(registry.executeTransformation('normalizeRooms', '3 of meer')).toBe('3+');
    });
    
    test('normalizeRooms should handle invalid input', () => {
      registry.registerTransformation('normalizeRooms', transformer.transformations.normalizeRooms);
      
      // Test invalid input
      expect(registry.executeTransformation('normalizeRooms', null)).toBeNull();
      expect(registry.executeTransformation('normalizeRooms', undefined)).toBeUndefined();
      expect(registry.executeTransformation('normalizeRooms', '')).toBe('');
      expect(registry.executeTransformation('normalizeRooms', 'invalid')).toBe('invalid');
    });
  });
  
  describe('Location Transformation Functions', () => {
    test('normalizeLocation should handle string locations', () => {
      registry.registerTransformation('normalizeLocation', transformer.transformations.normalizeLocation);
      
      // Test string locations
      const result = registry.executeTransformation('normalizeLocation', 'Amsterdam, Noord-Holland');
      expect(result).toHaveProperty('_legacy', 'Amsterdam, Noord-Holland');
      expect(result).toHaveProperty('_unified.address.city', 'Amsterdam');
      expect(result).toHaveProperty('_unified.address.province', 'Noord-Holland');
    });
    
    test('normalizeLocation should handle object locations', () => {
      registry.registerTransformation('normalizeLocation', transformer.transformations.normalizeLocation);
      
      // Test object locations
      const locationObj = {
        street: 'Prinsengracht',
        houseNumber: '123',
        postalCode: '1015 AB',
        city: 'Amsterdam',
        province: 'Noord-Holland'
      };
      
      const result = registry.executeTransformation('normalizeLocation', locationObj);
      expect(result).toHaveProperty('_unified.address.street', 'Prinsengracht');
      expect(result).toHaveProperty('_unified.address.city', 'Amsterdam');
      expect(result).toHaveProperty('_legacy');
    });
    
    test('normalizeLocation should handle invalid input', () => {
      registry.registerTransformation('normalizeLocation', transformer.transformations.normalizeLocation);
      
      // Test invalid input
      expect(registry.executeTransformation('normalizeLocation', null)).toBeNull();
      expect(registry.executeTransformation('normalizeLocation', undefined)).toBeUndefined();
      expect(registry.executeTransformation('normalizeLocation', '')).toBe('');
    });
    
    test('extractCity should extract city from location string', () => {
      registry.registerTransformation('extractCity', transformer.transformations.extractCity);
      
      // Test various location formats
      expect(registry.executeTransformation('extractCity', 'Amsterdam, Noord-Holland')).toBe('Amsterdam');
      expect(registry.executeTransformation('extractCity', 'Prinsengracht 123, Amsterdam')).toBe('Amsterdam');
      expect(registry.executeTransformation('extractCity', 'Amsterdam')).toBe('Amsterdam');
    });
  });
  
  describe('Property Type Transformation Functions', () => {
    test('normalizePropertyType should standardize property types', () => {
      registry.registerTransformation('normalizePropertyType', transformer.transformations.normalizePropertyType);
      
      // Test various property types
      expect(registry.executeTransformation('normalizePropertyType', 'appartement')).toBe('apartment');
      expect(registry.executeTransformation('normalizePropertyType', 'apartment')).toBe('apartment');
      expect(registry.executeTransformation('normalizePropertyType', 'huis')).toBe('house');
      expect(registry.executeTransformation('normalizePropertyType', 'house')).toBe('house');
      expect(registry.executeTransformation('normalizePropertyType', 'studio')).toBe('studio');
      expect(registry.executeTransformation('normalizePropertyType', 'kamer')).toBe('room');
      expect(registry.executeTransformation('normalizePropertyType', 'room')).toBe('room');
      expect(registry.executeTransformation('normalizePropertyType', 'woning')).toBe('woning');
      expect(registry.executeTransformation('normalizePropertyType', 'unknown')).toBe('woning');
    });
  });
  
  describe('Interior Transformation Functions', () => {
    test('normalizeInterior should standardize interior types', () => {
      registry.registerTransformation('normalizeInterior', transformer.transformations.normalizeInterior);
      
      // Test various interior types
      expect(registry.executeTransformation('normalizeInterior', 'Gemeubileerd')).toBe('Gemeubileerd');
      expect(registry.executeTransformation('normalizeInterior', 'gemeubileerd')).toBe('Gemeubileerd');
      expect(registry.executeTransformation('normalizeInterior', 'furnished')).toBe('Gemeubileerd');
      expect(registry.executeTransformation('normalizeInterior', 'Gestoffeerd')).toBe('Gestoffeerd');
      expect(registry.executeTransformation('normalizeInterior', 'gestoffeerd')).toBe('Gestoffeerd');
      expect(registry.executeTransformation('normalizeInterior', 'semi-furnished')).toBe('Gestoffeerd');
      expect(registry.executeTransformation('normalizeInterior', 'Kaal')).toBe('Kaal');
      expect(registry.executeTransformation('normalizeInterior', 'kaal')).toBe('Kaal');
      expect(registry.executeTransformation('normalizeInterior', 'unfurnished')).toBe('Kaal');
    });
    
    test('inferFurnishedStatus should infer boolean from interior type', () => {
      registry.registerTransformation('inferFurnishedStatus', transformer.transformations.inferFurnishedStatus);
      
      // Test various interior types
      expect(registry.executeTransformation('inferFurnishedStatus', 'Gemeubileerd')).toBe(true);
      expect(registry.executeTransformation('inferFurnishedStatus', 'gemeubileerd')).toBe(true);
      expect(registry.executeTransformation('inferFurnishedStatus', 'furnished')).toBe(true);
      expect(registry.executeTransformation('inferFurnishedStatus', 'Gestoffeerd')).toBe(false);
      expect(registry.executeTransformation('inferFurnishedStatus', 'Kaal')).toBe(false);
      expect(registry.executeTransformation('inferFurnishedStatus', null)).toBe(false);
    });
  });
  
  describe('Boolean Transformation Functions', () => {
    test('normalizeBoolean should convert various inputs to boolean', () => {
      registry.registerTransformation('normalizeBoolean', transformer.transformations.normalizeBoolean);
      
      // Test various boolean formats
      expect(registry.executeTransformation('normalizeBoolean', true)).toBe(true);
      expect(registry.executeTransformation('normalizeBoolean', false)).toBe(false);
      expect(registry.executeTransformation('normalizeBoolean', 'true')).toBe(true);
      expect(registry.executeTransformation('normalizeBoolean', 'false')).toBe(false);
      expect(registry.executeTransformation('normalizeBoolean', 'yes')).toBe(true);
      expect(registry.executeTransformation('normalizeBoolean', 'no')).toBe(false);
      expect(registry.executeTransformation('normalizeBoolean', 'ja')).toBe(true);
      expect(registry.executeTransformation('normalizeBoolean', 'nee')).toBe(false);
      expect(registry.executeTransformation('normalizeBoolean', '1')).toBe(true);
      expect(registry.executeTransformation('normalizeBoolean', '0')).toBe(false);
      expect(registry.executeTransformation('normalizeBoolean', 1)).toBe(true);
      expect(registry.executeTransformation('normalizeBoolean', 0)).toBe(false);
    });
  });
  
  describe('Image Transformation Functions', () => {
    test('normalizeImageArray should ensure array of strings', () => {
      registry.registerTransformation('normalizeImageArray', transformer.transformations.normalizeImageArray);
      
      // Test various image inputs
      expect(registry.executeTransformation('normalizeImageArray', ['image1.jpg', 'image2.jpg'])).toEqual(['image1.jpg', 'image2.jpg']);
      expect(registry.executeTransformation('normalizeImageArray', 'image1.jpg')).toEqual(['image1.jpg']);
      expect(registry.executeTransformation('normalizeImageArray', null)).toEqual([]);
      expect(registry.executeTransformation('normalizeImageArray', undefined)).toEqual([]);
      expect(registry.executeTransformation('normalizeImageArray', [])).toEqual([]);
      
      // Test with object containing URLs
      expect(registry.executeTransformation('normalizeImageArray', [
        { url: 'image1.jpg' },
        { url: 'image2.jpg' }
      ])).toEqual(['image1.jpg', 'image2.jpg']);
    });
  });
  
  describe('Date Transformation Functions', () => {
    test('normalizeDate should convert various date formats to ISO string', () => {
      registry.registerTransformation('normalizeDate', transformer.transformations.normalizeDate);
      
      // Test various date formats
      const isoDate = new Date().toISOString();
      expect(registry.executeTransformation('normalizeDate', new Date())).toMatch(/^\d{4}-\d{2}-\d{2}T/);
      expect(registry.executeTransformation('normalizeDate', isoDate)).toBe(isoDate);
      expect(registry.executeTransformation('normalizeDate', '2023-01-01')).toMatch(/^2023-01-01T/);
      expect(registry.executeTransformation('normalizeDate', '01-01-2023')).toMatch(/^2023-01-01T/);
      expect(registry.executeTransformation('normalizeDate', '01/01/2023')).toMatch(/^2023-01-01T/);
    });
    
    test('getCurrentISOString should return current date as ISO string', () => {
      registry.registerTransformation('getCurrentISOString', transformer.transformations.getCurrentISOString);
      
      // Test current date
      const result = registry.executeTransformation('getCurrentISOString');
      expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T/);
    });
  });
  
  describe('Year Transformation Functions', () => {
    test('normalizeYear should handle various year formats', () => {
      registry.registerTransformation('normalizeYear', transformer.transformations.normalizeYear);
      
      // Test various year formats
      expect(registry.executeTransformation('normalizeYear', '2010')).toBe('2010');
      expect(registry.executeTransformation('normalizeYear', 2010)).toBe('2010');
      expect(registry.executeTransformation('normalizeYear', 'Bouwjaar 2010')).toBe('2010');
      expect(registry.executeTransformation('normalizeYear', 'Built in 2010')).toBe('2010');
    });
    
    test('normalizeYear should handle invalid years', () => {
      registry.registerTransformation('normalizeYear', transformer.transformations.normalizeYear);
      
      // Test invalid years
      const currentYear = new Date().getFullYear();
      expect(registry.executeTransformation('normalizeYear', '1700')).toBe('1800'); // Too old, should be capped
      expect(registry.executeTransformation('normalizeYear', (currentYear + 10).toString())).toBe(currentYear.toString()); // Too new, should be capped
      expect(registry.executeTransformation('normalizeYear', null)).toBeNull();
      expect(registry.executeTransformation('normalizeYear', undefined)).toBeUndefined();
      expect(registry.executeTransformation('normalizeYear', '')).toBe('');
    });
  });
  
  describe('Energy Label Transformation Functions', () => {
    test('normalizeEnergyLabel should standardize energy labels', () => {
      registry.registerTransformation('normalizeEnergyLabel', transformer.transformations.normalizeEnergyLabel);
      
      // Test various energy labels
      expect(registry.executeTransformation('normalizeEnergyLabel', 'A')).toBe('A');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'a')).toBe('A');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'A+')).toBe('A+');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'A++')).toBe('A++');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'A+++')).toBe('A+++');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'B')).toBe('B');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'C')).toBe('C');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'D')).toBe('D');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'E')).toBe('E');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'F')).toBe('F');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'G')).toBe('G');
      expect(registry.executeTransformation('normalizeEnergyLabel', 'H')).toBeNull(); // Invalid label
      expect(registry.executeTransformation('normalizeEnergyLabel', 'A++++')).toBeNull(); // Invalid label
    });
  });
  
  describe('Complex Transformation Functions', () => {
    test('transformRawScraperData should transform raw scraper data to unified schema', () => {
      // This is a more complex test that verifies the entire transformation pipeline
      // Register all necessary transformations
      Object.entries(transformer.transformations).forEach(([name, fn]) => {
        registry.registerTransformation(name, fn);
      });
      
      // Register mappings for Funda
      registry.registerMapping('funda.nl', {
        'title': 'title',
        'description': 'description',
        'url': 'url',
        'source': { value: 'funda.nl' },
        'location': {
          path: 'location',
          transform: 'normalizeLocation'
        },
        'price': {
          path: 'price',
          transform: 'normalizePrice'
        },
        'propertyType': {
          path: 'propertyType',
          transform: 'normalizePropertyType'
        },
        'size': {
          path: 'size',
          transform: 'normalizeSize'
        },
        'area': {
          path: 'size',
          transform: 'extractNumericSize'
        },
        'rooms': {
          path: 'rooms',
          transform: 'normalizeRooms'
        },
        'bedrooms': {
          path: 'bedrooms',
          transform: 'normalizeRooms'
        },
        'year': {
          path: 'year',
          transform: 'normalizeYear'
        },
        'interior': {
          path: 'interior',
          transform: 'normalizeInterior'
        },
        'furnished': {
          path: 'interior',
          transform: 'inferFurnishedStatus'
        },
        'images': {
          path: 'images',
          transform: 'normalizeImageArray'
        },
        'energyLabel': {
          path: 'energyLabel',
          transform: 'normalizeEnergyLabel'
        },
        'dateAdded': {
          transform: 'getCurrentISOString'
        }
      });
      
      // Transform raw Funda data
      const result = transformer.transform(rawScraperData.funda, 'funda.nl');
      
      // Verify the result
      expect(result).toHaveProperty('title', rawScraperData.funda.title);
      expect(result).toHaveProperty('description', rawScraperData.funda.description);
      expect(result).toHaveProperty('url', rawScraperData.funda.url);
      expect(result).toHaveProperty('source', 'funda.nl');
      expect(result).toHaveProperty('location._legacy', rawScraperData.funda.location);
      expect(result).toHaveProperty('price', 1500);
      expect(result).toHaveProperty('propertyType', 'apartment');
      expect(result).toHaveProperty('size', '85 m²');
      expect(result).toHaveProperty('area', 85);
      expect(result).toHaveProperty('rooms', '3');
      expect(result).toHaveProperty('bedrooms', '2');
      expect(result).toHaveProperty('year', '2010');
      expect(result).toHaveProperty('interior', 'Gemeubileerd');
      expect(result).toHaveProperty('furnished', true);
      expect(result).toHaveProperty('images').toBeInstanceOf(Array);
      expect(result).toHaveProperty('energyLabel', 'A');
      expect(result).toHaveProperty('dateAdded');
      expect(result).toHaveProperty('_internal.rawData.original', rawScraperData.funda);
    });
  });
});