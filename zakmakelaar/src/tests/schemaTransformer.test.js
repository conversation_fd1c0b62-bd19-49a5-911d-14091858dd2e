/**
 * Schema Transformer Tests
 * 
 * This file contains tests for the SchemaTransformer class, which is responsible
 * for transforming raw scraper data into the unified property schema format.
 */

const { SchemaTransformer } = require('../services/schemaTransformer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { ErrorTypes, SchemaError } = require('../utils/schemaErrors');
const { validateProperty } = require('../schemas/unifiedPropertySchema');

describe('SchemaTransformer', () => {
  let registry;
  let transformer;
  
  beforeEach(() => {
    // Create a fresh registry for each test
    registry = new FieldMappingRegistry();
    
    // Register test mappings
    registry.registerMapping('test', {
      // Direct field mapping
      'title': 'title',
      'description': 'description',
      'url': 'url',
      
      // Static value
      'source': { value: 'test-source.nl' },
      
      // Transformation
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      
      // Default value
      'propertyType': {
        path: 'type',
        default: 'apartment'
      },
      
      // Required field
      'location': {
        path: 'location',
        required: true
      }
    });
    
    // Create transformer with registry
    transformer = new SchemaTransformer(registry);
  });
  
  test('should create a transformer with registry', () => {
    expect(transformer).toBeInstanceOf(SchemaTransformer);
    expect(transformer.mappingRegistry).toBe(registry);
  });
  
  test('should throw error when creating transformer without registry', () => {
    expect(() => new SchemaTransformer()).toThrow('MappingRegistry is required');
  });
  
  test('should transform raw data to unified schema', async () => {
    const rawData = {
      title: 'Test Property',
      description: 'A test property',
      url: 'https://test.com/property',
      price: '€1000',
      type: 'house',
      location: 'Amsterdam'
    };
    
    const result = await transformer.transform(rawData, 'test');
    
    expect(result).toHaveProperty('title', 'Test Property');
    expect(result).toHaveProperty('description', 'A test property');
    expect(result).toHaveProperty('url', 'https://test.com/property');
    expect(result).toHaveProperty('source', 'test-source.nl');
    expect(result).toHaveProperty('propertyType', 'house');
    expect(result).toHaveProperty('location', 'Amsterdam');
    
    // Price should be normalized
    expect(result.price).toBe(1000);
    
    // Should have internal metadata
    expect(result).toHaveProperty('_internal.sourceMetadata.website', 'test');
    expect(result).toHaveProperty('_internal.rawData.original', rawData);
  });
  
  test('should handle missing fields with defaults', async () => {
    const rawData = {
      title: 'Test Property',
      url: 'https://test.com/property',
      location: 'Amsterdam'
    };
    
    const result = await transformer.transform(rawData, 'test');
    
    expect(result).toHaveProperty('title', 'Test Property');
    // Description is not in the result object if it's undefined
    expect(result.description).toBeUndefined();
    expect(result).toHaveProperty('propertyType', 'apartment'); // Default value
  });
  
  test('should handle required fields', async () => {
    const rawData = {
      title: 'Test Property',
      url: 'https://test.com/property'
      // Missing required location field
    };
    
    const result = await transformer.transform(rawData, 'test');
    
    // Should have error in processing metadata
    expect(result).toHaveProperty('_internal.processingMetadata.errors');
    expect(result._internal.processingMetadata.errors[0]).toHaveProperty(
      'field', 'location'
    );
  });
  
  test('should handle transformation errors', async () => {
    // Register a transformation that throws an error
    registry.registerTransformation('errorTransform', () => {
      throw new Error('Test transformation error');
    });
    
    registry.registerMapping('error-test', {
      'title': 'title',
      'price': {
        path: 'price',
        transform: 'errorTransform'
      },
      'location': { value: 'Test Location' },
      'url': { value: 'https://test.com' },
      'source': { value: 'test-source.nl' }
    });
    
    const rawData = {
      title: 'Test Property',
      price: '€1000'
    };
    
    const result = await transformer.transform(rawData, 'error-test');
    
    // Should have error in processing metadata
    expect(result).toHaveProperty('_internal.processingMetadata.errors');
    expect(result._internal.processingMetadata.errors[0]).toHaveProperty(
      'field', 'price'
    );
  });
  
  test('should throw error when throwOnError is true', async () => {
    const rawData = {
      title: 'Test Property',
      url: 'https://test.com/property'
      // Missing required location field
    };
    
    await expect(
      transformer.transform(rawData, 'test', { throwOnError: true })
    ).rejects.toBeInstanceOf(SchemaError);
  });
  
  test('should handle invalid source', async () => {
    const rawData = {
      title: 'Test Property'
    };
    
    const result = await transformer.transform(rawData, 'non-existent-source');
    
    // Should have error in processing metadata
    expect(result).toHaveProperty('_internal.processingMetadata.errors');
    expect(result._internal.processingMetadata.errors[0]).toHaveProperty(
      'type', ErrorTypes.MAPPING_ERROR
    );
  });
  
  test('should handle invalid raw data', async () => {
    const result = await transformer.transform(null, 'test');
    
    // Should have error in processing metadata
    expect(result).toHaveProperty('_internal.processingMetadata.errors');
    expect(result._internal.processingMetadata.errors[0]).toHaveProperty(
      'type', ErrorTypes.TRANSFORMATION_ERROR
    );
  });
  
  test('should calculate data quality metrics', () => {
    const transformedData = {
      title: 'Test Property',
      description: 'A test property',
      location: 'Amsterdam',
      price: 1000,
      propertyType: 'apartment',
      size: '50 m²',
      rooms: 2,
      images: ['image1.jpg', 'image2.jpg']
    };
    
    const quality = transformer.calculateDataQuality(transformedData);
    
    expect(quality).toHaveProperty('completeness', 100);
    expect(quality).toHaveProperty('accuracy', 100);
  });
  
  test('should update data quality in transformed data', () => {
    const transformedData = {
      title: 'Test Property',
      description: 'A test property',
      location: 'Amsterdam',
      price: 1000,
      propertyType: 'apartment',
      // Missing size, rooms, images
    };
    
    const updated = transformer.updateDataQuality(transformedData);
    
    expect(updated).toHaveProperty('_internal.dataQuality.completeness');
    expect(updated).toHaveProperty('_internal.dataQuality.accuracy');
    expect(updated).toHaveProperty('_internal.dataQuality.lastValidated');
    
    // Should have lower completeness due to missing fields
    expect(updated._internal.dataQuality.completeness).toBeLessThan(100);
  });
  
  test('should batch transform multiple items', async () => {
    const rawDataItems = [
      {
        title: 'Property 1',
        description: 'First property',
        url: 'https://test.com/property1',
        price: '€1000',
        location: 'Amsterdam'
      },
      {
        title: 'Property 2',
        description: 'Second property',
        url: 'https://test.com/property2',
        price: '€1500',
        location: 'Rotterdam'
      }
    ];
    
    const result = await transformer.batchTransform(rawDataItems, 'test');
    
    expect(result).toHaveProperty('success', true);
    expect(result).toHaveProperty('totalProcessed', 2);
    expect(result).toHaveProperty('successCount', 2);
    expect(result).toHaveProperty('errorCount', 0);
    expect(result.results).toHaveLength(2);
    
    // Check transformed items
    expect(result.results[0]).toHaveProperty('title', 'Property 1');
    expect(result.results[1]).toHaveProperty('title', 'Property 2');
  });
  
  test('should handle errors in batch transform', async () => {
    const rawDataItems = [
      {
        title: 'Property 1',
        description: 'First property',
        url: 'https://test.com/property1',
        price: '€1000',
        location: 'Amsterdam'
      },
      {
        title: 'Property 2',
        description: 'Second property',
        url: 'https://test.com/property2',
        price: '€1500'
        // Missing required location
      }
    ];
    
    const result = await transformer.batchTransform(rawDataItems, 'test');
    
    // Check that we have the right number of items
    expect(result).toHaveProperty('totalProcessed', 2);
    expect(result.results).toHaveLength(2);
    
    // Second item should have error in processing metadata
    expect(result.results[1]._internal.processingMetadata.errors.length).toBeGreaterThan(0);
    expect(result.results[1]._internal.processingMetadata.errors[0].field).toBe('location');
  });
  
  test('should validate transformed data against schema', async () => {
    // Update the test mapping to use a valid source value
    registry.registerMapping('test-schema', {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'funda.nl' }, // Valid source value
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'propertyType': {
        path: 'type',
        default: 'apartment'
      },
      'location': {
        path: 'location',
        required: true
      }
    });
    
    const rawData = {
      title: 'Test Property',
      description: 'A test property',
      url: 'https://test.com/property',
      price: '€1000',
      location: 'Amsterdam'
    };
    
    const result = await transformer.transform(rawData, 'test-schema');
    
    // Validate against schema
    const validation = validateProperty(result);
    expect(validation.error).toBeUndefined();
  });
  
  test('should preserve raw data when requested', async () => {
    const rawData = {
      title: 'Test Property',
      extraField: 'This should be preserved'
    };
    
    // With preservation (default)
    const resultWithPreservation = await transformer.transform(rawData, 'test');
    expect(resultWithPreservation).toHaveProperty('_internal.rawData.original.extraField', 'This should be preserved');
    
    // Without preservation
    const resultWithoutPreservation = await transformer.transform(rawData, 'test', { preserveRawData: false });
    expect(resultWithoutPreservation).not.toHaveProperty('_internal.rawData.original');
  });
});