/**
 * Enhanced Validation Tests for Unified Property Schema
 * Tests for the additional validation rules and data quality checks
 */

const {
    validateProperty,
    validatePropertyStrict,
    createMinimalProperty
} = require('../schemas/unifiedPropertySchema');

describe('Unified Property Schema - Enhanced Validation', () => {
    describe('Enhanced size validation', () => {
        test('should accept size with plus sign', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                size: '85+ m²'
            };

            const result = validateProperty(property);
            expect(result.error).toBeUndefined();
            expect(result.value.size).toBe('85+ m²');
        });

        test('should accept size without m² symbol', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                size: '85'
            };

            const result = validateProperty(property);
            expect(result.error).toBeUndefined();
            expect(result.value.size).toBe('85');
        });
    });

    describe('Enhanced area validation', () => {
        test('should accept reasonable area values', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                area: 85
            };

            const result = validateProperty(property);
            expect(result.error).toBeUndefined();
            expect(result.value.area).toBe(85);
        });

        test('should reject area that is too small', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                area: 5 // Too small
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details[0].path).toContain('area');
        });

        test('should reject area that is too large', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                area: 1500 // Too large for typical rental
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details[0].path).toContain('area');
        });
    });

    describe('Enhanced room validation', () => {
        test('should accept rooms with plus sign', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                rooms: '3+',
                bedrooms: '2+'
            };

            const result = validateProperty(property);
            expect(result.error).toBeUndefined();
            expect(result.value.rooms).toBe('3+');
            expect(result.value.bedrooms).toBe('2+');
        });

        test('should reject unreasonable room counts', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                rooms: 25, // Too many rooms
                bedrooms: 20 // Too many bedrooms
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details.length).toBeGreaterThan(0);
        });

        test('should reject invalid room format', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                rooms: 'three' // Invalid format
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details[0].path).toContain('rooms');
        });
    });

    describe('Enhanced year validation', () => {
        test('should accept reasonable build years', () => {
            const currentYear = new Date().getFullYear();
            const validYears = ['1900', '1950', '2000', '2020', currentYear.toString()];

            validYears.forEach(year => {
                const property = {
                    title: 'Test Property',
                    source: 'funda.nl',
                    url: 'https://www.funda.nl/test',
                    location: 'Amsterdam',
                    price: 1500,
                    year: year
                };

                const result = validateProperty(property);
                expect(result.error).toBeUndefined();
                expect(result.value.year).toBe(year);
            });
        });

        test('should reject unreasonable build years', () => {
            const currentYear = new Date().getFullYear();
            const invalidYears = ['1700', (currentYear + 10).toString()]; // Too old or too far in future

            invalidYears.forEach(year => {
                const property = {
                    title: 'Test Property',
                    source: 'funda.nl',
                    url: 'https://www.funda.nl/test',
                    location: 'Amsterdam',
                    price: 1500,
                    year: year
                };

                const result = validateProperty(property);
                expect(result.error).toBeDefined();
                expect(result.error.details[0].path).toContain('year');
            });
        });

        test('should reject invalid year format', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                year: '20' // Too short
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details[0].path).toContain('year');
        });
    });

    describe('Enhanced price validation', () => {
        test('should accept reasonable prices', () => {
            const validPrices = [500, 1500, 5000, '€ 1500 per maand', 'Prijs op aanvraag'];

            validPrices.forEach(price => {
                const property = {
                    title: 'Test Property',
                    source: 'funda.nl',
                    url: 'https://www.funda.nl/test',
                    location: 'Amsterdam',
                    price: price
                };

                const result = validateProperty(property);
                expect(result.error).toBeUndefined();
                expect(result.value.price).toBe(price);
            });
        });

        test('should reject unreasonable numeric prices', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 100000 // Too high for monthly rent
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details[0].path).toContain('price');
        });

        test('should reject extremely long price strings', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 'A'.repeat(150) // Too long
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details[0].path).toContain('price');
        });
    });

    describe('Enhanced features validation', () => {
        test('should accept reasonable features array', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                features: ['balcony', 'parking', 'garden', 'elevator']
            };

            const result = validateProperty(property);
            expect(result.error).toBeUndefined();
            expect(result.value.features).toEqual(['balcony', 'parking', 'garden', 'elevator']);
        });

        test('should reject too many features', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                features: Array.from({ length: 25 }, (_, i) => `feature${i}`) // Too many features
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details[0].path).toContain('features');
        });

        test('should reject features with too long strings', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                features: ['A'.repeat(60)] // Feature string too long
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details[0].path).toContain('features');
        });
    });

    describe('Enhanced deposit and utilities validation', () => {
        test('should accept reasonable deposit and utilities', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                deposit: 3000,
                utilities: 150
            };

            const result = validateProperty(property);
            expect(result.error).toBeUndefined();
            expect(result.value.deposit).toBe(3000);
            expect(result.value.utilities).toBe(150);
        });

        test('should reject unreasonable deposit', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                deposit: 150000 // Too high
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details[0].path).toContain('deposit');
        });

        test('should reject unreasonable utilities', () => {
            const property = {
                title: 'Test Property',
                source: 'funda.nl',
                url: 'https://www.funda.nl/test',
                location: 'Amsterdam',
                price: 1500,
                utilities: 2000 // Too high
            };

            const result = validateProperty(property);
            expect(result.error).toBeDefined();
            expect(result.error.details[0].path).toContain('utilities');
        });
    });

    describe('Data quality validation', () => {
        test('should validate complete property with all enhanced rules', () => {
            const property = {
                title: 'Complete Enhanced Property',
                description: 'A well-structured property with all valid fields',
                source: 'funda.nl',
                url: 'https://www.funda.nl/enhanced-test',
                location: 'Amsterdam',
                propertyType: 'appartement',
                size: '85+ m²',
                area: 85,
                rooms: '3+',
                bedrooms: '2',
                bathrooms: '1',
                year: '2020',
                price: 1800,
                interior: 'Gestoffeerd',
                furnished: false,
                pets: false,
                smoking: false,
                garden: false,
                balcony: true,
                parking: true,
                energyLabel: 'B',
                images: ['https://example.com/image1.jpg'],
                isActive: true,
                features: ['balcony', 'parking', 'elevator'],
                deposit: 3600,
                utilities: 125,
                dateAvailable: '2024-02-01T00:00:00.000Z',
                contactInfo: {
                    name: 'Test Contact',
                    email: '<EMAIL>'
                }
            };

            const result = validateProperty(property);
            expect(result.error).toBeUndefined();
            expect(result.value.title).toBe(property.title);
            expect(result.value.size).toBe('85+ m²');
            expect(result.value.rooms).toBe('3+');
            expect(result.value.year).toBe('2020');
        });
    });
});