/**
 * Tests for Mapping Configuration Loader
 */

const path = require('path');
const fs = require('fs').promises;
const { MappingConfigLoader } = require('../services/mappingConfigLoader');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');

// Mock fs.promises
jest.mock('fs', () => ({
  promises: {
    readFile: jest.fn(),
    readdir: jest.fn(),
    stat: jest.fn()
  }
}));

describe('MappingConfigLoader', () => {
  let loader;
  let registry;

  beforeEach(() => {
    registry = new FieldMappingRegistry();
    loader = new MappingConfigLoader(registry);
    
    // Reset mocks
    fs.readFile.mockReset();
    fs.readdir.mockReset();
  });

  describe('constructor', () => {
    test('should initialize with provided registry', () => {
      expect(loader.registry).toBe(registry);
    });

    test('should create new registry if none provided', () => {
      const newLoader = new MappingConfigLoader();
      expect(newLoader.registry).toBeInstanceOf(FieldMappingRegistry);
    });

    test('should initialize empty configPaths map', () => {
      expect(loader.configPaths).toBeInstanceOf(Map);
      expect(loader.configPaths.size).toBe(0);
    });
  });

  describe('setRegistry', () => {
    test('should set registry instance', () => {
      const newRegistry = new FieldMappingRegistry();
      loader.setRegistry(newRegistry);
      expect(loader.registry).toBe(newRegistry);
    });

    test('should throw error for invalid registry', () => {
      expect(() => loader.setRegistry({})).toThrow('Registry must be an instance of FieldMappingRegistry');
      expect(() => loader.setRegistry(null)).toThrow('Registry must be an instance of FieldMappingRegistry');
    });
  });

  describe('loadFromFile', () => {
    beforeEach(() => {
      // Mock successful file read
      fs.readFile.mockResolvedValue(JSON.stringify({
        title: 'title',
        description: 'description',
        source: { value: 'funda.nl' }
      }));
      
      // Mock registry methods
      registry.loadMappingFromFile = jest.fn().mockImplementation(async (filePath) => {
        const content = await fs.readFile(filePath, 'utf8');
        return JSON.parse(content);
      });
      registry.registerMapping = jest.fn();
    });

    test('should load mapping from file', async () => {
      const result = await loader.loadFromFile('/path/to/funda.json');
      
      expect(registry.loadMappingFromFile).toHaveBeenCalledWith(expect.stringContaining('funda.json'));
      expect(registry.registerMapping).toHaveBeenCalledWith('funda', expect.any(Object));
      expect(result.source).toBe('funda');
      expect(result.path).toContain('funda.json');
      expect(loader.configPaths.get('funda')).toContain('funda.json');
    });

    test('should use provided source identifier', async () => {
      const result = await loader.loadFromFile('/path/to/mapping.json', 'custom-source');
      
      expect(registry.registerMapping).toHaveBeenCalledWith('custom-source', expect.any(Object));
      expect(result.source).toBe('custom-source');
      expect(loader.configPaths.get('custom-source')).toContain('mapping.json');
    });

    test('should throw error if registry not set', async () => {
      loader.registry = null;
      await expect(loader.loadFromFile('/path/to/funda.json')).rejects.toThrow('Registry not set');
    });

    test('should throw error if file loading fails', async () => {
      registry.loadMappingFromFile.mockRejectedValue(new Error('File not found'));
      await expect(loader.loadFromFile('/path/to/nonexistent.json')).rejects.toThrow('Failed to load mapping');
    });
  });

  describe('loadFromFiles', () => {
    beforeEach(() => {
      // Mock registry methods
      registry.loadMappingFromFile = jest.fn().mockImplementation(async (filePath) => {
        if (filePath.includes('error')) {
          throw new Error('File error');
        }
        return { title: 'title' };
      });
      registry.registerMapping = jest.fn();
      
      // Mock console.warn
      console.warn = jest.fn();
    });

    test('should load mappings from multiple files', async () => {
      const files = [
        '/path/to/funda.json',
        { path: '/path/to/huurwoningen.json', source: 'huurwoningen' }
      ];

      const results = await loader.loadFromFiles(files);
      
      expect(results).toHaveLength(2);
      expect(registry.loadMappingFromFile).toHaveBeenCalledTimes(2);
      expect(registry.registerMapping).toHaveBeenCalledTimes(2);
      expect(registry.registerMapping).toHaveBeenCalledWith('funda', expect.any(Object));
      expect(registry.registerMapping).toHaveBeenCalledWith('huurwoningen', expect.any(Object));
    });

    test('should handle errors and continue loading', async () => {
      const files = [
        '/path/to/funda.json',
        '/path/to/error.json',
        '/path/to/huurwoningen.json'
      ];

      const results = await loader.loadFromFiles(files);
      
      expect(results).toHaveLength(2); // Only 2 successful loads
      expect(registry.loadMappingFromFile).toHaveBeenCalledTimes(3);
      expect(registry.registerMapping).toHaveBeenCalledTimes(2);
      expect(console.warn).toHaveBeenCalled();
    });

    test('should throw error for invalid file specification', async () => {
      const files = [
        '/path/to/funda.json',
        { invalid: 'specification' }
      ];

      const results = await loader.loadFromFiles(files);
      
      expect(results).toHaveLength(1); // Only 1 successful load
      expect(console.warn).toHaveBeenCalled();
    });
  });

  describe('loadFromDirectory', () => {
    beforeEach(() => {
      // Mock directory read
      fs.readdir.mockResolvedValue([
        'funda.json',
        'huurwoningen.json',
        'pararius.json',
        'README.md' // Non-JSON file
      ]);
      
      // Mock registry methods
      registry.loadMappingFromFile = jest.fn().mockImplementation(async (filePath) => {
        if (filePath.includes('pararius')) {
          throw new Error('File error');
        }
        return { title: 'title' };
      });
      registry.registerMapping = jest.fn();
      
      // Mock file finder
      loader._findFiles = jest.fn().mockImplementation(async (dir, pattern) => {
        const files = await fs.readdir(dir);
        return files
          .filter(file => pattern.test(file))
          .map(file => path.join(dir, file));
      });
      
      // Mock console.warn
      console.warn = jest.fn();
    });

    test('should load mappings from directory', async () => {
      const results = await loader.loadFromDirectory('/path/to/mappings');
      
      expect(loader._findFiles).toHaveBeenCalledWith(
        expect.stringContaining('mappings'),
        /\.json$/,
        false
      );
      expect(results).toHaveLength(2); // Only JSON files that didn't error
      expect(registry.registerMapping).toHaveBeenCalledTimes(2);
      expect(registry.registerMapping).toHaveBeenCalledWith('funda', expect.any(Object));
      expect(registry.registerMapping).toHaveBeenCalledWith('huurwoningen', expect.any(Object));
      expect(console.warn).toHaveBeenCalled(); // Warning for pararius error
    });

    test('should use custom options', async () => {
      const sourceNameExtractor = jest.fn().mockImplementation((filePath) => {
        return path.basename(filePath, '.json') + '-custom';
      });
      
      const results = await loader.loadFromDirectory('/path/to/mappings', {
        pattern: /\.json$/,
        recursive: true,
        sourceNameExtractor
      });
      
      expect(loader._findFiles).toHaveBeenCalledWith(
        expect.stringContaining('mappings'),
        /\.json$/,
        true
      );
      expect(sourceNameExtractor).toHaveBeenCalled();
      expect(registry.registerMapping).toHaveBeenCalledWith('funda-custom', expect.any(Object));
      expect(registry.registerMapping).toHaveBeenCalledWith('huurwoningen-custom', expect.any(Object));
    });

    test('should throw error if registry not set', async () => {
      loader.registry = null;
      await expect(loader.loadFromDirectory('/path/to/mappings')).rejects.toThrow('Registry not set');
    });

    test('should throw error if directory reading fails', async () => {
      loader._findFiles.mockRejectedValue(new Error('Directory not found'));
      await expect(loader.loadFromDirectory('/path/to/nonexistent')).rejects.toThrow();
    });
  });

  describe('_findFiles', () => {
    beforeEach(() => {
      // Mock directory read
      fs.readdir.mockImplementation(async (dir, options) => {
        if (dir.includes('empty')) {
          return [];
        }
        
        if (dir.includes('error')) {
          throw new Error('Directory error');
        }
        
        if (dir.includes('sub')) {
          return [
            { name: 'sub1.json', isFile: () => true, isDirectory: () => false },
            { name: 'sub2.json', isFile: () => true, isDirectory: () => false }
          ];
        }
        
        return [
          { name: 'file1.json', isFile: () => true, isDirectory: () => false },
          { name: 'file2.txt', isFile: () => true, isDirectory: () => false },
          { name: 'subdir', isFile: () => false, isDirectory: () => true }
        ];
      });
    });

    test('should find files matching pattern', async () => {
      const files = await loader._findFiles('/path/to/mappings', /\.json$/, false);
      expect(files).toHaveLength(1);
      expect(files[0]).toContain('file1.json');
    });

    test('should search recursively when specified', async () => {
      fs.readdir.mockImplementationOnce(async () => [
        { name: 'file1.json', isFile: () => true, isDirectory: () => false },
        { name: 'subdir', isFile: () => false, isDirectory: () => true }
      ]);
      
      const files = await loader._findFiles('/path/to/mappings', /\.json$/, true);
      expect(files).toHaveLength(3); // 1 from root + 2 from subdir
      expect(files[0]).toContain('file1.json');
      expect(files[1]).toContain('sub1.json');
      expect(files[2]).toContain('sub2.json');
    });

    test('should handle empty directories', async () => {
      const files = await loader._findFiles('/path/to/empty', /\.json$/, false);
      expect(files).toHaveLength(0);
    });

    test('should throw error if directory reading fails', async () => {
      await expect(loader._findFiles('/path/to/error', /\.json$/, false)).rejects.toThrow('Failed to read directory');
    });
  });

  describe('loadFromObject', () => {
    beforeEach(() => {
      registry.registerMapping = jest.fn();
    });

    test('should load mapping from object', () => {
      const config = {
        title: 'title',
        description: 'description'
      };
      
      const result = loader.loadFromObject('custom-source', config);
      
      expect(registry.registerMapping).toHaveBeenCalledWith('custom-source', config);
      expect(result.source).toBe('custom-source');
      expect(result.config).toBe(config);
      expect(result.path).toBe('<object>');
      expect(loader.configPaths.get('custom-source')).toBe('<object>');
    });

    test('should throw error if registry not set', () => {
      loader.registry = null;
      expect(() => loader.loadFromObject('source', {})).toThrow('Registry not set');
    });
  });

  describe('loadDefaults', () => {
    beforeEach(() => {
      loader.loadFromObject = jest.fn().mockImplementation((source, config) => {
        return { source, config, path: '<object>' };
      });
      
      // Mock _getDefaultConfigurations
      loader._getDefaultConfigurations = jest.fn().mockReturnValue({
        funda: { title: 'title' },
        huurwoningen: { title: 'title' },
        pararius: { title: 'title' }
      });
      
      // Mock console.warn
      console.warn = jest.fn();
    });

    test('should load default configurations', async () => {
      const results = await loader.loadDefaults();
      
      expect(results).toHaveLength(3);
      expect(loader.loadFromObject).toHaveBeenCalledTimes(3);
      expect(loader.loadFromObject).toHaveBeenCalledWith('funda', expect.any(Object));
      expect(loader.loadFromObject).toHaveBeenCalledWith('huurwoningen', expect.any(Object));
      expect(loader.loadFromObject).toHaveBeenCalledWith('pararius', expect.any(Object));
    });

    test('should handle errors and continue loading', async () => {
      loader.loadFromObject.mockImplementation((source, config) => {
        if (source === 'pararius') {
          throw new Error('Config error');
        }
        return { source, config, path: '<object>' };
      });
      
      const results = await loader.loadDefaults();
      
      expect(results).toHaveLength(2); // Only 2 successful loads
      expect(loader.loadFromObject).toHaveBeenCalledTimes(3);
      expect(console.warn).toHaveBeenCalled();
    });
  });

  describe('_getDefaultConfigurations', () => {
    test('should return default configurations', () => {
      const configs = loader._getDefaultConfigurations();
      
      expect(configs).toHaveProperty('funda');
      expect(configs).toHaveProperty('huurwoningen');
      expect(configs).toHaveProperty('pararius');
      
      // Check structure of funda config
      expect(configs.funda).toHaveProperty('title');
      expect(configs.funda).toHaveProperty('description');
      expect(configs.funda).toHaveProperty('source');
      expect(configs.funda).toHaveProperty('price');
      expect(configs.funda).toHaveProperty('location');
    });
  });

  describe('reloadSource', () => {
    beforeEach(() => {
      loader.configPaths.set('funda', '/path/to/funda.json');
      loader.configPaths.set('object-source', '<object>');
      loader.loadFromFile = jest.fn().mockResolvedValue({
        source: 'funda',
        config: { title: 'title' },
        path: '/path/to/funda.json'
      });
    });

    test('should reload source from file', async () => {
      const result = await loader.reloadSource('funda');
      
      expect(loader.loadFromFile).toHaveBeenCalledWith('/path/to/funda.json', 'funda');
      expect(result.source).toBe('funda');
    });

    test('should throw error for object source', async () => {
      await expect(loader.reloadSource('object-source')).rejects.toThrow('Cannot reload source');
    });

    test('should throw error for non-existent source', async () => {
      await expect(loader.reloadSource('nonexistent')).rejects.toThrow('Cannot reload source');
    });
  });

  describe('getLoadedConfigs', () => {
    beforeEach(() => {
      loader.configPaths.set('funda', '/path/to/funda.json');
      loader.configPaths.set('huurwoningen', '/path/to/huurwoningen.json');
      
      registry.getAllMappings = jest.fn().mockImplementation((source) => {
        if (source === 'funda') {
          return { title: 'title', price: { path: 'price' } };
        }
        if (source === 'huurwoningen') {
          return { title: 'title', size: { path: 'size' } };
        }
        return null;
      });
    });

    test('should return information about loaded configurations', () => {
      const configs = loader.getLoadedConfigs();
      
      expect(configs).toHaveProperty('funda');
      expect(configs).toHaveProperty('huurwoningen');
      expect(configs.funda.path).toBe('/path/to/funda.json');
      expect(configs.funda.fieldCount).toBe(2);
      expect(configs.huurwoningen.path).toBe('/path/to/huurwoningen.json');
      expect(configs.huurwoningen.fieldCount).toBe(2);
    });
  });

  describe('validateAllConfigs', () => {
    beforeEach(() => {
      loader.configPaths.set('funda', '/path/to/funda.json');
      loader.configPaths.set('huurwoningen', '/path/to/huurwoningen.json');
      
      registry.getRegisteredSources = jest.fn().mockReturnValue(['funda', 'huurwoningen']);
      registry.getAllMappings = jest.fn().mockImplementation((source) => {
        return { title: 'title' };
      });
      registry.validateMapping = jest.fn().mockImplementation((mapping) => {
        if (mapping.title === 'title') {
          return { error: null };
        }
        return { error: new Error('Invalid mapping') };
      });
    });

    test('should validate all loaded configurations', () => {
      const results = loader.validateAllConfigs();
      
      expect(results).toHaveProperty('funda');
      expect(results).toHaveProperty('huurwoningen');
      expect(results.funda.valid).toBe(true);
      expect(results.funda.error).toBeNull();
      expect(results.funda.path).toBe('/path/to/funda.json');
      expect(results.huurwoningen.valid).toBe(true);
      expect(results.huurwoningen.error).toBeNull();
    });

    test('should report validation errors', () => {
      registry.validateMapping.mockImplementationOnce(() => {
        return { error: new Error('Invalid mapping') };
      });
      
      const results = loader.validateAllConfigs();
      
      expect(results.funda.valid).toBe(false);
      expect(results.funda.error).toBe('Invalid mapping');
    });
  });

  describe('clear', () => {
    beforeEach(() => {
      loader.configPaths.set('funda', '/path/to/funda.json');
      loader.configPaths.set('huurwoningen', '/path/to/huurwoningen.json');
      registry.clearAll = jest.fn();
    });

    test('should clear all loaded configurations', () => {
      loader.clear();
      
      expect(registry.clearAll).toHaveBeenCalled();
      expect(loader.configPaths.size).toBe(0);
    });

    test('should not throw if registry is null', () => {
      loader.registry = null;
      expect(() => loader.clear()).not.toThrow();
      expect(loader.configPaths.size).toBe(0);
    });
  });
});