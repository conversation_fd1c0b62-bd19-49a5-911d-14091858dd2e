const AntiDetectionSystem = require('../../services/antiDetectionSystem');
const puppeteer = require('puppeteer');

// Mock puppeteer
jest.mock('puppeteer');

// Mock logger
jest.mock('../../services/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

describe('AntiDetectionSystem', () => {
  let antiDetectionSystem;
  let mockBrowser;
  let mockPage;

  beforeEach(() => {
    antiDetectionSystem = new AntiDetectionSystem();
    
    // Setup mocks
    mockPage = {
      setViewport: jest.fn(),
      setUserAgent: jest.fn(),
      setExtraHTTPHeaders: jest.fn(),
      evaluateOnNewDocument: jest.fn(),
      content: jest.fn(),
      url: jest.fn(),
      response: { status: jest.fn() },
      screenshot: jest.fn(),
      viewport: jest.fn(),
      mouse: {
        move: jest.fn()
      },
      evaluate: jest.fn(),
      focus: jest.fn(),
      keyboard: {
        type: jest.fn(),
        press: jest.fn()
      }
    };

    mockBrowser = {
      newPage: jest.fn().mockResolvedValue(mockPage),
      close: jest.fn()
    };

    puppeteer.launch = jest.fn().mockResolvedValue(mockBrowser);
    
    // Clear any existing sessions
    antiDetectionSystem.sessionData.clear();
  });

  afterEach(() => {
    jest.clearAllMocks();
    // Restore Math.random if it was mocked
    if (Math.random.mockRestore) {
      Math.random.mockRestore();
    }
  });

  describe('constructor', () => {
    it('should initialize with stealth profiles and empty session data', () => {
      expect(antiDetectionSystem.stealthProfiles).toBeDefined();
      expect(antiDetectionSystem.stealthProfiles.length).toBeGreaterThan(0);
      expect(antiDetectionSystem.sessionData).toBeInstanceOf(Map);
      expect(antiDetectionSystem.sessionData.size).toBe(0);
      expect(antiDetectionSystem.captchaDetectionPatterns).toBeDefined();
    });

    it('should have comprehensive CAPTCHA detection patterns', () => {
      const patterns = antiDetectionSystem.captchaDetectionPatterns;
      expect(patterns).toContain('captcha');
      expect(patterns).toContain('recaptcha');
      expect(patterns).toContain('hcaptcha');
      expect(patterns).toContain('cloudflare');
      expect(patterns).toContain('Je bent bijna op de pagina die je zoekt');
    });
  });

  describe('generateStealthProfiles', () => {
    it('should generate multiple stealth profiles', () => {
      const profiles = antiDetectionSystem.generateStealthProfiles();
      
      expect(profiles.length).toBeGreaterThan(0);
      expect(profiles[0]).toHaveProperty('userAgent');
      expect(profiles[0]).toHaveProperty('viewport');
      expect(profiles[0]).toHaveProperty('languages');
      expect(profiles[0]).toHaveProperty('timezone');
      expect(profiles[0]).toHaveProperty('platform');
    });

    it('should generate profiles with realistic properties', () => {
      const profiles = antiDetectionSystem.generateStealthProfiles();
      const profile = profiles[0];

      expect(profile.userAgent).toContain('Chrome');
      expect(profile.viewport.width).toBeGreaterThan(0);
      expect(profile.viewport.height).toBeGreaterThan(0);
      expect(Array.isArray(profile.languages)).toBe(true);
      expect(profile.hardwareConcurrency).toBeGreaterThanOrEqual(4);
      expect([4, 8, 16]).toContain(profile.deviceMemory);
    });
  });

  describe('setupStealthBrowser', () => {
    it('should create a stealth browser with proper configuration', async () => {
      const result = await antiDetectionSystem.setupStealthBrowser();

      expect(puppeteer.launch).toHaveBeenCalledWith(
        expect.objectContaining({
          headless: 'new',
          args: expect.arrayContaining([
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage'
          ]),
          ignoreDefaultArgs: ['--enable-automation'],
          ignoreHTTPSErrors: true
        })
      );

      expect(result).toHaveProperty('browser');
      expect(result).toHaveProperty('sessionId');
      expect(result).toHaveProperty('profile');
      expect(antiDetectionSystem.sessionData.size).toBe(1);
    });

    it('should store session data correctly', async () => {
      const result = await antiDetectionSystem.setupStealthBrowser();
      const sessionData = antiDetectionSystem.sessionData.get(result.sessionId);

      expect(sessionData).toBeDefined();
      expect(sessionData.browser).toBe(mockBrowser);
      expect(sessionData.profile).toBe(result.profile);
      expect(sessionData.createdAt).toBeInstanceOf(Date);
      expect(sessionData.pageCount).toBe(0);
    });

    it('should handle browser launch errors', async () => {
      puppeteer.launch.mockRejectedValue(new Error('Launch failed'));

      await expect(antiDetectionSystem.setupStealthBrowser())
        .rejects.toThrow('Launch failed');
    });
  });

  describe('randomizeFingerprint', () => {
    it('should apply comprehensive stealth measures to page', async () => {
      const profile = antiDetectionSystem.getRandomStealthProfile();
      
      await antiDetectionSystem.randomizeFingerprint(mockPage, profile);

      expect(mockPage.setViewport).toHaveBeenCalledWith(profile.viewport);
      expect(mockPage.setUserAgent).toHaveBeenCalledWith(profile.userAgent);
      expect(mockPage.setExtraHTTPHeaders).toHaveBeenCalledWith(
        expect.objectContaining({
          'Accept-Language': profile.languages.join(','),
          'Sec-Ch-Ua-Platform': expect.any(String)
        })
      );
      expect(mockPage.evaluateOnNewDocument).toHaveBeenCalled();
    });

    it('should use random profile if none provided', async () => {
      await antiDetectionSystem.randomizeFingerprint(mockPage);

      expect(mockPage.setViewport).toHaveBeenCalled();
      expect(mockPage.setUserAgent).toHaveBeenCalled();
      expect(mockPage.setExtraHTTPHeaders).toHaveBeenCalled();
    });

    it('should handle fingerprint randomization errors', async () => {
      mockPage.setViewport.mockRejectedValue(new Error('Viewport error'));

      await expect(antiDetectionSystem.randomizeFingerprint(mockPage))
        .rejects.toThrow('Viewport error');
    });
  });

  describe('simulateHumanBehavior', () => {
    beforeEach(() => {
      mockPage.viewport.mockResolvedValue({ width: 1920, height: 1080 });
    });

    it('should simulate human-like interactions', async () => {
      await antiDetectionSystem.simulateHumanBehavior(mockPage);

      expect(mockPage.mouse.move).toHaveBeenCalled();
      expect(mockPage.evaluate).toHaveBeenCalled();
    });

    it('should respect behavior options', async () => {
      await antiDetectionSystem.simulateHumanBehavior(mockPage, {
        mouseMovements: false,
        scrolling: false,
        delays: false
      });

      // Should still complete without errors
      expect(true).toBe(true);
    });

    it('should handle simulation errors gracefully', async () => {
      mockPage.mouse.move.mockRejectedValue(new Error('Mouse error'));

      // Should not throw, just log error
      await expect(antiDetectionSystem.simulateHumanBehavior(mockPage))
        .resolves.not.toThrow();
    });
  });

  describe('simulateTyping', () => {
    it('should simulate realistic typing with delays', async () => {
      // Mock addRandomDelay to avoid actual delays
      const originalAddRandomDelay = antiDetectionSystem.addRandomDelay;
      antiDetectionSystem.addRandomDelay = jest.fn().mockResolvedValue();
      
      const text = '<EMAIL>';
      const selector = '#email';

      await antiDetectionSystem.simulateTyping(mockPage, selector, text, {
        mistakes: false // Disable mistakes for this test
      });

      expect(mockPage.focus).toHaveBeenCalledWith(selector);
      expect(mockPage.keyboard.type).toHaveBeenCalledTimes(text.length);
      
      // Restore mock
      antiDetectionSystem.addRandomDelay = originalAddRandomDelay;
    });

    it('should simulate typing mistakes occasionally', async () => {
      // Mock addRandomDelay to avoid actual delays
      const originalAddRandomDelay = antiDetectionSystem.addRandomDelay;
      antiDetectionSystem.addRandomDelay = jest.fn().mockResolvedValue();
      
      // Mock Math.random to always trigger mistakes
      const originalRandom = Math.random;
      Math.random = jest.fn().mockReturnValue(0.01); // Always trigger mistake (< 0.02)

      const text = 'a';
      await antiDetectionSystem.simulateTyping(mockPage, '#input', text, {
        mistakes: true
      });

      expect(mockPage.keyboard.press).toHaveBeenCalledWith('Backspace');
      
      // Restore mocks
      Math.random = originalRandom;
      antiDetectionSystem.addRandomDelay = originalAddRandomDelay;
    });

    it('should handle typing errors', async () => {
      mockPage.focus.mockRejectedValue(new Error('Focus error'));

      await expect(antiDetectionSystem.simulateTyping(mockPage, '#input', 'test'))
        .rejects.toThrow('Focus error');
    });
  });

  describe('handleCaptcha', () => {
    it('should detect CAPTCHA patterns in page content', async () => {
      mockPage.content.mockResolvedValue('<div>Please complete the reCAPTCHA</div>');
      mockPage.url.mockReturnValue('https://example.com');
      mockPage.screenshot.mockResolvedValue(Buffer.from('screenshot'));

      const result = await antiDetectionSystem.handleCaptcha(mockPage);

      expect(result.detected).toBe(true);
      expect(result.type).toBe('reCAPTCHA');
      expect(result.url).toBe('https://example.com');
      expect(result.screenshot).toBeDefined();
    });

    it('should detect Funda-specific verification pages', async () => {
      mockPage.content.mockResolvedValue('Je bent bijna op de pagina die je zoekt');
      mockPage.url.mockReturnValue('https://funda.nl');
      mockPage.screenshot.mockResolvedValue(Buffer.from('screenshot'));

      const result = await antiDetectionSystem.handleCaptcha(mockPage);

      expect(result.detected).toBe(true);
      expect(result.type).toBe('Funda Verification');
    });

    it('should return false for normal pages', async () => {
      mockPage.content.mockResolvedValue('<div>Normal page content</div>');

      const result = await antiDetectionSystem.handleCaptcha(mockPage);

      expect(result.detected).toBe(false);
    });

    it('should handle CAPTCHA detection errors', async () => {
      mockPage.content.mockRejectedValue(new Error('Content error'));

      const result = await antiDetectionSystem.handleCaptcha(mockPage);

      expect(result.detected).toBe(false);
      expect(result.error).toBe('Content error');
    });
  });

  describe('detectBlocking', () => {
    it('should detect blocking based on content', async () => {
      mockPage.content.mockResolvedValue('Access denied - too many requests');
      mockPage.url.mockReturnValue('https://example.com');

      const result = await antiDetectionSystem.detectBlocking(mockPage);

      expect(result.blocked).toBe(true);
      expect(result.url).toBe('https://example.com');
    });

    it('should detect blocking based on HTTP status', async () => {
      mockPage.content.mockResolvedValue('Normal content');
      mockPage.response = { status: () => 429 };

      const result = await antiDetectionSystem.detectBlocking(mockPage);

      expect(result.blocked).toBe(true);
      expect(result.status).toBe(429);
    });

    it('should return false for normal pages', async () => {
      mockPage.content.mockResolvedValue('Normal page content');
      mockPage.response = { status: () => 200 };

      const result = await antiDetectionSystem.detectBlocking(mockPage);

      expect(result.blocked).toBe(false);
    });
  });

  describe('session management', () => {
    it('should cleanup individual sessions', async () => {
      const { sessionId } = await antiDetectionSystem.setupStealthBrowser();
      
      expect(antiDetectionSystem.sessionData.size).toBe(1);
      
      await antiDetectionSystem.cleanupSession(sessionId);
      
      expect(antiDetectionSystem.sessionData.size).toBe(0);
      expect(mockBrowser.close).toHaveBeenCalled();
    });

    it('should cleanup all sessions', async () => {
      const session1 = await antiDetectionSystem.setupStealthBrowser();
      const session2 = await antiDetectionSystem.setupStealthBrowser();
      
      expect(antiDetectionSystem.sessionData.size).toBe(2);
      
      await antiDetectionSystem.cleanupAllSessions();
      
      expect(antiDetectionSystem.sessionData.size).toBe(0);
      expect(mockBrowser.close).toHaveBeenCalledTimes(2);
    });

    it('should handle cleanup errors gracefully', async () => {
      const { sessionId } = await antiDetectionSystem.setupStealthBrowser();
      mockBrowser.close.mockRejectedValue(new Error('Close error'));

      // Should not throw
      await expect(antiDetectionSystem.cleanupSession(sessionId))
        .resolves.not.toThrow();
    });
  });

  describe('adaptToChanges', () => {
    it('should adapt to fingerprint detection', async () => {
      const originalProfiles = antiDetectionSystem.stealthProfiles;
      
      const result = await antiDetectionSystem.adaptToChanges({
        type: 'fingerprint'
      });

      expect(result.adapted).toBe(true);
      expect(antiDetectionSystem.stealthProfiles).not.toBe(originalProfiles);
    });

    it('should recommend delays for rate limiting', async () => {
      const result = await antiDetectionSystem.adaptToChanges({
        type: 'rate_limit'
      });

      expect(result.adapted).toBe(true);
      expect(result.recommendedDelay).toBe(60000);
    });

    it('should handle unknown detection types', async () => {
      const result = await antiDetectionSystem.adaptToChanges({
        type: 'unknown'
      });

      expect(result.adapted).toBe(true);
    });
  });

  describe('utility methods', () => {
    it('should get random stealth profile', () => {
      const profile = antiDetectionSystem.getRandomStealthProfile();
      
      expect(profile).toHaveProperty('userAgent');
      expect(profile).toHaveProperty('viewport');
      expect(antiDetectionSystem.stealthProfiles).toContain(profile);
    });

    it('should generate unique session IDs', () => {
      const id1 = antiDetectionSystem.generateSessionId();
      const id2 = antiDetectionSystem.generateSessionId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^session_\d+_[a-z0-9]+_[a-z0-9]+_\d+_[a-z0-9]+$/);
    });

    it('should add random delays', async () => {
      const start = Date.now();
      await antiDetectionSystem.addRandomDelay(100, 200);
      const end = Date.now();
      
      expect(end - start).toBeGreaterThanOrEqual(100);
      expect(end - start).toBeLessThan(300); // Allow some margin
    });

    it('should provide session statistics', async () => {
      const session1 = await antiDetectionSystem.setupStealthBrowser();
      const session2 = await antiDetectionSystem.setupStealthBrowser();
      
      const stats = antiDetectionSystem.getSessionStats();
      
      expect(stats.activeSessions).toBe(2);
      expect(stats.totalPageViews).toBe(0);
      expect(typeof stats.oldestSession).toBe('number');
      expect(stats.profiles).toBeGreaterThan(0);
    });
  });

  describe('identifyCaptchaType', () => {
    it('should identify different CAPTCHA types', () => {
      expect(antiDetectionSystem.identifyCaptchaType('reCAPTCHA challenge'))
        .toBe('reCAPTCHA');
      
      expect(antiDetectionSystem.identifyCaptchaType('hCaptcha verification'))
        .toBe('hCaptcha');
      
      expect(antiDetectionSystem.identifyCaptchaType('Cloudflare security check'))
        .toBe('Cloudflare');
      
      expect(antiDetectionSystem.identifyCaptchaType('Funda verification page'))
        .toBe('Funda Verification');
      
      expect(antiDetectionSystem.identifyCaptchaType('Unknown verification'))
        .toBe('Unknown');
    });
  });

  describe('error handling', () => {
    it('should handle errors in simulateMouseMovements', async () => {
      mockPage.viewport.mockRejectedValue(new Error('Viewport error'));

      // Should not throw, just log error
      await expect(antiDetectionSystem.simulateMouseMovements(mockPage))
        .resolves.not.toThrow();
    });

    it('should handle errors in simulateScrolling', async () => {
      mockPage.evaluate.mockRejectedValue(new Error('Evaluate error'));

      // Should not throw, just log error
      await expect(antiDetectionSystem.simulateScrolling(mockPage))
        .resolves.not.toThrow();
    });
  });

  describe('integration scenarios', () => {
    it('should handle complete stealth setup workflow', async () => {
      const { browser, sessionId, profile } = await antiDetectionSystem.setupStealthBrowser();
      const page = await browser.newPage();
      
      await antiDetectionSystem.randomizeFingerprint(page, profile);
      await antiDetectionSystem.simulateHumanBehavior(page);
      
      const captchaResult = await antiDetectionSystem.handleCaptcha(page);
      const blockingResult = await antiDetectionSystem.detectBlocking(page);
      
      await antiDetectionSystem.cleanupSession(sessionId);

      expect(captchaResult).toHaveProperty('detected');
      expect(blockingResult).toHaveProperty('blocked');
      expect(antiDetectionSystem.sessionData.size).toBe(0);
    });
  });
});