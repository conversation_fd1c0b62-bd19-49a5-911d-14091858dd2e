const cacheService = require('../../services/cacheService');

// Mock Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    connect: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    setex: jest.fn(),
    del: jest.fn(),
    keys: jest.fn(),
    exists: jest.fn(),
    info: jest.fn(),
    flushall: jest.fn(),
    quit: jest.fn(),
    on: jest.fn()
  }));
});

// Mock config
jest.mock('../../config/config', () => ({
  redisHost: 'localhost',
  redisPort: 6379,
  redisPassword: null
}));

describe('CacheService', () => {
  let mockRedisClient;

  beforeEach(() => {
    jest.clearAllMocks();
    // Get the mocked Redis client instance
    const Redis = require('ioredis');
    mockRedisClient = new Redis();
    // Simulate connected state
    cacheService.isConnected = true;
    cacheService.client = mockRedisClient;
  });

  describe('get', () => {
    it('should return parsed JSON data when cache hit', async () => {
      // Arrange
      const testData = { totalListings: 100, averagePrice: 2500 };
      mockRedisClient.get.mockResolvedValue(JSON.stringify(testData));

      // Act
      const result = await cacheService.get('test-key');

      // Assert
      expect(mockRedisClient.get).toHaveBeenCalledWith('test-key');
      expect(result).toEqual(testData);
    });

    it('should return null when cache miss', async () => {
      // Arrange
      mockRedisClient.get.mockResolvedValue(null);

      // Act
      const result = await cacheService.get('test-key');

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when not connected', async () => {
      // Arrange
      cacheService.isConnected = false;

      // Act
      const result = await cacheService.get('test-key');

      // Assert
      expect(mockRedisClient.get).not.toHaveBeenCalled();
      expect(result).toBeNull();
    });

    it('should return null and log error when Redis operation fails', async () => {
      // Arrange
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockRedisClient.get.mockRejectedValue(new Error('Redis error'));

      // Act
      const result = await cacheService.get('test-key');

      // Assert
      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Cache get error:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });

    it('should handle invalid JSON gracefully', async () => {
      // Arrange
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockRedisClient.get.mockResolvedValue('invalid-json');

      // Act
      const result = await cacheService.get('test-key');

      // Assert
      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('set', () => {
    it('should set value with TTL when TTL is provided', async () => {
      // Arrange
      const testData = { totalListings: 100 };
      const ttl = 300;
      mockRedisClient.setex.mockResolvedValue('OK');

      // Act
      const result = await cacheService.set('test-key', testData, ttl);

      // Assert
      expect(mockRedisClient.setex).toHaveBeenCalledWith('test-key', ttl, JSON.stringify(testData));
      expect(result).toBe(true);
    });

    it('should set value without TTL when TTL is 0', async () => {
      // Arrange
      const testData = { totalListings: 100 };
      mockRedisClient.set.mockResolvedValue('OK');

      // Act
      const result = await cacheService.set('test-key', testData, 0);

      // Assert
      expect(mockRedisClient.set).toHaveBeenCalledWith('test-key', JSON.stringify(testData));
      expect(result).toBe(true);
    });

    it('should return false when not connected', async () => {
      // Arrange
      cacheService.isConnected = false;

      // Act
      const result = await cacheService.set('test-key', {}, 300);

      // Assert
      expect(mockRedisClient.setex).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });

    it('should return false and log error when Redis operation fails', async () => {
      // Arrange
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockRedisClient.setex.mockRejectedValue(new Error('Redis error'));

      // Act
      const result = await cacheService.set('test-key', {}, 300);

      // Assert
      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Cache set error:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });

    it('should use default TTL of 3600 seconds when not specified', async () => {
      // Arrange
      const testData = { totalListings: 100 };
      mockRedisClient.setex.mockResolvedValue('OK');

      // Act
      await cacheService.set('test-key', testData);

      // Assert
      expect(mockRedisClient.setex).toHaveBeenCalledWith('test-key', 3600, JSON.stringify(testData));
    });
  });

  describe('del', () => {
    it('should delete key successfully', async () => {
      // Arrange
      mockRedisClient.del.mockResolvedValue(1);

      // Act
      const result = await cacheService.del('test-key');

      // Assert
      expect(mockRedisClient.del).toHaveBeenCalledWith('test-key');
      expect(result).toBe(true);
    });

    it('should return false when not connected', async () => {
      // Arrange
      cacheService.isConnected = false;

      // Act
      const result = await cacheService.del('test-key');

      // Assert
      expect(mockRedisClient.del).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });

    it('should return false and log error when Redis operation fails', async () => {
      // Arrange
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockRedisClient.del.mockRejectedValue(new Error('Redis error'));

      // Act
      const result = await cacheService.del('test-key');

      // Assert
      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Cache delete error:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('exists', () => {
    it('should return true when key exists', async () => {
      // Arrange
      mockRedisClient.exists.mockResolvedValue(1);

      // Act
      const result = await cacheService.exists('test-key');

      // Assert
      expect(mockRedisClient.exists).toHaveBeenCalledWith('test-key');
      expect(result).toBe(true);
    });

    it('should return false when key does not exist', async () => {
      // Arrange
      mockRedisClient.exists.mockResolvedValue(0);

      // Act
      const result = await cacheService.exists('test-key');

      // Assert
      expect(result).toBe(false);
    });

    it('should return false when not connected', async () => {
      // Arrange
      cacheService.isConnected = false;

      // Act
      const result = await cacheService.exists('test-key');

      // Assert
      expect(mockRedisClient.exists).not.toHaveBeenCalled();
      expect(result).toBe(false);
    });
  });

  describe('connection handling', () => {
    it('should initialize Redis client with proper configuration', () => {
      // Verify that Redis client was initialized
      expect(cacheService.client).toBeDefined();
      expect(cacheService.isConnected).toBe(true); // Set in beforeEach
    });
  });
});