const searchService = require('../../services/searchService');
const cacheService = require('../../services/cacheService');
const Listing = require('../../models/Listing');

// Mock dependencies
jest.mock('../../services/cacheService');
jest.mock('../../models/Listing');
jest.mock('../../services/logger', () => ({
  logHelpers: {
    logDbOperation: jest.fn()
  }
}));

describe('SearchService - Quick Stats Caching', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getQuickStats', () => {
    const mockStats = {
      totalListings: 1250,
      averagePrice: 2850,
      newToday: 15
    };

    const mockAggregationResult = [{
      totalCount: [{ count: 1250 }],
      avgPrice: [{ avgPrice: 2850.5 }],
      newToday: [{ count: 15 }]
    }];

    it('should return cached data when cache hit occurs', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(mockStats);

      // Act
      const result = await searchService.getQuickStats();

      // Assert
      expect(cacheService.get).toHaveBeenCalledWith('quick-stats');
      expect(result).toEqual({
        ...mockStats,
        cached: true
      });
      expect(Listing.aggregate).not.toHaveBeenCalled();
      expect(cacheService.set).not.toHaveBeenCalled();
    });

    it('should calculate stats from database when cache miss occurs', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(null);
      Listing.aggregate.mockResolvedValue(mockAggregationResult);
      cacheService.set.mockResolvedValue(true);

      // Act
      const result = await searchService.getQuickStats();

      // Assert
      expect(cacheService.get).toHaveBeenCalledWith('quick-stats');
      expect(Listing.aggregate).toHaveBeenCalled();
      expect(cacheService.set).toHaveBeenCalledWith('quick-stats', {
        totalListings: 1250,
        averagePrice: 2851, // Rounded from 2850.5
        newToday: 15
      }, 300); // 5 minutes in seconds
      expect(result).toEqual({
        totalListings: 1250,
        averagePrice: 2851,
        newToday: 15,
        cached: false
      });
    });

    it('should use correct cache key for quick stats', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(null);
      Listing.aggregate.mockResolvedValue(mockAggregationResult);

      // Act
      await searchService.getQuickStats();

      // Assert
      expect(cacheService.get).toHaveBeenCalledWith('quick-stats');
      expect(cacheService.set).toHaveBeenCalledWith('quick-stats', expect.any(Object), 300);
    });

    it('should cache results with 5-minute expiration (300 seconds)', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(null);
      Listing.aggregate.mockResolvedValue(mockAggregationResult);

      // Act
      await searchService.getQuickStats();

      // Assert
      expect(cacheService.set).toHaveBeenCalledWith('quick-stats', expect.any(Object), 300);
    });

    it('should fallback to database calculation when cache fails', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(null); // Cache miss, not error
      Listing.aggregate.mockResolvedValue(mockAggregationResult);

      // Act
      const result = await searchService.getQuickStats();

      // Assert
      expect(Listing.aggregate).toHaveBeenCalled();
      expect(result.cached).toBe(false);
      expect(result.totalListings).toBe(1250);
    });

    it('should continue when cache set fails', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(null);
      Listing.aggregate.mockResolvedValue(mockAggregationResult);
      cacheService.set.mockResolvedValue(false); // Cache set fails but doesn't throw

      // Act
      const result = await searchService.getQuickStats();

      // Assert
      expect(result).toEqual({
        totalListings: 1250,
        averagePrice: 2851,
        newToday: 15,
        cached: false
      });
    });

    it('should handle empty aggregation results with fallback values', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(null);
      Listing.aggregate.mockResolvedValue([{
        totalCount: [],
        avgPrice: [],
        newToday: []
      }]);
      cacheService.set.mockResolvedValue(true);

      // Act
      const result = await searchService.getQuickStats();

      // Assert
      expect(result).toEqual({
        totalListings: 0,
        averagePrice: 0,
        newToday: 0,
        cached: false
      });
    });

    it('should return default values and error info when database fails', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(null);
      const dbError = new Error('Database connection failed');
      Listing.aggregate.mockRejectedValue(dbError);

      // Act
      const result = await searchService.getQuickStats();

      // Assert
      expect(result).toEqual({
        totalListings: 0,
        averagePrice: 0,
        newToday: 0,
        cached: false,
        error: 'Database connection failed'
      });
    });

    it('should use correct aggregation pipeline structure', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(null);
      Listing.aggregate.mockResolvedValue(mockAggregationResult);

      // Act
      await searchService.getQuickStats();

      // Assert
      const aggregationCall = Listing.aggregate.mock.calls[0][0];
      expect(aggregationCall).toHaveLength(1);
      expect(aggregationCall[0]).toHaveProperty('$facet');
      expect(aggregationCall[0].$facet).toHaveProperty('totalCount');
      expect(aggregationCall[0].$facet).toHaveProperty('avgPrice');
      expect(aggregationCall[0].$facet).toHaveProperty('newToday');
    });

    it('should calculate start of today correctly for newToday filter', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(null);
      Listing.aggregate.mockResolvedValue(mockAggregationResult);
      
      const mockDate = new Date('2025-01-23T15:30:00.000Z');
      const originalDate = Date;
      global.Date = jest.fn(() => mockDate);
      global.Date.now = originalDate.now;

      // Act
      await searchService.getQuickStats();

      // Assert
      const aggregationCall = Listing.aggregate.mock.calls[0][0];
      const newTodayStage = aggregationCall[0].$facet.newToday;
      const matchStage = newTodayStage.find(stage => stage.$match);
      
      // Should match start of day (00:00:00)
      const expectedStartOfDay = new Date(2025, 0, 23); // Month is 0-indexed
      expect(matchStage.$match.dateAdded.$gte).toEqual(expectedStartOfDay);

      // Restore original Date
      global.Date = originalDate;
    });

    it('should round average price to nearest integer', async () => {
      // Arrange
      cacheService.get.mockResolvedValue(null);
      Listing.aggregate.mockResolvedValue([{
        totalCount: [{ count: 100 }],
        avgPrice: [{ avgPrice: 2849.7 }],
        newToday: [{ count: 5 }]
      }]);
      cacheService.set.mockResolvedValue(true);

      // Act
      const result = await searchService.getQuickStats();

      // Assert
      expect(result.averagePrice).toBe(2850); // Rounded from 2849.7
    });
  });
});