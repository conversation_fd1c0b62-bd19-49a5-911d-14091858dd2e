// Mock the OpenAI service before requiring aiService
jest.mock('openai');

// Mock the logger
jest.mock('../../services/logger', () => ({
  logHelpers: {
    logAiOperation: jest.fn()
  }
}));

// Mock config
jest.mock('../../config/config', () => ({
  openRouter: {
    apiKey: 'test-api-key',
    baseURL: 'https://test-api.com',
    models: {
      analysis: 'gpt-3.5-turbo',
      matching: 'gpt-3.5-turbo',
      summarization: 'gpt-3.5-turbo',
      translation: 'gpt-3.5-turbo'
    },
    defaultModel: 'gpt-3.5-turbo',
    maxTokens: 1000,
    temperature: 0.7
  }
}));

const OpenAI = require('openai');
const aiService = require('../../services/aiService');

describe('AIService - Auto Application Methods', () => {
  let mockOpenAI;
  let mockCompletion;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock OpenAI completion response
    mockCompletion = {
      choices: [{
        message: {
          content: 'Generated application content'
        }
      }]
    };

    // Mock OpenAI instance
    mockOpenAI = {
      chat: {
        completions: {
          create: jest.fn().mockResolvedValue(mockCompletion)
        }
      }
    };

    // Mock OpenAI constructor
    OpenAI.mockImplementation(() => mockOpenAI);
    
    // Replace the aiService's openai instance with our mock
    aiService.openai = mockOpenAI;
  });

  describe('generateAutoApplicationLetter', () => {
    const mockListing = {
      id: 'listing123',
      title: 'Beautiful Apartment in Amsterdam',
      location: 'Amsterdam Centrum',
      price: '€1,500',
      size: '75m²',
      rooms: '2 rooms',
      propertyType: 'Apartment',
      description: 'Modern apartment with great views'
    };

    const mockUserProfile = {
      name: 'John Doe',
      personalInfo: {
        fullName: 'John Doe',
        occupation: 'Software Engineer',
        employer: 'Tech Company',
        monthlyIncome: 4000,
        nationality: 'Dutch',
        dateOfBirth: '1990-01-01',
        moveInDate: '2024-03-01',
        leaseDuration: 12,
        numberOfOccupants: 1,
        hasGuarantor: false
      }
    };

    const mockSettings = {
      applicationTemplate: 'professional'
    };

    it('should generate professional application letter in Dutch', async () => {
      mockCompletion.choices[0].message.content = 'Geachte verhuurder, ik ben zeer geïnteresseerd in uw prachtige appartement...';

      const result = await aiService.generateAutoApplicationLetter(
        mockListing,
        mockUserProfile,
        mockSettings,
        'dutch'
      );

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: expect.stringContaining('expert rental application writer')
          },
          {
            role: 'user',
            content: expect.stringContaining('Beautiful Apartment in Amsterdam')
          }
        ],
        max_tokens: 1200,
        temperature: 0.7
      });

      expect(result).toEqual({
        subject: 'Rental Application - Beautiful Apartment in Amsterdam',
        message: 'Geachte verhuurder, ik ben zeer geïnteresseerd in uw prachtige appartement...',
        template: 'professional',
        language: 'dutch',
        personalizedElements: expect.any(Array),
        generatedAt: expect.any(String),
        wordCount: expect.any(Number),
        estimatedReadTime: expect.any(Number)
      });
    });

    it('should generate casual application letter in English', async () => {
      mockCompletion.choices[0].message.content = 'Dear landlord, I am very interested in your beautiful apartment...';

      const casualSettings = { applicationTemplate: 'casual' };
      const result = await aiService.generateAutoApplicationLetter(
        mockListing,
        mockUserProfile,
        casualSettings,
        'english'
      );

      expect(result.template).toBe('casual');
      expect(result.language).toBe('english');
      expect(result.message).toContain('Dear landlord');
    });

    it('should generate student template application', async () => {
      const studentProfile = {
        ...mockUserProfile,
        personalInfo: {
          ...mockUserProfile.personalInfo,
          occupation: 'Student',
          employer: 'University of Amsterdam'
        }
      };

      const studentSettings = { applicationTemplate: 'student' };
      
      await aiService.generateAutoApplicationLetter(
        mockListing,
        studentProfile,
        studentSettings,
        'dutch'
      );

      const systemPrompt = mockOpenAI.chat.completions.create.mock.calls[0][0].messages[0].content;
      expect(systemPrompt).toContain('student');
    });

    it('should generate expat template application', async () => {
      const expatProfile = {
        ...mockUserProfile,
        personalInfo: {
          ...mockUserProfile.personalInfo,
          nationality: 'American'
        }
      };

      const expatSettings = { applicationTemplate: 'expat' };
      
      await aiService.generateAutoApplicationLetter(
        mockListing,
        expatProfile,
        expatSettings,
        'english'
      );

      const systemPrompt = mockOpenAI.chat.completions.create.mock.calls[0][0].messages[0].content;
      expect(systemPrompt).toContain('expat');
    });

    it('should handle missing personal information gracefully', async () => {
      const incompleteProfile = {
        name: 'Jane Doe'
        // Missing personalInfo
      };

      const result = await aiService.generateAutoApplicationLetter(
        mockListing,
        incompleteProfile,
        mockSettings,
        'dutch'
      );

      expect(result).toBeDefined();
      expect(result.subject).toContain(mockListing.title);
    });

    it('should handle API errors gracefully', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('API Error'));

      await expect(
        aiService.generateAutoApplicationLetter(mockListing, mockUserProfile, mockSettings)
      ).rejects.toThrow('Auto-application generation failed: API Error');
    });

    it('should calculate age correctly', async () => {
      const profileWithAge = {
        ...mockUserProfile,
        personalInfo: {
          ...mockUserProfile.personalInfo,
          dateOfBirth: '1990-06-15'
        }
      };

      await aiService.generateAutoApplicationLetter(
        mockListing,
        profileWithAge,
        mockSettings
      );

      const userPrompt = mockOpenAI.chat.completions.create.mock.calls[0][0].messages[1].content;
      expect(userPrompt).toMatch(/Age: \d+/);
    });
  });

  describe('generatePropertySpecificContent', () => {
    const mockListing = {
      id: 'listing123',
      title: 'Modern Loft',
      location: 'Amsterdam Noord',
      description: 'Spacious loft with high ceilings and natural light'
    };

    const mockUserProfile = {
      name: 'Alice Smith',
      personalInfo: {
        fullName: 'Alice Smith',
        occupation: 'Graphic Designer'
      }
    };

    it('should generate property-specific customization', async () => {
      const mockJsonResponse = {
        propertyConnection: 'The modern design appeals to my creative work',
        locationBenefits: 'Noord is perfect for my lifestyle',
        featureHighlights: ['High ceilings', 'Natural light'],
        personalizedReasons: ['Great for creative work'],
        futureVision: 'I can see myself working and living here'
      };

      mockCompletion.choices[0].message.content = JSON.stringify(mockJsonResponse);

      const result = await aiService.generatePropertySpecificContent(
        mockListing,
        mockUserProfile,
        ['high ceilings', 'natural light'],
        'english'
      );

      expect(result).toEqual({
        ...mockJsonResponse,
        generatedAt: expect.any(String),
        language: 'english'
      });

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: expect.stringContaining('property-specific content')
          },
          {
            role: 'user',
            content: expect.stringContaining('Modern Loft')
          }
        ],
        max_tokens: 800,
        temperature: 0.6
      });
    });

    it('should handle empty property features', async () => {
      const mockJsonResponse = {
        propertyConnection: 'General appeal',
        locationBenefits: 'Good location',
        featureHighlights: [],
        personalizedReasons: [],
        futureVision: 'Suitable living space'
      };

      mockCompletion.choices[0].message.content = JSON.stringify(mockJsonResponse);

      const result = await aiService.generatePropertySpecificContent(
        mockListing,
        mockUserProfile,
        [],
        'dutch'
      );

      expect(result.language).toBe('dutch');
      expect(result.featureHighlights).toEqual([]);
    });

    it('should handle JSON parsing errors', async () => {
      mockCompletion.choices[0].message.content = 'Invalid JSON response';

      await expect(
        aiService.generatePropertySpecificContent(mockListing, mockUserProfile, [])
      ).rejects.toThrow('Property customization failed');
    });
  });

  describe('generateTemplateBasedContent', () => {
    const mockUserProfile = {
      name: 'Bob Johnson',
      personalInfo: {
        fullName: 'Bob Johnson',
        occupation: 'Teacher'
      }
    };

    const mockListing = {
      title: 'Family Home',
      location: 'Utrecht'
    };

    it('should generate professional template content', async () => {
      mockCompletion.choices[0].message.content = 'Professional application content...';

      const result = await aiService.generateTemplateBasedContent(
        'professional',
        mockUserProfile,
        mockListing,
        'dutch'
      );

      expect(result).toEqual({
        content: 'Professional application content...',
        templateType: 'professional',
        language: 'dutch',
        generatedAt: expect.any(String),
        personalizedElements: expect.any(Array)
      });
    });

    it('should generate casual template content', async () => {
      await aiService.generateTemplateBasedContent(
        'casual',
        mockUserProfile,
        mockListing,
        'english'
      );

      const systemPrompt = mockOpenAI.chat.completions.create.mock.calls[0][0].messages[0].content;
      expect(systemPrompt).toContain('friendly, approachable');
    });

    it('should generate student template content', async () => {
      await aiService.generateTemplateBasedContent(
        'student',
        mockUserProfile,
        mockListing,
        'dutch'
      );

      const systemPrompt = mockOpenAI.chat.completions.create.mock.calls[0][0].messages[0].content;
      expect(systemPrompt).toContain('academic dedication');
    });

    it('should generate expat template content', async () => {
      await aiService.generateTemplateBasedContent(
        'expat',
        mockUserProfile,
        mockListing,
        'english'
      );

      const systemPrompt = mockOpenAI.chat.completions.create.mock.calls[0][0].messages[0].content;
      expect(systemPrompt).toContain('international experience');
    });
  });

  describe('enhanceWithPropertyDetails', () => {
    const baseContent = 'I am interested in renting your property.';
    const mockListing = {
      id: 'listing456',
      title: 'Garden Apartment',
      location: 'The Hague',
      description: 'Apartment with private garden'
    };

    const mockUserProfile = {
      name: 'Carol White',
      personalInfo: {
        fullName: 'Carol White',
        occupation: 'Nurse'
      }
    };

    it('should enhance content with property details', async () => {
      const enhancedContent = 'I am very interested in renting your beautiful Garden Apartment in The Hague, especially because of the private garden which would be perfect for my lifestyle as a nurse.';
      mockCompletion.choices[0].message.content = enhancedContent;

      const result = await aiService.enhanceWithPropertyDetails(
        baseContent,
        mockListing,
        mockUserProfile,
        'english'
      );

      expect(result).toEqual({
        originalContent: baseContent,
        enhancedContent: enhancedContent,
        enhancements: expect.any(Array),
        language: 'english',
        enhancedAt: expect.any(String)
      });

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: expect.stringContaining('personalizing rental applications')
          },
          {
            role: 'user',
            content: expect.stringContaining(baseContent)
          }
        ],
        max_tokens: 1000,
        temperature: 0.6
      });
    });

    it('should identify content enhancements', async () => {
      const enhancedContent = 'I am very interested in renting your beautiful Garden Apartment in The Hague. The private garden would be perfect for my lifestyle. I can offer €1,500 monthly rent.';
      mockCompletion.choices[0].message.content = enhancedContent;

      const result = await aiService.enhanceWithPropertyDetails(
        baseContent,
        mockListing,
        mockUserProfile
      );

      expect(result.enhancements).toContain('Content expanded with additional details');
      expect(result.enhancements).toContain('Financial details added');
      expect(result.enhancements).toContain('Additional sentences for clarity');
    });
  });

  describe('generateMultiLanguageContent', () => {
    const mockListing = {
      title: 'Studio Apartment',
      location: 'Rotterdam'
    };

    const mockUserProfile = {
      name: 'David Brown',
      personalInfo: {
        fullName: 'David Brown'
      }
    };

    it('should generate content in multiple languages', async () => {
      // Mock different responses for different languages
      let callCount = 0;
      mockOpenAI.chat.completions.create.mockImplementation(() => {
        callCount++;
        return Promise.resolve({
          choices: [{
            message: {
              content: callCount === 1 ? 'Dutch content...' : 'English content...'
            }
          }]
        });
      });

      const result = await aiService.generateMultiLanguageContent(
        mockListing,
        mockUserProfile,
        'professional',
        ['dutch', 'english']
      );

      expect(result).toEqual({
        languages: {
          dutch: expect.objectContaining({
            message: 'Dutch content...',
            language: 'dutch'
          }),
          english: expect.objectContaining({
            message: 'English content...',
            language: 'english'
          })
        },
        primaryLanguage: 'dutch',
        generatedAt: expect.any(String),
        templateType: 'professional'
      });

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(2);
    });

    it('should handle single language generation', async () => {
      const result = await aiService.generateMultiLanguageContent(
        mockListing,
        mockUserProfile,
        'casual',
        ['english']
      );

      expect(result.languages).toHaveProperty('english');
      expect(result.primaryLanguage).toBe('english');
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledTimes(1);
    });

    it('should handle generation errors gracefully', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('API Error'));

      await expect(
        aiService.generateMultiLanguageContent(mockListing, mockUserProfile, 'professional')
      ).rejects.toThrow('Multi-language generation failed');
    });
  });

  describe('Helper Methods', () => {
    describe('calculateAge', () => {
      it('should calculate age correctly', () => {
        const birthDate = '1990-06-15';
        const age = aiService.calculateAge(birthDate);
        
        // Age should be around 33-34 depending on current date
        expect(age).toBeGreaterThan(30);
        expect(age).toBeLessThan(40);
      });

      it('should handle invalid date', () => {
        const age = aiService.calculateAge(null);
        expect(age).toBe('Not specified');
      });

      it('should handle future date', () => {
        const futureDate = '2030-01-01';
        const age = aiService.calculateAge(futureDate);
        expect(age).toBeLessThan(0);
      });
    });

    describe('extractPersonalizedElements', () => {
      it('should identify personalized elements', () => {
        const content = 'I am John Doe, a Software Engineer interested in the Beautiful Apartment in Amsterdam.';
        const context = {
          title: 'Beautiful Apartment',
          location: 'Amsterdam',
          personalInfo: {
            fullName: 'John Doe',
            occupation: 'Software Engineer'
          }
        };

        const elements = aiService.extractPersonalizedElements(content, context);
        
        expect(elements).toContain('Property title mentioned');
        expect(elements).toContain('Location referenced');
        expect(elements).toContain('Occupation highlighted');
        expect(elements).toContain('Personal name included');
      });

      it('should handle missing context', () => {
        const content = 'Generic application content';
        const context = {};

        const elements = aiService.extractPersonalizedElements(content, context);
        expect(elements).toEqual([]);
      });
    });

    describe('identifyEnhancements', () => {
      it('should identify content expansion', () => {
        const original = 'Short content.';
        const enhanced = 'This is much longer enhanced content with additional details and information.';

        const enhancements = aiService.identifyEnhancements(original, enhanced);
        expect(enhancements).toContain('Content expanded with additional details');
      });

      it('should identify financial details', () => {
        const original = 'I am interested.';
        const enhanced = 'I am interested and can pay €1,500 monthly.';

        const enhancements = aiService.identifyEnhancements(original, enhanced);
        expect(enhancements).toContain('Financial details added');
      });

      it('should identify additional sentences', () => {
        const original = 'One sentence.';
        const enhanced = 'One sentence. Another sentence. Third sentence.';

        const enhancements = aiService.identifyEnhancements(original, enhanced);
        expect(enhancements).toContain('Additional sentences for clarity');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle OpenAI API errors', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('Rate limit exceeded'));

      await expect(
        aiService.generateAutoApplicationLetter({}, {}, {})
      ).rejects.toThrow('Auto-application generation failed: Rate limit exceeded');
    });

    it('should handle network errors', async () => {
      mockOpenAI.chat.completions.create.mockRejectedValue(new Error('Network error'));

      await expect(
        aiService.generateTemplateBasedContent('professional', {}, {})
      ).rejects.toThrow('Template generation failed: Network error');
    });

    it('should handle invalid JSON responses', async () => {
      mockCompletion.choices[0].message.content = 'Not valid JSON';

      await expect(
        aiService.generatePropertySpecificContent({}, {}, [])
      ).rejects.toThrow('Property customization failed');
    });
  });

  describe('Integration with Existing Methods', () => {
    it('should maintain compatibility with existing generateApplicationMessage', async () => {
      const listing = { title: 'Test Property' };
      const userProfile = { name: 'Test User' };

      await aiService.generateApplicationMessage(listing, userProfile, 'professional');

      expect(mockOpenAI.chat.completions.create).toHaveBeenCalled();
    });

    it('should use consistent logging across all methods', async () => {
      const { logHelpers } = require('../../services/logger');

      await aiService.generateAutoApplicationLetter({}, {}, {});
      
      expect(logHelpers.logAiOperation).toHaveBeenCalledWith(
        'auto_application_generation',
        'success',
        expect.any(String)
      );
    });
  });
});