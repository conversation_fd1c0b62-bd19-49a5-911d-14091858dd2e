const LearningOptimizationService = require('../../services/learningOptimizationService');
const ApplicationResult = require('../../models/ApplicationResult');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');

// Mock the models
jest.mock('../../models/ApplicationResult');
jest.mock('../../models/AutoApplicationSettings');
jest.mock('../../services/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn()
  }
}));

describe('LearningOptimizationService', () => {
  let service;
  let mockUserId;

  beforeEach(() => {
    service = new LearningOptimizationService();
    mockUserId = 'user123';
    jest.clearAllMocks();
  });

  describe('trackSuccessRates', () => {
    it('should track success rates and patterns for a user', async () => {
      const mockApplications = [
        {
          userId: mockUserId,
          status: 'submitted',
          response: { success: true, message: 'Accepted' },
          createdAt: new Date(),
          listingId: { propertyType: 'apartment', location: { city: 'Amsterdam' }, price: 1500 }
        },
        {
          userId: mockUserId,
          status: 'submitted',
          response: { success: false, message: 'Rejected' },
          createdAt: new Date(),
          listingId: { propertyType: 'house', location: { city: 'Utrecht' }, price: 2000 }
        },
        {
          userId: mockUserId,
          status: 'failed',
          createdAt: new Date(),
          listingId: { propertyType: 'apartment', location: { city: 'Amsterdam' }, price: 1200 }
        }
      ];

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockApplications)
      });

      const result = await service.trackSuccessRates(mockUserId, '30d');

      expect(result).toHaveProperty('userId', mockUserId);
      expect(result).toHaveProperty('totalApplications', 3);
      expect(result).toHaveProperty('successfulApplications', 1);
      expect(result).toHaveProperty('successRate', 1/3);
      expect(result).toHaveProperty('responseRate', 2/3);
      expect(result).toHaveProperty('patterns');
      expect(ApplicationResult.find).toHaveBeenCalledWith({
        userId: mockUserId,
        createdAt: { $gte: expect.any(Date) }
      });
    });

    it('should handle empty application history', async () => {
      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue([])
      });

      const result = await service.trackSuccessRates(mockUserId, '30d');

      expect(result.totalApplications).toBe(0);
      expect(result.successRate).toBe(0);
      expect(result.responseRate).toBe(0);
    });

    it('should cache results for performance', async () => {
      const mockApplications = [
        {
          userId: mockUserId,
          status: 'submitted',
          response: { success: true },
          createdAt: new Date(),
          listingId: { propertyType: 'apartment' }
        }
      ];

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockResolvedValue(mockApplications)
      });

      // First call
      const result1 = await service.trackSuccessRates(mockUserId, '30d');
      
      // Second call should use cache
      const result2 = await service.trackSuccessRates(mockUserId, '30d');

      // Both results should be the same (from cache)
      expect(result1.userId).toBe(result2.userId);
      expect(result1.totalApplications).toBe(result2.totalApplications);
    });
  });

  describe('analyzeSuccessPatterns', () => {
    it('should analyze patterns in successful vs failed applications', async () => {
      const applications = [
        {
          status: 'submitted',
          response: { success: true },
          createdAt: new Date('2024-01-15T10:00:00Z'),
          listingId: { propertyType: 'apartment', location: { city: 'Amsterdam' }, price: 1500 },
          formData: { template: 'professional' }
        },
        {
          status: 'failed',
          createdAt: new Date('2024-01-15T14:00:00Z'),
          listingId: { propertyType: 'house', location: { city: 'Utrecht' }, price: 2000 },
          formData: { template: 'casual' }
        }
      ];

      const patterns = await service.analyzeSuccessPatterns(applications);

      expect(patterns).toHaveProperty('propertyTypes');
      expect(patterns).toHaveProperty('priceRanges');
      expect(patterns).toHaveProperty('locations');
      expect(patterns).toHaveProperty('applicationTiming');
      expect(patterns).toHaveProperty('templateTypes');
      expect(patterns).toHaveProperty('propertyFeatures');
    });

    it('should handle applications without listing data', async () => {
      const applications = [
        {
          status: 'submitted',
          response: { success: true },
          createdAt: new Date(),
          listingId: null
        }
      ];

      const patterns = await service.analyzeSuccessPatterns(applications);

      expect(patterns).toBeDefined();
      expect(patterns.propertyTypes).toEqual({});
      expect(patterns.locations).toEqual({});
    });
  });

  describe('generateMLInsights', () => {
    it('should generate ML insights for users with sufficient data', async () => {
      const mockApplications = Array.from({ length: 20 }, (_, i) => ({
        userId: mockUserId,
        status: i % 3 === 0 ? 'submitted' : 'failed',
        response: i % 3 === 0 ? { success: true } : null,
        createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        listingId: { 
          propertyType: 'apartment', 
          price: 1500 + i * 100,
          location: { city: 'Amsterdam' }
        },
        generatedContent: { message: 'Test message '.repeat(10 + i) }
      }));

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue(mockApplications)
          })
        })
      });

      const insights = await service.generateMLInsights(mockUserId);

      expect(insights).toHaveProperty('insights');
      expect(insights.insights).toHaveProperty('optimalTiming');
      expect(insights.insights).toHaveProperty('contentOptimization');
      expect(insights.insights).toHaveProperty('marketTrends');
      expect(insights.insights).toHaveProperty('personalizedRecommendations');
      expect(insights).toHaveProperty('confidence');
      expect(insights.confidence).toBeGreaterThan(0);
    });

    it('should return low confidence for users with insufficient data', async () => {
      const mockApplications = [
        {
          userId: mockUserId,
          status: 'submitted',
          response: { success: true },
          createdAt: new Date(),
          listingId: { propertyType: 'apartment' }
        }
      ];

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue(mockApplications)
          })
        })
      });

      const insights = await service.generateMLInsights(mockUserId);

      expect(insights.insights).toEqual([]);
      expect(insights.recommendations).toContain('Need more application data for meaningful insights');
      expect(insights.confidence).toBe(0);
    });

    it('should use cached insights when available', async () => {
      const mockApplications = Array.from({ length: 10 }, () => ({
        userId: mockUserId,
        status: 'submitted',
        response: { success: true },
        createdAt: new Date(),
        listingId: { propertyType: 'apartment' }
      }));

      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue(mockApplications)
          })
        })
      });

      // First call
      await service.generateMLInsights(mockUserId);
      
      // Second call should use cache
      await service.generateMLInsights(mockUserId);

      expect(ApplicationResult.find).toHaveBeenCalledTimes(1);
    });
  });

  describe('recommendOptimizations', () => {
    it('should recommend template changes based on performance', async () => {
      const mockSettings = {
        userId: mockUserId,
        settings: { applicationTemplate: 'casual' }
      };

      const mockApplications = [
        {
          status: 'submitted',
          response: { success: true },
          formData: { template: 'professional' },
          listingId: { propertyType: 'apartment' }
        },
        {
          status: 'submitted',
          response: { success: true },
          formData: { template: 'professional' },
          listingId: { propertyType: 'house' }
        },
        {
          status: 'submitted',
          response: { success: true },
          formData: { template: 'professional' },
          listingId: { propertyType: 'studio' }
        },
        {
          status: 'failed',
          formData: { template: 'casual' },
          listingId: { propertyType: 'apartment' }
        }
      ];

      AutoApplicationSettings.findOne.mockResolvedValue(mockSettings);
      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue(mockApplications)
          })
        })
      });

      const recommendations = await service.recommendOptimizations(mockUserId);

      expect(recommendations).toHaveProperty('recommendations');
      expect(recommendations.recommendations.length).toBeGreaterThan(0);
      
      // Check if template change recommendation exists
      const templateRec = recommendations.recommendations.find(r => r.type === 'template_change');
      if (templateRec) {
        expect(templateRec.current).toBe('casual');
        expect(templateRec.recommended).toBe('professional');
      }
    });

    it('should recommend timing optimizations', async () => {
      const mockSettings = {
        userId: mockUserId,
        settings: { applicationTemplate: 'professional' }
      };

      const mockApplications = Array.from({ length: 10 }, (_, i) => ({
        status: i < 7 ? 'submitted' : 'failed',
        response: i < 7 ? { success: true } : null,
        createdAt: new Date(`2024-01-15T${10 + (i % 3)}:00:00Z`),
        listingId: { propertyType: 'apartment' },
        formData: { template: 'professional' }
      }));

      AutoApplicationSettings.findOne.mockResolvedValue(mockSettings);
      ApplicationResult.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          sort: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue(mockApplications)
          })
        })
      });

      const recommendations = await service.recommendOptimizations(mockUserId);

      expect(recommendations.recommendations).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'timing_optimization'
          })
        ])
      );
    });

    it('should handle users without settings', async () => {
      AutoApplicationSettings.findOne.mockResolvedValue(null);

      await expect(service.recommendOptimizations(mockUserId))
        .rejects.toThrow('User settings not found');
    });
  });

  describe('analyzeMarketTrends', () => {
    it('should analyze market trends and generate adaptive strategies', async () => {
      const now = new Date();
      const applications = Array.from({ length: 20 }, (_, i) => ({
        createdAt: new Date(now.getTime() - i * 24 * 60 * 60 * 1000),
        status: i % 4 === 0 ? 'submitted' : 'failed',
        response: i % 4 === 0 ? { success: true, message: 'Response' } : null,
        listingId: { 
          price: 1500 + i * 50,
          propertyType: 'apartment'
        }
      }));

      const trends = await service.analyzeMarketTrends(applications);

      expect(trends).toHaveProperty('trends');
      expect(trends.trends).toHaveProperty('competitionLevel');
      expect(trends.trends).toHaveProperty('priceMovement');
      expect(trends.trends).toHaveProperty('responseTime');
      expect(trends.trends).toHaveProperty('successRateChange');
      expect(trends.trends).toHaveProperty('seasonalPatterns');
      expect(trends).toHaveProperty('adaptiveStrategies');
      expect(trends).toHaveProperty('marketCondition');
    });

    it('should handle empty application data', async () => {
      const trends = await service.analyzeMarketTrends([]);

      expect(trends.trends.competitionLevel.current).toBeNaN();
      expect(trends.trends.priceMovement.current).toBeNaN();
      expect(['challenging', 'neutral']).toContain(trends.marketCondition);
    });
  });

  describe('analyzeByCategory', () => {
    it('should analyze success patterns by property type', () => {
      const successful = [
        { listingId: { propertyType: 'apartment' } },
        { listingId: { propertyType: 'apartment' } },
        { listingId: { propertyType: 'house' } }
      ];

      const failed = [
        { listingId: { propertyType: 'apartment' } },
        { listingId: { propertyType: 'house' } },
        { listingId: { propertyType: 'house' } }
      ];

      const analysis = service.analyzeByCategory(successful, failed, 'propertyType');

      expect(analysis).toHaveProperty('apartment');
      expect(analysis).toHaveProperty('house');
      expect(analysis.apartment.successRate).toBe(2/3);
      expect(analysis.house.successRate).toBe(1/3);
    });

    it('should handle missing listing data', () => {
      const successful = [{ listingId: null }];
      const failed = [{ listingId: { propertyType: 'apartment' } }];

      const analysis = service.analyzeByCategory(successful, failed, 'propertyType');

      expect(analysis).toHaveProperty('apartment');
      expect(analysis.apartment.successRate).toBe(0);
    });
  });

  describe('analyzePriceRanges', () => {
    it('should analyze success patterns by price ranges', () => {
      const successful = [
        { listingId: { price: 800 } },
        { listingId: { price: 1200 } },
        { listingId: { price: 1800 } }
      ];

      const failed = [
        { listingId: { price: 900 } },
        { listingId: { price: 2200 } }
      ];

      const analysis = service.analyzePriceRanges(successful, failed);

      expect(analysis).toHaveProperty('€0-1000');
      expect(analysis).toHaveProperty('€1000-1500');
      expect(analysis).toHaveProperty('€1500-2000');
      expect(analysis).toHaveProperty('€2000-2500');
    });
  });

  describe('analyzeApplicationTiming', () => {
    it('should analyze success patterns by hour of day', () => {
      const successful = [
        { createdAt: new Date('2024-01-15T12:00:00Z') },
        { createdAt: new Date('2024-01-15T12:30:00Z') },
        { createdAt: new Date('2024-01-15T16:00:00Z') }
      ];

      const failed = [
        { createdAt: new Date('2024-01-15T12:15:00Z') },
        { createdAt: new Date('2024-01-15T18:00:00Z') }
      ];

      const analysis = service.analyzeApplicationTiming(successful, failed);

      // Check that analysis has the correct structure
      expect(typeof analysis).toBe('object');
      expect(Object.keys(analysis).length).toBeGreaterThan(0);
      
      // Check that each hour has the correct properties
      Object.values(analysis).forEach(hourData => {
        expect(hourData).toHaveProperty('successRate');
        expect(hourData).toHaveProperty('totalApplications');
        expect(hourData).toHaveProperty('successCount');
        expect(hourData).toHaveProperty('failCount');
        expect(typeof hourData.successRate).toBe('number');
        expect(hourData.successRate).toBeGreaterThanOrEqual(0);
        expect(hourData.successRate).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('analyzeTemplatePerformance', () => {
    it('should analyze template performance and identify best performing', () => {
      const successful = [
        { formData: { template: 'professional' } },
        { formData: { template: 'professional' } },
        { formData: { template: 'casual' } }
      ];

      const failed = [
        { formData: { template: 'professional' } },
        { formData: { template: 'casual' } },
        { formData: { template: 'casual' } }
      ];

      const analysis = service.analyzeTemplatePerformance(successful, failed);

      expect(analysis).toHaveProperty('professional');
      expect(analysis).toHaveProperty('casual');
      expect(analysis).toHaveProperty('bestPerforming');
      expect(analysis.professional.successRate).toBe(2/3);
      expect(analysis.casual.successRate).toBe(1/3);
      expect(analysis.bestPerforming).toBe('professional');
    });

    it('should require minimum sample size for best template recommendation', () => {
      const successful = [{ formData: { template: 'professional' } }];
      const failed = [];

      const analysis = service.analyzeTemplatePerformance(successful, failed);

      expect(analysis.bestPerforming).toBeNull();
    });
  });

  describe('calculateConfidenceScore', () => {
    it('should return appropriate confidence based on sample size', () => {
      expect(service.calculateConfidenceScore(Array(5).fill({}))).toBe(0.3);
      expect(service.calculateConfidenceScore(Array(20).fill({}))).toBe(0.6);
      expect(service.calculateConfidenceScore(Array(40).fill({}))).toBe(0.8);
      expect(service.calculateConfidenceScore(Array(60).fill({}))).toBe(0.9);
    });
  });

  describe('getStartDate', () => {
    it('should return correct start dates for different timeframes', () => {
      const now = new Date();
      
      const sevenDays = service.getStartDate('7d');
      const thirtyDays = service.getStartDate('30d');
      const ninetyDays = service.getStartDate('90d');
      const defaultDays = service.getStartDate('invalid');

      expect(sevenDays.getTime()).toBeCloseTo(now.getTime() - 7 * 24 * 60 * 60 * 1000, -10000);
      expect(thirtyDays.getTime()).toBeCloseTo(now.getTime() - 30 * 24 * 60 * 60 * 1000, -10000);
      expect(ninetyDays.getTime()).toBeCloseTo(now.getTime() - 90 * 24 * 60 * 60 * 1000, -10000);
      expect(defaultDays.getTime()).toBeCloseTo(now.getTime() - 30 * 24 * 60 * 60 * 1000, -10000);
    });
  });

  describe('cache management', () => {
    it('should clear cache when requested', () => {
      service.analysisCache.set('test', { data: 'test', timestamp: Date.now() });
      expect(service.analysisCache.size).toBe(1);
      
      service.clearCache();
      expect(service.analysisCache.size).toBe(0);
    });

    it('should return cached analysis when available and not expired', () => {
      const testData = { test: 'data' };
      service.analysisCache.set('test_key', {
        data: testData,
        timestamp: Date.now()
      });

      const result = service.getCachedAnalysis('test_key');
      expect(result).toEqual(testData);
    });

    it('should return null for expired cache', () => {
      const testData = { test: 'data' };
      service.analysisCache.set('test_key', {
        data: testData,
        timestamp: Date.now() - service.cacheExpiry - 1000
      });

      const result = service.getCachedAnalysis('test_key');
      expect(result).toBeNull();
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      ApplicationResult.find.mockImplementation(() => {
        throw new Error('Database error');
      });

      await expect(service.trackSuccessRates(mockUserId))
        .rejects.toThrow('Database error');
    });

    it('should handle missing user settings in recommendations', async () => {
      AutoApplicationSettings.findOne.mockResolvedValue(null);

      await expect(service.recommendOptimizations(mockUserId))
        .rejects.toThrow('User settings not found');
    });
  });
});