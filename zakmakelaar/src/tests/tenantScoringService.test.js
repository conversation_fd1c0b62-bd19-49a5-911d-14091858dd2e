const tenantScoringService = require('../services/tenantScoringService');
const User = require('../models/User');
const cacheService = require('../services/cacheService');

// Mock dependencies
jest.mock('../models/User');
jest.mock('../services/cacheService');
jest.mock('../services/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn()
}));

describe('TenantScoringService', () => {
  let mockUser;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock user data
    mockUser = {
      _id: '507f1f77bcf86cd799439011',
      email: '<EMAIL>',
      fullName: '<PERSON>',
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        employment: {
          occupation: 'Software Engineer',
          employmentType: 'full-time',
          contractType: 'permanent',
          employer: 'Tech Company',
          workLocation: 'Amsterdam',
          monthlyIncome: 4000,
          incomeVerified: true
        },
        rentalHistory: {
          previousAddresses: [
            {
              address: 'Damrak 1, Amsterdam',
              landlordName: '<PERSON>',
              landlordContact: '<EMAIL>',
              rentAmount: 1500,
              duration: '2 years',
              reasonForLeaving: 'Job relocation',
              reference: 'Good tenant'
            }
          ],
          evictions: false,
          paymentIssues: false,
          creditScore: 'good'
        }
      },
      documents: [
        {
          id: 'doc1',
          type: 'income_proof',
          verified: true
        },
        {
          id: 'doc2',
          type: 'employment_contract',
          verified: true
        }
      ],
      tenantScore: {
        overallScore: 0,
        components: {
          incomeStability: 0,
          rentalHistory: 0,
          creditworthiness: 0,
          employment: 0,
          references: 0
        },
        lastCalculated: null,
        verificationLevel: 'unverified'
      }
    };

    // Mock cache service
    cacheService.get.mockResolvedValue(null);
    cacheService.set.mockResolvedValue(true);
    cacheService.del.mockResolvedValue(true);
  });

  describe('calculateTenantScore', () => {
    it('should calculate tenant score correctly for a complete profile', async () => {
      User.findById.mockResolvedValue(mockUser);

      const result = await tenantScoringService.calculateTenantScore(mockUser._id);

      expect(result).toHaveProperty('overallScore');
      expect(result).toHaveProperty('components');
      expect(result).toHaveProperty('verificationLevel');
      expect(result).toHaveProperty('lastCalculated');
      expect(result.overallScore).toBeGreaterThan(0);
      expect(result.overallScore).toBeLessThanOrEqual(100);
    });

    it('should return cached score if available', async () => {
      const cachedScore = {
        overallScore: 85,
        components: {
          incomeStability: 90,
          rentalHistory: 80,
          creditworthiness: 85,
          employment: 95,
          references: 70
        },
        verificationLevel: 'verified',
        lastCalculated: new Date(),
        userId: mockUser._id
      };

      cacheService.get.mockResolvedValue(cachedScore);

      const result = await tenantScoringService.calculateTenantScore(mockUser._id);

      expect(result).toEqual(cachedScore);
      expect(User.findById).not.toHaveBeenCalled();
    });

    it('should throw error if user not found', async () => {
      User.findById.mockResolvedValue(null);

      await expect(tenantScoringService.calculateTenantScore('invalid_id'))
        .rejects.toThrow('User not found');
    });

    it('should handle missing employment data gracefully', async () => {
      const userWithoutEmployment = {
        ...mockUser,
        profile: {
          ...mockUser.profile,
          employment: null
        }
      };

      User.findById.mockResolvedValue(userWithoutEmployment);

      const result = await tenantScoringService.calculateTenantScore(mockUser._id);

      expect(result.components.incomeStability).toBe(0);
      expect(result.components.employment).toBe(0);
    });

    it('should handle missing rental history gracefully', async () => {
      const userWithoutRentalHistory = {
        ...mockUser,
        profile: {
          ...mockUser.profile,
          rentalHistory: null
        }
      };

      User.findById.mockResolvedValue(userWithoutRentalHistory);

      const result = await tenantScoringService.calculateTenantScore(mockUser._id);

      expect(result.components.rentalHistory).toBe(50); // Neutral score for no history
      expect(result.components.creditworthiness).toBe(50);
      expect(result.components.references).toBe(0);
    });
  });

  describe('updateScore', () => {
    it('should update score and clear cache', async () => {
      User.findById.mockResolvedValue(mockUser);
      User.findByIdAndUpdate.mockResolvedValue(mockUser);

      const result = await tenantScoringService.updateScore(mockUser._id, {});

      expect(cacheService.del).toHaveBeenCalledWith(`tenant_score:${mockUser._id}`);
      expect(User.findByIdAndUpdate).toHaveBeenCalled();
      expect(result).toHaveProperty('overallScore');
    });

    it('should handle update errors gracefully', async () => {
      User.findById.mockRejectedValue(new Error('Database error'));

      await expect(tenantScoringService.updateScore(mockUser._id, {}))
        .rejects.toThrow('Database error');
    });
  });

  describe('getScoreBreakdown', () => {
    it('should return detailed score breakdown', async () => {
      User.findById.mockResolvedValue(mockUser);

      const result = await tenantScoringService.getScoreBreakdown(mockUser._id);

      expect(result).toHaveProperty('breakdown');
      expect(result).toHaveProperty('grade');
      expect(result).toHaveProperty('recommendations');
      expect(result.breakdown).toHaveProperty('incomeStability');
      expect(result.breakdown).toHaveProperty('rentalHistory');
      expect(result.breakdown).toHaveProperty('creditworthiness');
      expect(result.breakdown).toHaveProperty('employment');
      expect(result.breakdown).toHaveProperty('references');
    });

    it('should include factors for each component', async () => {
      User.findById.mockResolvedValue(mockUser);

      const result = await tenantScoringService.getScoreBreakdown(mockUser._id);

      expect(result.breakdown.incomeStability).toHaveProperty('factors');
      expect(result.breakdown.rentalHistory).toHaveProperty('factors');
      expect(result.breakdown.creditworthiness).toHaveProperty('factors');
      expect(result.breakdown.employment).toHaveProperty('factors');
      expect(result.breakdown.references).toHaveProperty('factors');
    });
  });

  describe('verifyDocument', () => {
    it('should verify document and update score if relevant', async () => {
      const userWithDocuments = {
        ...mockUser,
        documents: [
          {
            id: 'doc1',
            type: 'income_proof',
            verified: false
          }
        ],
        save: jest.fn().mockResolvedValue(true)
      };

      User.findById.mockResolvedValue(userWithDocuments);

      const result = await tenantScoringService.verifyDocument(mockUser._id, 'doc1');

      expect(userWithDocuments.save).toHaveBeenCalled();
      expect(result).toHaveProperty('overallScore');
    });

    it('should not update score for non-relevant documents', async () => {
      const userWithDocuments = {
        ...mockUser,
        documents: [
          {
            id: 'doc1',
            type: 'id_document',
            verified: false
          }
        ],
        save: jest.fn().mockResolvedValue(true)
      };

      User.findById.mockResolvedValue(userWithDocuments);

      const result = await tenantScoringService.verifyDocument(mockUser._id, 'doc1');

      expect(userWithDocuments.save).toHaveBeenCalled();
      expect(result).toBeNull();
    });

    it('should throw error if document not found', async () => {
      User.findById.mockResolvedValue(mockUser);

      await expect(tenantScoringService.verifyDocument(mockUser._id, 'invalid_doc'))
        .rejects.toThrow('Document not found');
    });
  });

  describe('generateScoreReport', () => {
    it('should generate comprehensive score report', async () => {
      User.findById.mockResolvedValue(mockUser);

      const result = await tenantScoringService.generateScoreReport(mockUser._id);

      expect(result).toHaveProperty('userId');
      expect(result).toHaveProperty('userName');
      expect(result).toHaveProperty('generatedAt');
      expect(result).toHaveProperty('score');
      expect(result).toHaveProperty('grade');
      expect(result).toHaveProperty('components');
      expect(result).toHaveProperty('recommendations');
      expect(result).toHaveProperty('summary');
      expect(result.summary).toHaveProperty('strengths');
      expect(result.summary).toHaveProperty('weaknesses');
      expect(result.summary).toHaveProperty('improvementAreas');
    });
  });

  describe('Score calculation algorithms', () => {
    describe('Income Stability', () => {
      it('should give high score for full-time permanent employment with verified income', async () => {
        const userWithGoodEmployment = {
          ...mockUser,
          profile: {
            ...mockUser.profile,
            employment: {
              occupation: 'Software Engineer',
              employmentType: 'full-time',
              contractType: 'permanent',
              incomeVerified: true
            }
          }
        };

        User.findById.mockResolvedValue(userWithGoodEmployment);

        const result = await tenantScoringService.calculateTenantScore(mockUser._id);

        expect(result.components.incomeStability).toBeGreaterThan(80);
      });

      it('should give lower score for unemployed status', async () => {
        const userUnemployed = {
          ...mockUser,
          profile: {
            ...mockUser.profile,
            employment: {
              occupation: null,
              employmentType: 'unemployed',
              contractType: null,
              incomeVerified: false
            }
          }
        };

        User.findById.mockResolvedValue(userUnemployed);

        const result = await tenantScoringService.calculateTenantScore(mockUser._id);

        expect(result.components.incomeStability).toBeLessThan(30);
      });
    });

    describe('Rental History', () => {
      it('should give high score for good rental history', async () => {
        const userWithGoodHistory = {
          ...mockUser,
          profile: {
            ...mockUser.profile,
            rentalHistory: {
              previousAddresses: [
                { landlordName: 'John', reference: 'Good tenant' },
                { landlordName: 'Jane', reference: 'Excellent tenant' }
              ],
              evictions: false,
              paymentIssues: false,
              creditScore: 'excellent'
            }
          }
        };

        User.findById.mockResolvedValue(userWithGoodHistory);

        const result = await tenantScoringService.calculateTenantScore(mockUser._id);

        expect(result.components.rentalHistory).toBeGreaterThan(80);
      });

      it('should give low score for evictions and payment issues', async () => {
        const userWithBadHistory = {
          ...mockUser,
          profile: {
            ...mockUser.profile,
            rentalHistory: {
              previousAddresses: [],
              evictions: true,
              paymentIssues: true,
              creditScore: 'poor'
            }
          }
        };

        User.findById.mockResolvedValue(userWithBadHistory);

        const result = await tenantScoringService.calculateTenantScore(mockUser._id);

        expect(result.components.rentalHistory).toBeLessThan(30);
      });
    });

    describe('Credit Worthiness', () => {
      it('should give high score for excellent credit', async () => {
        const userWithExcellentCredit = {
          ...mockUser,
          profile: {
            ...mockUser.profile,
            rentalHistory: {
              ...mockUser.profile.rentalHistory,
              creditScore: 'excellent',
              evictions: false,
              paymentIssues: false
            }
          }
        };

        User.findById.mockResolvedValue(userWithExcellentCredit);

        const result = await tenantScoringService.calculateTenantScore(mockUser._id);

        expect(result.components.creditworthiness).toBe(100);
      });

      it('should give low score for poor credit with issues', async () => {
        const userWithPoorCredit = {
          ...mockUser,
          profile: {
            ...mockUser.profile,
            rentalHistory: {
              ...mockUser.profile.rentalHistory,
              creditScore: 'poor',
              evictions: true,
              paymentIssues: true
            }
          }
        };

        User.findById.mockResolvedValue(userWithPoorCredit);

        const result = await tenantScoringService.calculateTenantScore(mockUser._id);

        expect(result.components.creditworthiness).toBeLessThan(20);
      });
    });

    describe('References', () => {
      it('should give high score for multiple references', async () => {
        const userWithManyReferences = {
          ...mockUser,
          profile: {
            ...mockUser.profile,
            rentalHistory: {
              ...mockUser.profile.rentalHistory,
              previousAddresses: [
                { landlordName: 'John', landlordContact: '<EMAIL>', reference: 'Great' },
                { landlordName: 'Jane', landlordContact: '<EMAIL>', reference: 'Excellent' },
                { landlordName: 'Bob', landlordContact: '<EMAIL>', reference: 'Perfect' }
              ]
            }
          }
        };

        User.findById.mockResolvedValue(userWithManyReferences);

        const result = await tenantScoringService.calculateTenantScore(mockUser._id);

        expect(result.components.references).toBeGreaterThan(80);
      });

      it('should give zero score for no references', async () => {
        const userWithNoReferences = {
          ...mockUser,
          profile: {
            ...mockUser.profile,
            rentalHistory: {
              ...mockUser.profile.rentalHistory,
              previousAddresses: []
            }
          }
        };

        User.findById.mockResolvedValue(userWithNoReferences);

        const result = await tenantScoringService.calculateTenantScore(mockUser._id);

        expect(result.components.references).toBe(0);
      });
    });
  });

  describe('Verification Level', () => {
    it('should return "verified" for high verification ratio', async () => {
      const userHighlyVerified = {
        ...mockUser,
        profile: {
          ...mockUser.profile,
          employment: {
            ...mockUser.profile.employment,
            incomeVerified: true
          }
        },
        documents: [
          { type: 'income_proof', verified: true },
          { type: 'employment_contract', verified: true },
          { type: 'bank_statement', verified: true }
        ]
      };

      User.findById.mockResolvedValue(userHighlyVerified);

      const result = await tenantScoringService.calculateTenantScore(mockUser._id);

      expect(result.verificationLevel).toBe('verified');
    });

    it('should return "unverified" for low verification ratio', async () => {
      const userUnverified = {
        ...mockUser,
        profile: {
          ...mockUser.profile,
          employment: {
            ...mockUser.profile.employment,
            incomeVerified: false
          }
        },
        documents: [
          { type: 'income_proof', verified: false },
          { type: 'employment_contract', verified: false }
        ]
      };

      User.findById.mockResolvedValue(userUnverified);

      const result = await tenantScoringService.calculateTenantScore(mockUser._id);

      expect(result.verificationLevel).toBe('unverified');
    });
  });

  describe('Score Grading', () => {
    it('should assign correct grades based on score', async () => {
      const testCases = [
        { score: 95, expectedGrade: 'A' },
        { score: 85, expectedGrade: 'B' },
        { score: 75, expectedGrade: 'C' },
        { score: 65, expectedGrade: 'D' },
        { score: 45, expectedGrade: 'F' }
      ];

      for (const testCase of testCases) {
        // Mock a user that would result in the desired score
        const mockUserForScore = {
          ...mockUser,
          profile: {
            ...mockUser.profile,
            employment: testCase.score > 80 ? {
              occupation: 'Engineer',
              employmentType: 'full-time',
              contractType: 'permanent',
              incomeVerified: true
            } : {
              occupation: null,
              employmentType: 'unemployed',
              contractType: null,
              incomeVerified: false
            }
          }
        };

        User.findById.mockResolvedValue(mockUserForScore);

        const breakdown = await tenantScoringService.getScoreBreakdown(mockUser._id);
        
        // We can't easily control the exact score, so we'll test the grading function indirectly
        expect(['A', 'B', 'C', 'D', 'F']).toContain(breakdown.grade);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle cache errors gracefully', async () => {
      cacheService.get.mockRejectedValue(new Error('Cache error'));
      User.findById.mockResolvedValue(mockUser);

      const result = await tenantScoringService.calculateTenantScore(mockUser._id);

      expect(result).toHaveProperty('overallScore');
    });

    it('should handle cache set errors gracefully', async () => {
      cacheService.set.mockRejectedValue(new Error('Cache set error'));
      User.findById.mockResolvedValue(mockUser);

      const result = await tenantScoringService.calculateTenantScore(mockUser._id);

      expect(result).toHaveProperty('overallScore');
    });
  });

  describe('Recommendations', () => {
    it('should provide recommendations for low scores', async () => {
      const userWithLowScores = {
        ...mockUser,
        profile: {
          ...mockUser.profile,
          employment: {
            occupation: null,
            employmentType: 'unemployed',
            incomeVerified: false
          },
          rentalHistory: {
            previousAddresses: [],
            evictions: false,
            paymentIssues: false,
            creditScore: 'fair'
          }
        },
        documents: []
      };

      User.findById.mockResolvedValue(userWithLowScores);

      const breakdown = await tenantScoringService.getScoreBreakdown(mockUser._id);

      expect(breakdown.recommendations).toBeInstanceOf(Array);
      expect(breakdown.recommendations.length).toBeGreaterThan(0);
      expect(breakdown.recommendations.some(rec => 
        rec.includes('income') || rec.includes('employment') || rec.includes('documents')
      )).toBe(true);
    });
  });
});