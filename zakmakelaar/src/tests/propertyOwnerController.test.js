const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const propertyOwnerController = require('../controllers/propertyOwnerController');
const propertyOwnerService = require('../services/propertyOwnerService');
const User = require('../models/User');
const auth = require('../middleware/auth');
const { globalErrorHandler } = require('../middleware/errorHandler');

// Mock the service
jest.mock('../services/propertyOwnerService');
jest.mock('../services/logger', () => ({
  loggers: {
    app: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn()
    }
  }
}));

describe('PropertyOwnerController', () => {
  let app;
  let testUser;
  let authToken;

  beforeAll(async () => {
    // Connect to test database
    const mongoUri = process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/zakmakelaar_test';
    await mongoose.connect(mongoUri);

    // Create Express app for testing
    app = express();
    app.use(express.json());
    
    // Add auth middleware
    app.use((req, res, next) => {
      req.user = testUser;
      next();
    });

    // Add routes
    app.post('/register', propertyOwnerController.registerPropertyOwner);
    app.post('/verify', propertyOwnerController.verifyPropertyOwner);
    app.get('/dashboard', propertyOwnerController.getDashboard);
    app.post('/screen-tenants/:propertyId', propertyOwnerController.screenTenants);
    app.post('/rank-applicants/:propertyId', propertyOwnerController.rankApplicants);
    app.get('/properties', propertyOwnerController.manageProperties);
    app.post('/properties', propertyOwnerController.manageProperties);
    app.put('/properties/:propertyId', propertyOwnerController.manageProperties);
    app.delete('/properties/:propertyId', propertyOwnerController.manageProperties);
    app.put('/properties/:propertyId/activate', propertyOwnerController.activateProperty);
    app.put('/properties/:propertyId/deactivate', propertyOwnerController.deactivateProperty);
    app.get('/properties/:propertyId/report', propertyOwnerController.generatePropertyReport);
    app.get('/verification-status', propertyOwnerController.getVerificationStatus);
    app.get('/properties/:propertyId/applications', propertyOwnerController.getPropertyApplications);
    app.put('/applications/:applicationId/status', propertyOwnerController.updateApplicationStatus);
    app.get('/statistics', propertyOwnerController.getStatistics);

    // Add error handler
    app.use(globalErrorHandler);
  });

  beforeEach(async () => {
    // Clear test data
    await User.deleteMany({});

    // Create test user with unique email
    const timestamp = Date.now();
    testUser = new User({
      email: `test${timestamp}@example.com`,
      password: 'password123',
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        userType: ['property_owner']
      },
      propertyOwner: {
        isPropertyOwner: true,
        verificationStatus: 'verified',
        businessRegistration: '12345678',
        properties: ['prop1', 'prop2']
      }
    });
    await testUser.save();

    // Generate auth token
    authToken = jwt.sign({ userId: testUser._id }, 'test-secret');

    // Reset mocks
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await mongoose.connection.close();
  });

  describe('POST /register', () => {
    it('should successfully register property owner', async () => {
      // Requirements: 10.1, 10.2
      const registrationData = {
        businessRegistration: '********',
        taxNumber: 'NL9********B12',
        bankAccount: 'NL98WXYZ9********0',
        businessName: 'Test Property Management B.V.'
      };

      const mockResult = {
        success: true,
        message: 'Property owner registration completed',
        verificationStatus: 'pending',
        userId: testUser._id
      };

      propertyOwnerService.registerAsPropertyOwner.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/register')
        .send(registrationData)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Property owner registration completed successfully');
      expect(response.body.data.success).toBe(mockResult.success);
      expect(response.body.data.message).toBe(mockResult.message);
      expect(response.body.data.verificationStatus).toBe(mockResult.verificationStatus);
      expect(propertyOwnerService.registerAsPropertyOwner).toHaveBeenCalledWith(testUser._id, registrationData);
    });

    it('should return 400 for missing required fields', async () => {
      // Requirements: 10.1
      const incompleteData = {
        taxNumber: 'NL9********B12'
        // Missing businessRegistration
      };

      const response = await request(app)
        .post('/register')
        .send(incompleteData)
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('Missing required fields');
      expect(response.body.missingFields).toContain('businessRegistration');
    });

    it('should handle service errors', async () => {
      // Requirements: 10.1
      const registrationData = {
        businessRegistration: '********'
      };

      propertyOwnerService.registerAsPropertyOwner.mockRejectedValue(new Error('Registration failed'));

      const response = await request(app)
        .post('/register')
        .send(registrationData)
        .expect(500);

      expect(response.body.status).toBe('error');
    });
  });

  describe('POST /verify', () => {
    it('should successfully verify property owner', async () => {
      // Requirements: 10.2, 10.3
      const verificationData = {
        documents: [
          { documentId: 'doc1', type: 'business_registration' }
        ]
      };

      const mockResult = {
        overallStatus: 'verified',
        verificationScore: 85,
        businessRegistration: { valid: true },
        userId: testUser._id
      };

      propertyOwnerService.verifyPropertyOwner.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/verify')
        .send(verificationData)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Property owner verification completed');
      expect(response.body.data).toEqual(mockResult);
      expect(propertyOwnerService.verifyPropertyOwner).toHaveBeenCalledWith(testUser._id, verificationData.documents);
    });

    it('should handle verification without documents', async () => {
      // Requirements: 10.2
      const mockResult = {
        overallStatus: 'partial',
        verificationScore: 60
      };

      propertyOwnerService.verifyPropertyOwner.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/verify')
        .send({})
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(propertyOwnerService.verifyPropertyOwner).toHaveBeenCalledWith(testUser._id, []);
    });
  });

  describe('GET /dashboard', () => {
    it('should return property owner dashboard', async () => {
      // Requirements: 10.4, 10.5
      const mockDashboard = {
        owner: {
          id: testUser._id,
          name: 'John Doe',
          verificationStatus: 'verified'
        },
        properties: {
          total: 2,
          active: 1,
          rented: 1,
          data: []
        },
        applications: {
          total: 5,
          pending: 2,
          reviewed: 3
        },
        screening: {
          totalScreened: 10,
          averageScore: 75
        },
        performance: {
          occupancyRate: 0.85
        }
      };

      propertyOwnerService.getPropertyOwnerDashboard.mockResolvedValue(mockDashboard);

      const response = await request(app)
        .get('/dashboard')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toEqual(mockDashboard);
      expect(propertyOwnerService.getPropertyOwnerDashboard).toHaveBeenCalledWith(testUser._id);
    });
  });

  describe('POST /screen-tenants/:propertyId', () => {
    it('should successfully screen tenants', async () => {
      // Requirements: 10.4, 10.5, 10.6
      const propertyId = 'prop123';
      const screeningData = {
        applicationIds: ['app1', 'app2']
      };

      const mockResult = {
        propertyId,
        screenedApplications: [
          {
            applicationId: 'app1',
            recommendationScore: 85,
            riskLevel: 'low'
          }
        ],
        screeningReport: {
          totalApplications: 1,
          averageScore: 85
        }
      };

      propertyOwnerService.screenTenants.mockResolvedValue(mockResult);

      const response = await request(app)
        .post(`/screen-tenants/${propertyId}`)
        .send(screeningData)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Tenant screening completed');
      expect(response.body.data).toEqual(mockResult);
      expect(propertyOwnerService.screenTenants).toHaveBeenCalledWith(propertyId, screeningData.applicationIds);
    });

    it('should handle screening without application IDs', async () => {
      // Requirements: 10.4
      const propertyId = 'prop123';

      const mockResult = {
        propertyId,
        screenedApplications: [],
        message: 'No applications found for screening'
      };

      propertyOwnerService.screenTenants.mockResolvedValue(mockResult);

      const response = await request(app)
        .post(`/screen-tenants/${propertyId}`)
        .send({})
        .expect(200);

      expect(response.body.data).toEqual(mockResult);
      expect(propertyOwnerService.screenTenants).toHaveBeenCalledWith(propertyId, []);
    });
  });

  describe('POST /rank-applicants/:propertyId', () => {
    it('should successfully rank applicants', async () => {
      // Requirements: 10.5, 10.6
      const propertyId = 'prop123';
      const rankingCriteria = {
        priorityFactors: {
          tenantScore: 0.4,
          applicationCompleteness: 0.3
        },
        minimumScore: 60
      };

      const mockResult = {
        propertyId,
        rankedApplicants: [
          {
            rank: 1,
            applicantId: 'user1',
            finalScore: 85
          }
        ],
        rankingCriteria
      };

      propertyOwnerService.rankApplicants.mockResolvedValue(mockResult);

      const response = await request(app)
        .post(`/rank-applicants/${propertyId}`)
        .send(rankingCriteria)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Applicant ranking completed');
      expect(response.body.data).toEqual(mockResult);
      expect(propertyOwnerService.rankApplicants).toHaveBeenCalledWith(propertyId, rankingCriteria);
    });
  });

  describe('Property Management Routes', () => {
    it('should list properties (GET /properties)', async () => {
      // Requirements: 10.3, 10.4
      const mockProperties = [
        { id: 'prop1', title: 'Property 1', status: 'active' },
        { id: 'prop2', title: 'Property 2', status: 'rented' }
      ];

      propertyOwnerService.manageProperties.mockResolvedValue(mockProperties);

      const response = await request(app)
        .get('/properties')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toEqual(mockProperties);
      expect(propertyOwnerService.manageProperties).toHaveBeenCalledWith(testUser._id, 'list', null);
    });

    it('should add property (POST /properties)', async () => {
      // Requirements: 10.3
      const propertyData = {
        title: 'New Property',
        address: 'Test Street 123',
        rent: 2500
      };

      const mockResult = {
        success: true,
        propertyId: 'prop123',
        message: 'Property added successfully'
      };

      propertyOwnerService.manageProperties.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/properties')
        .send(propertyData)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toEqual(mockResult);
      expect(propertyOwnerService.manageProperties).toHaveBeenCalledWith(testUser._id, 'add', propertyData);
    });

    it('should update property (PUT /properties/:propertyId)', async () => {
      // Requirements: 10.3
      const propertyId = 'prop123';
      const updateData = {
        title: 'Updated Property',
        rent: 2800
      };

      const mockResult = {
        success: true,
        propertyId,
        message: 'Property updated successfully'
      };

      propertyOwnerService.manageProperties.mockResolvedValue(mockResult);

      const response = await request(app)
        .put(`/properties/${propertyId}`)
        .send(updateData)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toEqual(mockResult);
      expect(propertyOwnerService.manageProperties).toHaveBeenCalledWith(
        testUser._id, 
        'update', 
        { ...updateData, propertyId }
      );
    });

    it('should remove property (DELETE /properties/:propertyId)', async () => {
      // Requirements: 10.3
      const propertyId = 'prop123';

      const mockResult = {
        success: true,
        propertyId,
        message: 'Property removed successfully'
      };

      propertyOwnerService.manageProperties.mockResolvedValue(mockResult);

      const response = await request(app)
        .delete(`/properties/${propertyId}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toEqual(mockResult);
      expect(propertyOwnerService.manageProperties).toHaveBeenCalledWith(
        testUser._id, 
        'remove', 
        { propertyId }
      );
    });
  });

  describe('Property Activation/Deactivation', () => {
    it('should activate property', async () => {
      // Requirements: 10.3, 10.4
      const propertyId = 'prop123';

      const mockResult = {
        success: true,
        propertyId,
        status: 'active',
        message: 'Property activated successfully'
      };

      propertyOwnerService.manageProperties.mockResolvedValue(mockResult);

      const response = await request(app)
        .put(`/properties/${propertyId}/activate`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Property activated successfully');
      expect(propertyOwnerService.manageProperties).toHaveBeenCalledWith(
        testUser._id, 
        'activate', 
        { propertyId }
      );
    });

    it('should deactivate property', async () => {
      // Requirements: 10.3, 10.4
      const propertyId = 'prop123';

      const mockResult = {
        success: true,
        propertyId,
        status: 'inactive',
        message: 'Property deactivated successfully'
      };

      propertyOwnerService.manageProperties.mockResolvedValue(mockResult);

      const response = await request(app)
        .put(`/properties/${propertyId}/deactivate`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Property deactivated successfully');
      expect(propertyOwnerService.manageProperties).toHaveBeenCalledWith(
        testUser._id, 
        'deactivate', 
        { propertyId }
      );
    });
  });

  describe('GET /properties/:propertyId/report', () => {
    it('should generate comprehensive report', async () => {
      // Requirements: 10.6
      const propertyId = 'prop123';
      const reportType = 'comprehensive';

      const mockReport = {
        propertyId,
        reportType,
        property: { title: 'Test Property' },
        summary: { totalApplications: 10 },
        generatedAt: new Date()
      };

      propertyOwnerService.generatePropertyReport.mockResolvedValue(mockReport);

      const response = await request(app)
        .get(`/properties/${propertyId}/report?type=${reportType}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Property report generated successfully');
      expect(response.body.data).toEqual(mockReport);
      expect(propertyOwnerService.generatePropertyReport).toHaveBeenCalledWith(propertyId, reportType);
    });

    it('should default to comprehensive report type', async () => {
      // Requirements: 10.6
      const propertyId = 'prop123';

      const mockReport = {
        propertyId,
        reportType: 'comprehensive'
      };

      propertyOwnerService.generatePropertyReport.mockResolvedValue(mockReport);

      const response = await request(app)
        .get(`/properties/${propertyId}/report`)
        .expect(200);

      expect(propertyOwnerService.generatePropertyReport).toHaveBeenCalledWith(propertyId, 'comprehensive');
    });
  });

  describe('GET /verification-status', () => {
    it('should return verification status for property owner', async () => {
      // Requirements: 10.2
      const response = await request(app)
        .get('/verification-status')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.isPropertyOwner).toBe(true);
      expect(response.body.data.verificationStatus).toBe('verified');
      expect(response.body.data.businessRegistration).toBe('12345678');
      expect(response.body.data.properties).toBe(2);
    });

    it('should return 404 for non-property owner', async () => {
      // Requirements: 10.2
      // Update test user to not be a property owner
      testUser.propertyOwner.isPropertyOwner = false;
      await testUser.save();

      const response = await request(app)
        .get('/verification-status')
        .expect(404);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('User is not registered as a property owner');
    });
  });

  describe('GET /properties/:propertyId/applications', () => {
    it('should return applications for property', async () => {
      // Requirements: 10.4, 10.5
      const propertyId = 'prop123';

      const response = await request(app)
        .get(`/properties/${propertyId}/applications`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.applications).toBeDefined();
      expect(response.body.data.pagination).toBeDefined();
    });

    it('should filter applications by status', async () => {
      // Requirements: 10.4
      const propertyId = 'prop123';
      const status = 'submitted';

      const response = await request(app)
        .get(`/properties/${propertyId}/applications?status=${status}`)
        .expect(200);

      expect(response.body.status).toBe('success');
    });
  });

  describe('PUT /applications/:applicationId/status', () => {
    it('should update application status', async () => {
      // Requirements: 10.5, 10.6
      const applicationId = 'app123';
      const statusUpdate = {
        status: 'approved',
        reason: 'Strong candidate',
        notes: 'Excellent tenant score'
      };

      const response = await request(app)
        .put(`/applications/${applicationId}/status`)
        .send(statusUpdate)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Application status updated successfully');
      expect(response.body.data.applicationId).toBe(applicationId);
      expect(response.body.data.newStatus).toBe('approved');
    });

    it('should reject invalid status', async () => {
      // Requirements: 10.5
      const applicationId = 'app123';
      const invalidStatusUpdate = {
        status: 'invalid_status'
      };

      const response = await request(app)
        .put(`/applications/${applicationId}/status`)
        .send(invalidStatusUpdate)
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('Invalid application status');
    });
  });

  describe('GET /statistics', () => {
    it('should return property owner statistics', async () => {
      // Requirements: 10.6
      const response = await request(app)
        .get('/statistics')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.properties).toBeDefined();
      expect(response.body.data.applications).toBeDefined();
      expect(response.body.data.screening).toBeDefined();
      expect(response.body.data.financial).toBeDefined();
      expect(response.body.data.performance).toBeDefined();
    });

    it('should accept period parameter', async () => {
      // Requirements: 10.6
      const period = '7d';

      const response = await request(app)
        .get(`/statistics?period=${period}`)
        .expect(200);

      expect(response.body.data.period).toBe(period);
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors gracefully', async () => {
      // Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6
      propertyOwnerService.getPropertyOwnerDashboard.mockRejectedValue(new Error('Service unavailable'));

      const response = await request(app)
        .get('/dashboard')
        .expect(500);

      expect(response.body.status).toBe('error');
    });

    it('should handle validation errors', async () => {
      // Requirements: 10.1
      const response = await request(app)
        .post('/register')
        .send({}) // Empty body
        .expect(400);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('Missing required fields');
    });
  });

  describe('HTTP Method Handling', () => {
    it('should handle unsupported HTTP methods', async () => {
      // Requirements: 10.3
      const response = await request(app)
        .patch('/properties') // PATCH not supported
        .expect(405);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('Method not allowed');
    });
  });
});