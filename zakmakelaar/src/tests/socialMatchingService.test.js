const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const User = require('../models/User');
const SocialMatch = require('../models/SocialMatch');
const Message = require('../models/Message');
const socialMatchingService = require('../services/socialMatchingService');

describe('Social Matching Service', () => {
  let mongoServer;
  let testUser1, testUser2, testUser3;

  beforeAll(async () => {
    // Setup in-memory MongoDB
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    // Clear all collections
    await User.deleteMany({});
    await SocialMatch.deleteMany({});
    await Message.deleteMany({});

    // Create test users
    testUser1 = await User.create({
      email: '<EMAIL>',
      password: 'password123',
      emailVerified: true,
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: new Date('1995-01-01'),
        userType: ['student'],
        employment: {
          occupation: 'Student',
          employmentType: 'student'
        },
        socialPreferences: {
          lookingForRoommate: true,
          isVisible: true,
          roommateCriteria: {
            ageRange: { min: 20, max: 30 },
            gender: 'any',
            occupation: ['student', 'young_professional'],
            lifestyle: {
              cleanliness: 'clean',
              noiseLevel: 'quiet',
              socialLevel: 'social',
              smokingTolerance: false,
              petTolerance: true,
              guestPolicy: 'moderate'
            }
          }
        }
      }
    });

    testUser2 = await User.create({
      email: '<EMAIL>',
      password: 'password123',
      emailVerified: true,
      profile: {
        firstName: 'Jane',
        lastName: 'Smith',
        dateOfBirth: new Date('1993-05-15'),
        userType: ['young_professional'],
        employment: {
          occupation: 'Software Engineer',
          employmentType: 'full-time'
        },
        socialPreferences: {
          lookingForRoommate: true,
          isVisible: true,
          roommateCriteria: {
            ageRange: { min: 22, max: 35 },
            gender: 'any',
            occupation: ['student', 'young_professional'],
            lifestyle: {
              cleanliness: 'clean',
              noiseLevel: 'quiet',
              socialLevel: 'moderate',
              smokingTolerance: false,
              petTolerance: true,
              guestPolicy: 'flexible'
            }
          }
        }
      }
    });

    testUser3 = await User.create({
      email: '<EMAIL>',
      password: 'password123',
      emailVerified: true,
      profile: {
        firstName: 'Bob',
        lastName: 'Wilson',
        dateOfBirth: new Date('1990-12-20'),
        userType: ['expat'],
        employment: {
          occupation: 'Marketing Manager',
          employmentType: 'full-time'
        },
        socialPreferences: {
          lookingForRoommate: false, // Not looking for roommate
          isVisible: false,
          roommateCriteria: {
            ageRange: { min: 25, max: 40 },
            gender: 'male',
            occupation: ['young_professional'],
            lifestyle: {
              cleanliness: 'moderate',
              noiseLevel: 'lively',
              socialLevel: 'very_social',
              smokingTolerance: true,
              petTolerance: false,
              guestPolicy: 'flexible'
            }
          }
        }
      }
    });
  });

  describe('findPotentialRoommates', () => {
    test('should find compatible roommates based on criteria', async () => {
      const matches = await socialMatchingService.findPotentialRoommates(testUser1._id);
      
      expect(matches).toHaveLength(1);
      expect(matches[0].user._id.toString()).toBe(testUser2._id.toString());
      expect(matches[0].compatibilityScore).toBeGreaterThan(0);
      expect(matches[0].matchedCriteria).toContain('age_range');
    });

    test('should not include users not looking for roommates', async () => {
      const matches = await socialMatchingService.findPotentialRoommates(testUser1._id);
      
      const userIds = matches.map(match => match.user._id.toString());
      expect(userIds).not.toContain(testUser3._id.toString());
    });

    test('should not include users with visibility disabled', async () => {
      const matches = await socialMatchingService.findPotentialRoommates(testUser1._id);
      
      const userIds = matches.map(match => match.user._id.toString());
      expect(userIds).not.toContain(testUser3._id.toString());
    });

    test('should apply age range filters', async () => {
      const filters = {
        ageRange: { min: 25, max: 35 }
      };
      
      const matches = await socialMatchingService.findPotentialRoommates(testUser1._id, filters);
      
      // Should only include testUser2 (born 1993, age ~30)
      expect(matches).toHaveLength(1);
      expect(matches[0].user._id.toString()).toBe(testUser2._id.toString());
    });

    test('should apply user type filters', async () => {
      const filters = {
        userType: ['young_professional']
      };
      
      const matches = await socialMatchingService.findPotentialRoommates(testUser1._id, filters);
      
      expect(matches).toHaveLength(1);
      expect(matches[0].user._id.toString()).toBe(testUser2._id.toString());
    });

    test('should throw error for ineligible users', async () => {
      await expect(
        socialMatchingService.findPotentialRoommates(testUser3._id)
      ).rejects.toThrow('User not eligible for social matching');
    });
  });

  describe('createMatch', () => {
    test('should create a match between two eligible users', async () => {
      const match = await socialMatchingService.createMatch(testUser1._id, testUser2._id);
      
      expect(match).toBeDefined();
      expect(match.status).toBe('pending');
      expect(match.initiatedBy.toString()).toBe(testUser1._id.toString());
      expect(match.compatibilityScore).toBeGreaterThan(0);
      expect(match.matchedCriteria).toBeInstanceOf(Array);
    });

    test('should not create duplicate matches', async () => {
      await socialMatchingService.createMatch(testUser1._id, testUser2._id);
      
      await expect(
        socialMatchingService.createMatch(testUser1._id, testUser2._id)
      ).rejects.toThrow('Match already exists between these users');
    });

    test('should throw error for non-existent users', async () => {
      const fakeId = new mongoose.Types.ObjectId();
      
      await expect(
        socialMatchingService.createMatch(testUser1._id, fakeId)
      ).rejects.toThrow('One or both users not found');
    });

    test('should throw error for ineligible users', async () => {
      await expect(
        socialMatchingService.createMatch(testUser1._id, testUser3._id)
      ).rejects.toThrow('One or both users not eligible for social matching');
    });
  });

  describe('respondToMatch', () => {
    let testMatch;

    beforeEach(async () => {
      testMatch = await socialMatchingService.createMatch(testUser1._id, testUser2._id);
    });

    test('should accept a match', async () => {
      const updatedMatch = await socialMatchingService.respondToMatch(
        testMatch._id, 
        testUser2._id, 
        'accept'
      );
      
      expect(updatedMatch.status).toBe('accepted');
      expect(updatedMatch.respondedAt).toBeDefined();
    });

    test('should reject a match', async () => {
      const updatedMatch = await socialMatchingService.respondToMatch(
        testMatch._id, 
        testUser2._id, 
        'reject'
      );
      
      expect(updatedMatch.status).toBe('rejected');
      expect(updatedMatch.respondedAt).toBeDefined();
    });

    test('should throw error for unauthorized user', async () => {
      await expect(
        socialMatchingService.respondToMatch(testMatch._id, testUser3._id, 'accept')
      ).rejects.toThrow('User not authorized to respond to this match');
    });

    test('should throw error for non-pending match', async () => {
      await socialMatchingService.respondToMatch(testMatch._id, testUser2._id, 'accept');
      
      await expect(
        socialMatchingService.respondToMatch(testMatch._id, testUser2._id, 'reject')
      ).rejects.toThrow('Match is not in pending status');
    });
  });

  describe('getMatches', () => {
    beforeEach(async () => {
      await socialMatchingService.createMatch(testUser1._id, testUser2._id);
    });

    test('should get all matches for a user', async () => {
      const result = await socialMatchingService.getMatches(testUser1._id);
      
      expect(result.matches).toHaveLength(1);
      expect(result.pagination.total).toBe(1);
    });

    test('should filter matches by status', async () => {
      const result = await socialMatchingService.getMatches(testUser1._id, 'pending');
      
      expect(result.matches).toHaveLength(1);
      expect(result.matches[0].status).toBe('pending');
    });

    test('should handle pagination', async () => {
      const result = await socialMatchingService.getMatches(testUser1._id, null, 1, 10);
      
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
    });
  });

  describe('updateSocialPreferences', () => {
    test('should update social preferences', async () => {
      const newPreferences = {
        lookingForRoommate: false,
        isVisible: false,
        roommateCriteria: {
          ageRange: { min: 25, max: 35 }
        }
      };
      
      const updatedPrefs = await socialMatchingService.updateSocialPreferences(
        testUser1._id, 
        newPreferences
      );
      
      expect(updatedPrefs.lookingForRoommate).toBe(false);
      expect(updatedPrefs.isVisible).toBe(false);
      expect(updatedPrefs.roommateCriteria.ageRange.min).toBe(25);
    });

    test('should validate age range', async () => {
      const invalidPreferences = {
        roommateCriteria: {
          ageRange: { min: 35, max: 25 } // Invalid: min > max
        }
      };
      
      await expect(
        socialMatchingService.updateSocialPreferences(testUser1._id, invalidPreferences)
      ).rejects.toThrow('Minimum age cannot be greater than maximum age');
    });
  });

  describe('toggleVisibility', () => {
    test('should toggle visibility on', async () => {
      const preferences = await socialMatchingService.toggleVisibility(testUser3._id, true);
      
      expect(preferences.isVisible).toBe(true);
    });

    test('should toggle visibility off', async () => {
      const preferences = await socialMatchingService.toggleVisibility(testUser1._id, false);
      
      expect(preferences.isVisible).toBe(false);
    });
  });

  describe('reportUser', () => {
    let testMatch;

    beforeEach(async () => {
      testMatch = await socialMatchingService.createMatch(testUser1._id, testUser2._id);
    });

    test('should report a user', async () => {
      const result = await socialMatchingService.reportUser(
        testUser1._id, 
        testUser2._id, 
        'inappropriate_behavior',
        'Test report description'
      );
      
      expect(result.success).toBe(true);
      
      // Verify report was added to match
      const updatedMatch = await SocialMatch.findById(testMatch._id);
      expect(updatedMatch.reportedBy).toHaveLength(1);
      expect(updatedMatch.reportedBy[0].reason).toBe('inappropriate_behavior');
    });

    test('should throw error when no match exists', async () => {
      await expect(
        socialMatchingService.reportUser(testUser1._id, testUser3._id, 'spam')
      ).rejects.toThrow('No match exists between these users');
    });
  });

  describe('blockUser', () => {
    let testMatch;

    beforeEach(async () => {
      testMatch = await socialMatchingService.createMatch(testUser1._id, testUser2._id);
    });

    test('should block a user', async () => {
      const result = await socialMatchingService.blockUser(testUser1._id, testUser2._id);
      
      expect(result.success).toBe(true);
      
      // Verify match status changed to blocked
      const updatedMatch = await SocialMatch.findById(testMatch._id);
      expect(updatedMatch.status).toBe('blocked');
      expect(updatedMatch.blockedBy).toHaveLength(1);
    });

    test('should throw error when no match exists', async () => {
      await expect(
        socialMatchingService.blockUser(testUser1._id, testUser3._id)
      ).rejects.toThrow('No match exists between these users');
    });
  });

  describe('sendMessage', () => {
    let testMatch;

    beforeEach(async () => {
      testMatch = await socialMatchingService.createMatch(testUser1._id, testUser2._id);
      await socialMatchingService.respondToMatch(testMatch._id, testUser2._id, 'accept');
    });

    test('should send a message', async () => {
      const message = await socialMatchingService.sendMessage(
        testMatch._id, 
        testUser1._id, 
        'Hello, how are you?'
      );
      
      expect(message.content).toBe('Hello, how are you?');
      expect(message.sender.toString()).toBe(testUser1._id.toString());
      expect(message.recipient.toString()).toBe(testUser2._id.toString());
    });

    test('should throw error for unauthorized user', async () => {
      await expect(
        socialMatchingService.sendMessage(testMatch._id, testUser3._id, 'Hello')
      ).rejects.toThrow('User not authorized to send messages in this match');
    });

    test('should throw error for empty message', async () => {
      await expect(
        socialMatchingService.sendMessage(testMatch._id, testUser1._id, '')
      ).rejects.toThrow('Message content cannot be empty');
    });

    test('should throw error for too long message', async () => {
      const longMessage = 'a'.repeat(1001);
      
      await expect(
        socialMatchingService.sendMessage(testMatch._id, testUser1._id, longMessage)
      ).rejects.toThrow('Message content too long');
    });
  });

  describe('getConversation', () => {
    let testMatch, testMessage;

    beforeEach(async () => {
      testMatch = await socialMatchingService.createMatch(testUser1._id, testUser2._id);
      await socialMatchingService.respondToMatch(testMatch._id, testUser2._id, 'accept');
      testMessage = await socialMatchingService.sendMessage(
        testMatch._id, 
        testUser1._id, 
        'Test message'
      );
    });

    test('should get conversation messages', async () => {
      const conversation = await socialMatchingService.getConversation(
        testMatch._id, 
        testUser1._id
      );
      
      expect(conversation.messages).toHaveLength(1);
      expect(conversation.messages[0].content).toBe('Test message');
      expect(conversation.pagination.total).toBe(1);
    });

    test('should throw error for unauthorized user', async () => {
      await expect(
        socialMatchingService.getConversation(testMatch._id, testUser3._id)
      ).rejects.toThrow('User not authorized to view this conversation');
    });
  });

  describe('getUserConversations', () => {
    beforeEach(async () => {
      const match = await socialMatchingService.createMatch(testUser1._id, testUser2._id);
      await socialMatchingService.respondToMatch(match._id, testUser2._id, 'accept');
      await socialMatchingService.sendMessage(match._id, testUser1._id, 'Hello');
    });

    test('should get user conversations', async () => {
      const conversations = await socialMatchingService.getUserConversations(testUser1._id);
      
      expect(conversations.conversations).toHaveLength(1);
      expect(conversations.conversations[0].lastMessage).toBeDefined();
      expect(conversations.conversations[0].otherUser).toBeDefined();
    });
  });

  describe('getUnreadMessageCount', () => {
    beforeEach(async () => {
      const match = await socialMatchingService.createMatch(testUser1._id, testUser2._id);
      await socialMatchingService.respondToMatch(match._id, testUser2._id, 'accept');
      await socialMatchingService.sendMessage(match._id, testUser1._id, 'Unread message');
    });

    test('should get unread message count', async () => {
      const result = await socialMatchingService.getUnreadMessageCount(testUser2._id);
      
      expect(result.unreadCount).toBe(1);
    });
  });

  describe('deleteMessage', () => {
    let testMatch, testMessage;

    beforeEach(async () => {
      testMatch = await socialMatchingService.createMatch(testUser1._id, testUser2._id);
      await socialMatchingService.respondToMatch(testMatch._id, testUser2._id, 'accept');
      testMessage = await socialMatchingService.sendMessage(
        testMatch._id, 
        testUser1._id, 
        'Message to delete'
      );
    });

    test('should delete a message', async () => {
      const result = await socialMatchingService.deleteMessage(
        testMessage._id, 
        testUser1._id
      );
      
      expect(result.success).toBe(true);
      
      // Verify message is soft deleted
      const deletedMessage = await Message.findById(testMessage._id);
      expect(deletedMessage.isDeleted).toBe(true);
    });

    test('should throw error for unauthorized user', async () => {
      await expect(
        socialMatchingService.deleteMessage(testMessage._id, testUser2._id)
      ).rejects.toThrow('User not authorized to delete this message');
    });
  });

  describe('compatibility scoring', () => {
    test('should calculate compatibility score correctly', async () => {
      // Create users with similar preferences
      const user1 = await User.create({
        email: '<EMAIL>',
        password: 'password123',
        emailVerified: true,
        profile: {
          firstName: 'Compatible',
          lastName: 'User1',
          dateOfBirth: new Date('1995-01-01'),
          userType: ['student'],
          employment: { occupation: 'Student', employmentType: 'student' },
          socialPreferences: {
            lookingForRoommate: true,
            isVisible: true,
            roommateCriteria: {
              ageRange: { min: 20, max: 30 },
              gender: 'any',
              lifestyle: {
                cleanliness: 'clean',
                noiseLevel: 'quiet',
                socialLevel: 'social',
                smokingTolerance: false,
                petTolerance: true,
                guestPolicy: 'moderate'
              }
            }
          }
        }
      });

      const user2 = await User.create({
        email: '<EMAIL>',
        password: 'password123',
        emailVerified: true,
        profile: {
          firstName: 'Compatible',
          lastName: 'User2',
          dateOfBirth: new Date('1994-01-01'),
          userType: ['student'],
          employment: { occupation: 'Student', employmentType: 'student' },
          socialPreferences: {
            lookingForRoommate: true,
            isVisible: true,
            roommateCriteria: {
              ageRange: { min: 20, max: 30 },
              gender: 'any',
              lifestyle: {
                cleanliness: 'clean',
                noiseLevel: 'quiet',
                socialLevel: 'social',
                smokingTolerance: false,
                petTolerance: true,
                guestPolicy: 'moderate'
              }
            }
          }
        }
      });

      const matches = await socialMatchingService.findPotentialRoommates(user1._id);
      const compatibleMatch = matches.find(m => m.user._id.toString() === user2._id.toString());
      
      expect(compatibleMatch).toBeDefined();
      expect(compatibleMatch.compatibilityScore).toBeGreaterThan(80); // High compatibility
    });
  });

  describe('privacy controls', () => {
    test('should respect visibility settings', async () => {
      // Make testUser2 invisible
      await socialMatchingService.toggleVisibility(testUser2._id, false);
      
      const matches = await socialMatchingService.findPotentialRoommates(testUser1._id);
      const userIds = matches.map(match => match.user._id.toString());
      
      expect(userIds).not.toContain(testUser2._id.toString());
    });

    test('should respect lookingForRoommate setting', async () => {
      // Make testUser2 not looking for roommate
      await socialMatchingService.updateSocialPreferences(testUser2._id, {
        lookingForRoommate: false
      });
      
      const matches = await socialMatchingService.findPotentialRoommates(testUser1._id);
      const userIds = matches.map(match => match.user._id.toString());
      
      expect(userIds).not.toContain(testUser2._id.toString());
    });
  });
});