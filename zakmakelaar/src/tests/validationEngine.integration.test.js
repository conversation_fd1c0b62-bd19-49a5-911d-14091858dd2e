/**
 * Validation Engine Integration Tests
 * 
 * This file contains integration tests for the ValidationEngine with SchemaTransformer.
 */

const { ValidationEngine } = require('../services/validationEngine');
const { SchemaTransformer } = require('../services/schemaTransformer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { SchemaError, ErrorTypes } = require('../utils/schemaErrors');

describe('ValidationEngine Integration', () => {
  let validationEngine;
  let mappingRegistry;
  let schemaTransformer;
  let fundaMapping;
  let fundaRawData;
  
  beforeEach(() => {
    // Create validation engine
    validationEngine = new ValidationEngine();
    
    // Create mapping registry
    mappingRegistry = new FieldMappingRegistry();
    
    // Create schema transformer
    schemaTransformer = new SchemaTransformer(mappingRegistry);
    
    // Define sample Funda mapping
    fundaMapping = {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'funda.nl' },
      'propertyType': {
        path: 'propertyType',
        transform: 'normalizePropertyType'
      },
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'location': {
        path: 'location',
        transform: 'normalizeLocation'
      },
      'size': {
        path: 'size',
        transform: 'formatSizeString'
      },
      'area': {
        path: 'size',
        transform: 'extractNumericSize'
      },
      'rooms': {
        path: 'rooms',
        transform: 'normalizeRooms'
      },
      'bedrooms': {
        path: 'bedrooms',
        transform: 'normalizeRooms'
      },
      'year': {
        path: 'year',
        transform: 'normalizeYear'
      },
      'interior': {
        path: 'interior',
        transform: 'normalizeInterior'
      },
      'images': {
        path: 'images',
        transform: 'normalizeImageArray'
      }
    };
    
    // Register mapping
    mappingRegistry.registerMapping('funda', fundaMapping);
    
    // Sample raw data from Funda
    fundaRawData = {
      title: 'Beautiful Apartment in Amsterdam',
      description: 'A spacious apartment in the heart of Amsterdam',
      url: 'https://funda.nl/apartment/123',
      propertyType: 'appartement',
      price: '€1500 per month',
      location: 'Amsterdam, Netherlands',
      size: '85m2',
      rooms: '3',
      bedrooms: 2,
      year: '2010',
      interior: 'gemeubileerd',
      images: [
        'https://funda.nl/images/1.jpg',
        'https://funda.nl/images/2.jpg'
      ]
    };
  });
  
  test('should validate transformed data', async () => {
    // Transform raw data
    const transformed = await schemaTransformer.transform(fundaRawData, 'funda', {
      validateOutput: false // Skip validation in transformer
    });
    
    // Validate with validation engine
    const validationResult = validationEngine.validate(transformed);
    
    expect(validationResult.valid).toBe(true);
    expect(validationResult.errors).toEqual([]);
    expect(validationResult.value).toBeDefined();
    
    // Check specific fields
    expect(validationResult.value.title).toBe('Beautiful Apartment in Amsterdam');
    expect(validationResult.value.source).toBe('funda.nl');
    expect(validationResult.value.propertyType).toBe('apartment');
    expect(validationResult.value.size).toBe('85 m²');
    expect(validationResult.value.area).toBe(85);
  });
  
  test('should detect validation errors in transformed data', async () => {
    // Create invalid raw data
    const invalidRawData = {
      ...fundaRawData,
      title: '', // Invalid title (empty)
      price: -100, // Invalid price (negative)
      rooms: -3 // Invalid rooms (negative)
    };
    
    // Transform raw data
    const transformed = await schemaTransformer.transform(invalidRawData, 'funda', {
      validateOutput: false // Skip validation in transformer
    });
    
    // Validate with validation engine
    const validationResult = validationEngine.validate(transformed);
    
    expect(validationResult.valid).toBe(false);
    expect(validationResult.errors.length).toBeGreaterThan(0);
    
    // Check specific errors
    const errorFields = validationResult.errors.map(error => error.context.field);
    expect(errorFields).toContain('title');
    expect(errorFields).toContain('price');
    expect(errorFields).toContain('rooms');
  });
  
  test('should fix validation errors in transformed data', async () => {
    // Create invalid raw data
    const invalidRawData = {
      ...fundaRawData,
      title: '', // Invalid title (empty)
      price: -100, // Invalid price (negative)
      rooms: -3 // Invalid rooms (negative)
    };
    
    // Transform raw data
    const transformed = await schemaTransformer.transform(invalidRawData, 'funda', {
      validateOutput: false // Skip validation in transformer
    });
    
    // Fix data with validation engine
    const fixed = validationEngine.fixData(transformed);
    
    // Validate fixed data
    const validationResult = validationEngine.validate(fixed);
    
    expect(validationResult.valid).toBe(true);
    expect(validationResult.errors).toEqual([]);
    
    // Check that fields were fixed
    expect(fixed.title).not.toBe('');
    expect(fixed.price).not.toBe(-100);
    expect(fixed.rooms).not.toBe(-3);
  });
  
  test('should calculate data quality for transformed data', async () => {
    // Transform raw data
    const transformed = await schemaTransformer.transform(fundaRawData, 'funda', {
      validateOutput: false // Skip validation in transformer
    });
    
    // Calculate data quality
    const quality = validationEngine.calculateDataQuality(transformed);
    
    expect(quality.completeness).toBeGreaterThanOrEqual(0);
    expect(quality.completeness).toBeLessThanOrEqual(100);
    expect(quality.accuracy).toBeGreaterThanOrEqual(0);
    expect(quality.accuracy).toBeLessThanOrEqual(100);
  });
  
  test('should classify errors by severity for transformed data', async () => {
    // Create partially invalid raw data
    const partiallyInvalidRawData = {
      ...fundaRawData,
      year: '9999', // Invalid year (minor error)
      interior: 'invalid' // Invalid interior (minor error)
    };
    
    // Transform raw data
    const transformed = await schemaTransformer.transform(partiallyInvalidRawData, 'funda', {
      validateOutput: false // Skip validation in transformer
    });
    
    // Validate with error classification
    const result = validationEngine.validateWithErrorClassification(transformed);
    
    expect(result.valid).toBe(false);
    expect(result.errors.critical).toEqual([]);
    expect(result.errors.major).toEqual([]);
    expect(result.errors.minor.length).toBeGreaterThan(0);
    
    // Check specific minor errors
    const minorErrorFields = result.errors.minor.map(error => error.context.field);
    expect(minorErrorFields).toContain('interior');
  });
  
  test('should integrate with schema transformer validation', async () => {
    // Create schema transformer with validation
    const transformerWithValidation = new SchemaTransformer(mappingRegistry);
    
    // Transform with validation
    const result = await transformerWithValidation.transform(fundaRawData, 'funda', {
      validateOutput: true,
      throwOnError: false
    });
    
    // Check that transformation succeeded
    expect(result.title).toBe('Beautiful Apartment in Amsterdam');
    expect(result.source).toBe('funda.nl');
    
    // Check that internal metadata exists
    expect(result._internal).toBeDefined();
    
    // The dataQuality might not be defined if there were no validation errors
    // Let's just check that the transformation was successful
    expect(result._internal.processingMetadata).toBeDefined();
  });
  
  test('should handle missing required fields', async () => {
    // Create raw data with missing required fields
    const incompleteRawData = {
      description: 'Incomplete property data',
      location: 'Amsterdam'
      // Missing title, url, price, etc.
    };
    
    // Transform raw data
    const transformed = await schemaTransformer.transform(incompleteRawData, 'funda', {
      validateOutput: false // Skip validation in transformer
    });
    
    // Validate with validation engine
    const validationResult = validationEngine.validate(transformed);
    
    expect(validationResult.valid).toBe(false);
    
    // Check specific errors for missing required fields
    const errorFields = validationResult.errors.map(error => error.context.field);
    expect(errorFields).toContain('title');
    expect(errorFields).toContain('url');
    expect(errorFields).toContain('price');
  });
  
  test('should apply defaults to incomplete transformed data', async () => {
    // Create raw data with missing optional fields
    const incompleteRawData = {
      title: 'Minimal Property',
      url: 'https://funda.nl/property/123',
      location: 'Amsterdam',
      price: '€1200'
      // Missing other fields
    };
    
    // Transform raw data
    const transformed = await schemaTransformer.transform(incompleteRawData, 'funda', {
      validateOutput: false // Skip validation in transformer
    });
    
    // Apply defaults and validate
    const validationResult = validationEngine.validate(transformed, { applyDefaults: true });
    
    expect(validationResult.valid).toBe(true);
    
    // Check that defaults were applied
    expect(validationResult.value.propertyType).toBe('woning');
    expect(validationResult.value.bathrooms).toBe('1');
    expect(validationResult.value.furnished).toBe(false);
    expect(validationResult.value.pets).toBe(false);
    expect(validationResult.value.smoking).toBe(false);
    expect(validationResult.value.garden).toBe(false);
    expect(validationResult.value.balcony).toBe(false);
    expect(validationResult.value.parking).toBe(false);
    expect(validationResult.value.isActive).toBe(true);
    expect(validationResult.value.images).toEqual([]);
    expect(validationResult.value.features).toEqual([]);
  });
});