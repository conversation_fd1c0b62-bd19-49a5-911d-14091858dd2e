/**
 * Optimized Transformation Performance Tests
 * 
 * This file contains performance tests to verify that the optimized transformation pipeline
 * meets the performance requirements specified in the requirements document.
 * 
 * Requirements:
 * - W<PERSON><PERSON> data transformation occurs THEN the system SHALL complete processing within 100ms per property record
 * - WHEN multiple scrapers run concurrently THEN the system SHALL handle schema normalization without resource contention
 * - WHEN large batches of data are processed THEN the system SHALL maintain memory usage below 500MB for transformation operations
 */

const { SchemaTransformer } = require('../services/schemaTransformer');
const { OptimizedSchemaTransformer } = require('../services/transformationOptimizer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { MappingConfigLoader } = require('../services/mappingConfigLoader');
const { rawScraperData } = require('./testData/unifiedSchemaTestData');

describe('Optimized Transformation Performance', () => {
  let registry;
  let baseTransformer;
  let optimizedTransformer;
  
  beforeEach(() => {
    // Set up the transformation pipeline components
    registry = new FieldMappingRegistry();
    const loader = new MappingConfigLoader(registry);
    
    // Load mappings for all sources
    loader.loadFromObject('funda.nl', {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'funda.nl' },
      'location': {
        path: 'location',
        transform: 'normalizeLocation'
      },
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'propertyType': {
        path: 'propertyType',
        transform: 'normalizePropertyType'
      },
      'size': {
        path: 'size',
        transform: 'formatSizeString'
      },
      'area': {
        path: 'size',
        transform: 'extractNumericSize'
      },
      'rooms': {
        path: 'rooms',
        transform: 'normalizeRooms'
      },
      'bedrooms': {
        path: 'bedrooms',
        transform: 'normalizeRooms'
      },
      'year': {
        path: 'year',
        transform: 'normalizeYear'
      },
      'interior': {
        path: 'interior',
        transform: 'normalizeInterior'
      },
      'furnished': {
        path: 'interior',
        transform: 'inferFurnishedStatus'
      },
      'images': {
        path: 'images',
        transform: 'normalizeImageArray'
      },
      'dateAdded': {
        transform: 'getCurrentISOString'
      }
    });
    
    // Create base transformer
    baseTransformer = new SchemaTransformer(registry);
    
    // Create optimized transformer
    optimizedTransformer = new OptimizedSchemaTransformer(baseTransformer, {
      cache: {
        stdTTL: 60, // 1 minute for tests
        maxKeys: 1000
      },
      concurrent: {
        maxWorkers: 2, // Limit to 2 workers for tests
        minBatchSize: 5
      }
    });
  });
  
  test('should transform a single property faster with optimized transformer', async () => {
    // Measure the time it takes to transform a single property with base transformer
    const baseStartTime = process.hrtime();
    await baseTransformer.transform(rawScraperData.funda, 'funda.nl');
    const baseEndTime = process.hrtime(baseStartTime);
    const baseExecutionTimeMs = (baseEndTime[0] * 1000) + (baseEndTime[1] / 1000000);
    
    // Measure the time it takes to transform a single property with optimized transformer
    const optimizedStartTime = process.hrtime();
    await optimizedTransformer.transform(rawScraperData.funda, 'funda.nl');
    const optimizedEndTime = process.hrtime(optimizedStartTime);
    const optimizedExecutionTimeMs = (optimizedEndTime[0] * 1000) + (optimizedEndTime[1] / 1000000);
    
    // Verify that both transformations took less than 100ms
    expect(baseExecutionTimeMs).toBeLessThan(100);
    expect(optimizedExecutionTimeMs).toBeLessThan(100);
    
    // Verify that the optimized transformer is at least as fast as the base transformer
    expect(optimizedExecutionTimeMs).toBeLessThanOrEqual(baseExecutionTimeMs * 1.2); // Allow 20% overhead for first run
    
    // Second run should be much faster due to caching
    const cachedStartTime = process.hrtime();
    await optimizedTransformer.transform(rawScraperData.funda, 'funda.nl');
    const cachedEndTime = process.hrtime(cachedStartTime);
    const cachedExecutionTimeMs = (cachedEndTime[0] * 1000) + (cachedEndTime[1] / 1000000);
    
    // Verify that the cached transformation is significantly faster
    expect(cachedExecutionTimeMs).toBeLessThan(optimizedExecutionTimeMs * 0.5); // At least 50% faster
  });
  
  test('should transform 100 properties faster with optimized transformer', async () => {
    // Create an array of 100 properties
    const properties = Array(100).fill().map((_, i) => ({
      ...rawScraperData.funda,
      title: `Property ${i}`,
      url: `https://www.funda.nl/huur/amsterdam/appartement-${i}/`
    }));
    
    // Measure the time it takes to transform 100 properties with base transformer
    const baseStartTime = process.hrtime();
    await baseTransformer.batchTransform(properties, 'funda.nl');
    const baseEndTime = process.hrtime(baseStartTime);
    const baseExecutionTimeMs = (baseEndTime[0] * 1000) + (baseEndTime[1] / 1000000);
    
    // Measure the time it takes to transform 100 properties with optimized transformer
    const optimizedStartTime = process.hrtime();
    await optimizedTransformer.batchTransform(properties, 'funda.nl', { useConcurrent: true });
    const optimizedEndTime = process.hrtime(optimizedStartTime);
    const optimizedExecutionTimeMs = (optimizedEndTime[0] * 1000) + (optimizedEndTime[1] / 1000000);
    
    // Verify that both transformations took less than 10 seconds (100ms per property)
    expect(baseExecutionTimeMs).toBeLessThan(10000);
    expect(optimizedExecutionTimeMs).toBeLessThan(10000);
    
    // Verify that the optimized transformer is faster than the base transformer
    expect(optimizedExecutionTimeMs).toBeLessThan(baseExecutionTimeMs * 0.8); // At least 20% faster
  });
  
  test('should handle concurrent transformations efficiently', async () => {
    // Create arrays of properties for each scraper
    const fundaProperties = Array(20).fill().map((_, i) => ({
      ...rawScraperData.funda,
      title: `Funda Property ${i}`,
      url: `https://www.funda.nl/huur/amsterdam/appartement-${i}/`
    }));
    
    const huurwoningenProperties = Array(20).fill().map((_, i) => ({
      ...rawScraperData.huurwoningen,
      title: `Huurwoningen Property ${i}`,
      url: `https://www.huurwoningen.nl/huren/rotterdam/studio-${i}/`
    }));
    
    const parariusProperties = Array(20).fill().map((_, i) => ({
      ...rawScraperData.pararius,
      title: `Pararius Property ${i}`,
      url: `https://www.pararius.nl/huurwoningen/utrecht/huis-${i}/`
    }));
    
    // Measure the time it takes to transform properties from multiple scrapers concurrently with optimized transformer
    const startTime = process.hrtime();
    
    // Run transformations concurrently
    await Promise.all([
      optimizedTransformer.batchTransform(fundaProperties, 'funda.nl', { useConcurrent: true }),
      optimizedTransformer.batchTransform(huurwoningenProperties, 'huurwoningen.nl', { useConcurrent: true }),
      optimizedTransformer.batchTransform(parariusProperties, 'pararius.nl', { useConcurrent: true })
    ]);
    
    const endTime = process.hrtime(startTime);
    const executionTimeMs = (endTime[0] * 1000) + (endTime[1] / 1000000);
    
    // Verify that the concurrent transformations completed successfully
    // and took less than 6 seconds (100ms per property * 60 properties)
    expect(executionTimeMs).toBeLessThan(6000);
  });
  
  test('should maintain reasonable memory usage for large batches', async () => {
    // Create a large batch of properties (500 properties)
    const properties = Array(500).fill().map((_, i) => ({
      ...rawScraperData.funda,
      title: `Property ${i}`,
      url: `https://www.funda.nl/huur/amsterdam/appartement-${i}/`,
      description: `A detailed description of property ${i}. `.repeat(20) // Add some bulk
    }));
    
    // Measure memory usage before transformation
    const memoryBefore = process.memoryUsage();
    
    // Transform the large batch with optimized transformer
    await optimizedTransformer.batchTransform(properties, 'funda.nl', { useConcurrent: true });
    
    // Measure memory usage after transformation
    const memoryAfter = process.memoryUsage();
    
    // Calculate memory increase in MB
    const memoryIncreaseMB = (memoryAfter.heapUsed - memoryBefore.heapUsed) / (1024 * 1024);
    
    // Verify that memory usage increase is below 500MB
    expect(memoryIncreaseMB).toBeLessThan(500);
  });
  
  test('should benefit from caching for repeated transformations', async () => {
    // Create a set of properties with some duplicates
    const properties = Array(50).fill().map((_, i) => ({
      ...rawScraperData.funda,
      title: `Property ${i % 10}`, // Only 10 unique titles
      url: `https://www.funda.nl/huur/amsterdam/appartement-${i % 10}/`, // Only 10 unique URLs
      description: `A detailed description of property ${i % 10}.` // Only 10 unique descriptions
    }));
    
    // First run - no cache
    const firstRunStartTime = process.hrtime();
    await optimizedTransformer.batchTransform(properties, 'funda.nl', { useConcurrent: true });
    const firstRunEndTime = process.hrtime(firstRunStartTime);
    const firstRunExecutionTimeMs = (firstRunEndTime[0] * 1000) + (firstRunEndTime[1] / 1000000);
    
    // Second run - should benefit from cache
    const secondRunStartTime = process.hrtime();
    await optimizedTransformer.batchTransform(properties, 'funda.nl', { useConcurrent: true });
    const secondRunEndTime = process.hrtime(secondRunStartTime);
    const secondRunExecutionTimeMs = (secondRunEndTime[0] * 1000) + (secondRunEndTime[1] / 1000000);
    
    // Verify that the second run is significantly faster due to caching
    expect(secondRunExecutionTimeMs).toBeLessThan(firstRunExecutionTimeMs * 0.7); // At least 30% faster
  });
});