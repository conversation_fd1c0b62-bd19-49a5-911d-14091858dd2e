/**
 * Unit tests for price extraction utility functions
 */

const {
  extractNumericPrice,
  parseEuropeanNumber,
  isValidPrice,
  formatPrice
} = require('../../utils/priceExtraction');

describe('Price Extraction Utility', () => {
  
  describe('extractNumericPrice', () => {
    
    describe('Basic price formats', () => {
      test('should extract price from standard euro format', () => {
        expect(extractNumericPrice('€ 2.850')).toBe(2850);
        expect(extractNumericPrice('€2.850')).toBe(2850);
        expect(extractNumericPrice('€ 2850')).toBe(2850);
      });

      test('should extract price with "per maand" suffix', () => {
        expect(extractNumericPrice('€ 2.850 per maand')).toBe(2850);
        expect(extractNumericPrice('€2.850 per maand')).toBe(2850);
        expect(extractNumericPrice('€ 2850 per maand')).toBe(2850);
      });

      test('should extract price with comma as thousands separator', () => {
        expect(extractNumericPrice('€ 2,850')).toBe(2850);
        expect(extractNumericPrice('€2,850')).toBe(2850);
      });
    });

    describe('European number formats', () => {
      test('should handle dot as thousands separator', () => {
        expect(extractNumericPrice('€ 1.500')).toBe(1500);
        expect(extractNumericPrice('€ 12.500')).toBe(12500);
        expect(extractNumericPrice('€ 125.000')).toBe(125000);
      });

      test('should handle comma as decimal separator', () => {
        expect(extractNumericPrice('€ 1.500,50')).toBe(1500.5);
        expect(extractNumericPrice('€ 2.850,75')).toBe(2850.75);
        expect(extractNumericPrice('€ 950,25')).toBe(950.25);
      });

      test('should handle mixed separators correctly', () => {
        expect(extractNumericPrice('€ 1.234.567,89')).toBe(1234567.89);
        expect(extractNumericPrice('€ 12.345,50')).toBe(12345.5);
        expect(extractNumericPrice('€ 1.500,00')).toBe(1500);
      });

      test('should handle comma as thousands separator when appropriate', () => {
        expect(extractNumericPrice('€ 1,500')).toBe(1500); // thousands
        expect(extractNumericPrice('€ 12,500')).toBe(12500); // thousands
        expect(extractNumericPrice('€ 1,5')).toBe(1.5); // decimal
      });
    });

    describe('Edge cases and malformed strings', () => {
      test('should handle null and undefined values', () => {
        expect(extractNumericPrice(null)).toBe(0);
        expect(extractNumericPrice(undefined)).toBe(0);
        expect(extractNumericPrice('')).toBe(0);
      });

      test('should handle numeric input', () => {
        expect(extractNumericPrice(2850)).toBe(2850);
        expect(extractNumericPrice(2850.5)).toBe(2850.5);
        expect(extractNumericPrice(0)).toBe(0);
      });

      test('should handle NaN input', () => {
        expect(extractNumericPrice(NaN)).toBe(0);
      });

      test('should handle special price indicators', () => {
        expect(extractNumericPrice('Prijs op aanvraag')).toBe(0);
        expect(extractNumericPrice('Op aanvraag')).toBe(0);
        expect(extractNumericPrice('n.o.t.k.')).toBe(0);
        expect(extractNumericPrice('NOTK')).toBe(0);
        expect(extractNumericPrice('Price on request')).toBe(0);
        expect(extractNumericPrice('POA')).toBe(0);
      });

      test('should handle strings without numeric values', () => {
        expect(extractNumericPrice('No price available')).toBe(0);
        expect(extractNumericPrice('Contact for pricing')).toBe(0);
        expect(extractNumericPrice('TBD')).toBe(0);
      });

      test('should handle malformed price strings', () => {
        expect(extractNumericPrice('€ abc')).toBe(0);
        expect(extractNumericPrice('€ ')).toBe(0);
        expect(extractNumericPrice('€')).toBe(0);
        expect(extractNumericPrice('price: unknown')).toBe(0);
      });

      test('should handle prices with trailing characters', () => {
        expect(extractNumericPrice('€ 2.850,-')).toBe(2850);
        expect(extractNumericPrice('€ 2.850,--')).toBe(2850);
        expect(extractNumericPrice('€ 2.850,00')).toBe(2850);
      });
    });

    describe('Various price formats from real data', () => {
      test('should handle Funda-style prices', () => {
        expect(extractNumericPrice('€ 1.500 per maand')).toBe(1500);
        expect(extractNumericPrice('€ 2.850 per maand')).toBe(2850);
        expect(extractNumericPrice('€ 950 per maand')).toBe(950);
      });

      test('should handle Pararius-style prices', () => {
        expect(extractNumericPrice('€ 1.200 per month')).toBe(1200);
        expect(extractNumericPrice('€1200 per month')).toBe(1200);
      });

      test('should handle Huurwoningen-style prices', () => {
        expect(extractNumericPrice('€ 850,- per maand')).toBe(850);
        expect(extractNumericPrice('€850 p.m.')).toBe(850);
      });

      test('should extract from complex strings', () => {
        expect(extractNumericPrice('Huurprijs: € 1.750 per maand')).toBe(1750);
        expect(extractNumericPrice('Rental price € 2.200 monthly')).toBe(2200);
        expect(extractNumericPrice('Price: € 1.500,50 per month')).toBe(1500.5);
      });
    });
  });

  describe('parseEuropeanNumber', () => {
    
    test('should parse numbers with dot as thousands separator', () => {
      expect(parseEuropeanNumber('2.850')).toBe(2850);
      expect(parseEuropeanNumber('12.500')).toBe(12500);
      expect(parseEuropeanNumber('1.234.567')).toBe(1234567);
    });

    test('should parse numbers with comma as decimal separator', () => {
      expect(parseEuropeanNumber('2.850,50')).toBe(2850.5);
      expect(parseEuropeanNumber('1.500,75')).toBe(1500.75);
      expect(parseEuropeanNumber('950,25')).toBe(950.25);
    });

    test('should parse numbers with comma as thousands separator', () => {
      expect(parseEuropeanNumber('2,850')).toBe(2850);
      expect(parseEuropeanNumber('12,500')).toBe(12500);
    });

    test('should handle decimal-only numbers', () => {
      expect(parseEuropeanNumber('2.5')).toBe(2.5);
      expect(parseEuropeanNumber('1,5')).toBe(1.5);
    });

    test('should handle plain numbers', () => {
      expect(parseEuropeanNumber('2850')).toBe(2850);
      expect(parseEuropeanNumber('950')).toBe(950);
    });

    test('should handle empty or invalid input', () => {
      expect(parseEuropeanNumber('')).toBe(0);
      expect(parseEuropeanNumber(null)).toBe(0);
      expect(parseEuropeanNumber(undefined)).toBe(0);
      expect(parseEuropeanNumber('abc')).toBe(0);
    });

    test('should handle trailing dashes', () => {
      expect(parseEuropeanNumber('2.850,-')).toBe(2850);
      expect(parseEuropeanNumber('1.500,--')).toBe(1500);
    });
  });

  describe('isValidPrice', () => {
    
    test('should return true for valid prices', () => {
      expect(isValidPrice('€ 2.850')).toBe(true);
      expect(isValidPrice('€ 1.500 per maand')).toBe(true);
      expect(isValidPrice(2850)).toBe(true);
      expect(isValidPrice('€ 950,50')).toBe(true);
    });

    test('should return false for invalid prices', () => {
      expect(isValidPrice('Prijs op aanvraag')).toBe(false);
      expect(isValidPrice('n.o.t.k.')).toBe(false);
      expect(isValidPrice(null)).toBe(false);
      expect(isValidPrice(undefined)).toBe(false);
      expect(isValidPrice('')).toBe(false);
      expect(isValidPrice(0)).toBe(false);
      expect(isValidPrice('No price')).toBe(false);
    });
  });

  describe('formatPrice', () => {
    
    test('should format numeric prices correctly', () => {
      expect(formatPrice(2850)).toBe('€ 2.850');
      expect(formatPrice(1500)).toBe('€ 1.500');
      expect(formatPrice(950)).toBe('€ 950');
      expect(formatPrice(12500)).toBe('€ 12.500');
    });

    test('should include "per maand" when requested', () => {
      expect(formatPrice(2850, true)).toBe('€ 2.850 per maand');
      expect(formatPrice(1500, true)).toBe('€ 1.500 per maand');
    });

    test('should handle invalid input', () => {
      expect(formatPrice(NaN)).toBe('€ 0');
      expect(formatPrice(null)).toBe('€ 0');
      expect(formatPrice(undefined)).toBe('€ 0');
      expect(formatPrice('invalid')).toBe('€ 0');
    });

    test('should handle zero and negative values', () => {
      expect(formatPrice(0)).toBe('€ 0');
      expect(formatPrice(-100)).toBe('€ -100');
    });

    test('should handle large numbers', () => {
      expect(formatPrice(1234567)).toBe('€ 1.234.567');
      expect(formatPrice(999999)).toBe('€ 999.999');
    });
  });

  describe('Integration tests with real-world data', () => {
    
    const realWorldPrices = [
      { input: '€ 1.500 per maand', expected: 1500 },
      { input: '€ 2.850 per maand', expected: 2850 },
      { input: '€ 950 per maand', expected: 950 },
      { input: '€ 1.200 per month', expected: 1200 },
      { input: '€1200 per month', expected: 1200 },
      { input: '€ 850,- per maand', expected: 850 },
      { input: '€850 p.m.', expected: 850 },
      { input: 'Huurprijs: € 1.750 per maand', expected: 1750 },
      { input: '€ 2.200 monthly', expected: 2200 },
      { input: '€ 1.500,50 per month', expected: 1500.5 },
      { input: 'Prijs op aanvraag', expected: 0 },
      { input: 'n.o.t.k.', expected: 0 }
    ];

    test('should correctly extract prices from real-world examples', () => {
      realWorldPrices.forEach(({ input, expected }) => {
        expect(extractNumericPrice(input)).toBe(expected);
      });
    });

    test('should validate real-world prices correctly', () => {
      const validPrices = realWorldPrices.filter(p => p.expected > 0);
      const invalidPrices = realWorldPrices.filter(p => p.expected === 0);

      validPrices.forEach(({ input }) => {
        expect(isValidPrice(input)).toBe(true);
      });

      invalidPrices.forEach(({ input }) => {
        expect(isValidPrice(input)).toBe(false);
      });
    });
  });
});