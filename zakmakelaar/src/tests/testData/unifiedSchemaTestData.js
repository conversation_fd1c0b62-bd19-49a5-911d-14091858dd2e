/**
 * Unified Schema Test Data
 * 
 * This file contains test data sets for all scrapers with various scenarios:
 * - Minimal valid data
 * - Complete valid data
 * - Edge cases
 * - Invalid data
 */

// Common test data structure for all sources
const createTestDataSets = (source) => ({
  minimal: {
    title: `Minimal ${source} Property`,
    source: source,
    url: `https://www.${source}/property/12345`,
    location: 'Amsterdam, Netherlands',
    price: 1500,
    propertyType: source === 'funda.nl' ? 'apartment' : 
                  source === 'huurwoningen.nl' ? 'studio' : 'house'
  },
  complete: {
    title: `Complete ${source} Property`,
    description: `A detailed description of a property from ${source}`,
    source: source,
    url: `https://www.${source}/property/12345`,
    dateAdded: new Date().toISOString(),
    location: {
      _unified: {
        address: {
          street: 'Prinsengracht',
          houseNumber: '123',
          postalCode: '1015 AB',
          city: 'Amsterdam',
          province: 'Noord-Holland',
          country: 'Netherlands'
        },
        coordinates: {
          lat: 52.3676,
          lng: 4.9041
        }
      },
      _legacy: 'Prinsengracht 123, 1015 AB Amsterdam'
    },
    propertyType: source === 'funda.nl' ? 'apartment' : 
                  source === 'huurwoningen.nl' ? 'studio' : 'house',
    size: '85 m²',
    area: 85,
    rooms: '3',
    bedrooms: 2,
    bathrooms: '1',
    year: '2010',
    price: 1500,
    interior: 'Gemeubileerd',
    furnished: true,
    pets: false,
    smoking: false,
    garden: true,
    balcony: true,
    parking: false,
    energyLabel: 'A',
    images: [
      `https://www.${source}/images/image1.jpg`,
      `https://www.${source}/images/image2.jpg`,
      `https://www.${source}/images/image3.jpg`
    ],
    isActive: true,
    features: ['Dishwasher', 'Washing Machine', 'Central Heating'],
    deposit: 1500,
    utilities: 150,
    dateAvailable: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString(),
    contactInfo: {
      name: `${source} Agent`,
      phone: '0612345678',
      email: `agent@${source}`
    }
  },
  edge_cases: {
    title: `Edge Case ${source} Property`,
    description: 'A'.repeat(4999), // Max length - 1
    source: source,
    url: `https://www.${source}/property/${'very-long-id-'.repeat(10)}`,
    location: 'A'.repeat(199), // Max length - 1
    propertyType: source === 'funda.nl' ? 'apartment' : 'woning',
    size: '999 m²',
    area: 999,
    rooms: '20',
    bedrooms: 15,
    bathrooms: '10',
    year: new Date().getFullYear().toString(),
    price: 49999, // Just under max
    interior: 'Kaal',
    images: Array(50).fill().map((_, i) => `https://www.${source}/images/image${i}.jpg`),
    features: Array(20).fill().map((_, i) => `Feature ${i}`),
    deposit: 99999,
    utilities: 999
  },
  invalid: {
    // Missing required fields
    description: `Invalid ${source} property`,
    propertyType: 'invalid-type',
    size: -50,
    area: 9, // Too small
    rooms: -1,
    bedrooms: 100, // Too many
    year: '1700', // Too old
    price: -100,
    interior: 'invalid-interior',
    energyLabel: 'H', // Invalid label
    images: 'not-an-array',
    features: ['A'.repeat(100)], // Too long feature
    deposit: -500,
    utilities: -50,
    contactInfo: 'not-an-object'
  }
});

// Create test data sets for each source
const testDataSets = {
  funda: createTestDataSets('funda.nl'),
  huurwoningen: createTestDataSets('huurwoningen.nl'),
  pararius: createTestDataSets('pararius.nl')
};

// Raw scraper data examples (closer to actual scraper output)
const rawScraperData = {
  funda: {
    title: 'Appartement te huur: Prinsengracht 123, Amsterdam',
    description: 'Mooi appartement in het centrum van Amsterdam',
    url: 'https://www.funda.nl/huur/amsterdam/appartement-12345678/',
    location: 'Amsterdam, Noord-Holland',
    price: '€ 1.500 per maand',
    propertyType: 'appartement',
    size: '85 m²',
    rooms: '3',
    bedrooms: '2',
    year: '2010',
    interior: 'Gemeubileerd',
    source: 'funda.nl',
    images: [
      'https://cloud.funda.nl/image1.jpg',
      'https://cloud.funda.nl/image2.jpg'
    ],
    // Funda-specific fields
    energyLabel: 'A',
    serviceKosten: '€ 50 per maand',
    beschikbaarDatum: '01-07-2023',
    buitenruimte: 'Balkon',
    parkeren: 'Geen',
    verwarming: 'CV-ketel',
    isolatie: 'Volledig geïsoleerd'
  },
  huurwoningen: {
    title: 'Studio te huur in Rotterdam',
    description: 'Modern studio appartement nabij Rotterdam Centraal',
    url: 'https://www.huurwoningen.nl/huren/rotterdam/studio-12345/',
    location: 'Rotterdam, Zuid-Holland',
    price: '€ 950 per maand',
    propertyType: 'studio',
    size: '45 m²',
    rooms: '1',
    bedrooms: '1',
    year: '2015',
    interior: 'Gestoffeerd',
    source: 'huurwoningen.nl',
    images: [
      'https://www.huurwoningen.nl/images/image3.jpg',
      'https://www.huurwoningen.nl/images/image4.jpg'
    ],
    // Huurwoningen-specific fields
    oppervlakte: '45 m²',
    huurprijs: '€ 950 per maand',
    beschikbaarVanaf: '01-08-2023',
    huisdierenToegstaan: 'Nee',
    rokenToegstaan: 'Nee',
    waarborgsom: '€ 950',
    inclusief: 'Internet, Water'
  },
  pararius: {
    title: 'Huis te huur: Utrechtsestraat 45, Utrecht',
    description: 'Ruim gezinshuis met tuin',
    url: 'https://www.pararius.nl/huurwoningen/utrecht/huis-12345/',
    location: 'Utrecht, Utrecht',
    price: '€ 2.200 per maand',
    propertyType: 'huis',
    size: '120 m²',
    rooms: '5',
    bedrooms: '3',
    bathrooms: '2',
    year: '2005',
    interior: 'Kaal',
    source: 'pararius.nl',
    images: [
      'https://www.pararius.nl/images/image5.jpg',
      'https://www.pararius.nl/images/image6.jpg'
    ],
    // Pararius-specific fields
    woonoppervlakte: '120 m²',
    huurprijs: '€ 2.200 p/m',
    beschikbaarVanaf: 'Direct',
    tuin: 'Ja',
    balkon: 'Nee',
    parkeren: 'Ja',
    energielabel: 'C'
  }
};

module.exports = {
  testDataSets,
  rawScraperData
};