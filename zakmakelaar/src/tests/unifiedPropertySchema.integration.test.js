/**
 * Integration Tests for Unified Property Schema
 * Tests with real-world data scenarios and edge cases
 */

const {
  validateProperty,
  validatePropertyStrict,
  createMinimalProperty,
  getSchema,
  getValidationOptions
} = require('../schemas/unifiedPropertySchema');

describe('Unified Property Schema - Integration Tests', () => {
  describe('Real-world data scenarios', () => {
    test('should validate typical Funda property data', () => {
      const fundaProperty = {
        title: 'Ruim 3-kamer appartement met balkon in Amsterdam',
        description: 'Prachtig gelegen 3-kamer appartement van ca. 85 m² met een zonnig balkon op het zuidwesten.',
        source: 'funda.nl',
        url: 'https://www.funda.nl/huur/amsterdam/appartement-12345678/',
        location: 'Amsterdam, Noord-Holland',
        propertyType: 'appartement',
        size: '85 m²',
        area: 85,
        rooms: '3',
        bedrooms: '2',
        bathrooms: '1',
        year: '2015',
        price: '€ 1.850 per maand',
        interior: 'Gestoffeerd',
        furnished: false,
        pets: false,
        smoking: false,
        garden: false,
        balcony: true,
        parking: false,
        energyLabel: 'B',
        images: [
          'https://cloud.funda.nl/valentina_media/123/456/789_1440x960.jpg',
          'https://cloud.funda.nl/valentina_media/123/456/790_1440x960.jpg'
        ],
        isActive: true,
        features: ['balcony', 'central-heating'],
        deposit: 3700,
        utilities: 125,
        dateAvailable: '2024-02-01T00:00:00.000Z',
        contactInfo: {
          name: 'Makelaar Amsterdam',
          phone: '+31 20 123 4567',
          email: '<EMAIL>'
        }
      };

      const result = validateProperty(fundaProperty);
      expect(result.error).toBeUndefined();
      expect(result.value.title).toBe(fundaProperty.title);
      expect(result.value.source).toBe('funda.nl');
      expect(result.value.propertyType).toBe('appartement');
      expect(result.value.balcony).toBe(true);
    });

    test('should validate typical Huurwoningen property data', () => {
      const huurwoningenProperty = {
        title: 'Moderne studio in het centrum van Rotterdam',
        description: 'Volledig gemeubileerde studio van 45 m² in het hart van Rotterdam.',
        source: 'huurwoningen.nl',
        url: 'https://www.huurwoningen.nl/huurwoning/rotterdam/studio-87654321/',
        location: {
          _unified: {
            address: {
              street: 'Coolsingel',
              houseNumber: '123A',
              postalCode: '3012 AC',
              city: 'Rotterdam',
              province: 'Zuid-Holland',
              country: 'Netherlands'
            },
            coordinates: {
              lat: 51.9244,
              lng: 4.4777
            }
          },
          _legacy: 'Rotterdam, Zuid-Holland'
        },
        propertyType: 'studio',
        size: '45 m²',
        area: 45,
        rooms: 1,
        bedrooms: 1,
        bathrooms: 1,
        year: '2020',
        price: 1200,
        interior: 'Gemeubileerd',
        furnished: true,
        pets: false,
        smoking: false,
        garden: false,
        balcony: false,
        parking: true,
        energyLabel: 'A',
        images: [
          'https://images.huurwoningen.nl/property/123456/image1.jpg'
        ],
        isActive: true,
        features: ['furnished', 'parking'],
        deposit: 2400,
        utilities: 100,
        contactInfo: {
          name: 'Verhuurder Rotterdam',
          email: '<EMAIL>'
        }
      };

      const result = validateProperty(huurwoningenProperty);
      expect(result.error).toBeUndefined();
      expect(result.value.source).toBe('huurwoningen.nl');
      expect(result.value.propertyType).toBe('studio');
      expect(result.value.furnished).toBe(true);
      expect(result.value.location._unified.address.city).toBe('Rotterdam');
    });

    test('should validate typical Pararius property data', () => {
      const parariusProperty = {
        title: 'Spacious family house with garden in Utrecht',
        description: 'Beautiful 4-bedroom house with large garden, perfect for families.',
        source: 'pararius.nl',
        url: 'https://www.pararius.nl/rental-property/utrecht/house-11223344',
        location: 'Utrecht, Utrecht',
        propertyType: 'house',
        size: '120 m²',
        area: 120,
        rooms: '5',
        bedrooms: '4',
        bathrooms: '2',
        year: '1995',
        price: '€ 2.200 per month',
        interior: 'Kaal',
        furnished: false,
        pets: true,
        smoking: false,
        garden: true,
        balcony: false,
        parking: true,
        energyLabel: 'C',
        images: [
          'https://images.pararius.nl/property/456789/exterior.jpg',
          'https://images.pararius.nl/property/456789/living-room.jpg',
          'https://images.pararius.nl/property/456789/garden.jpg'
        ],
        isActive: true,
        features: ['garden', 'parking', 'pets-allowed'],
        deposit: 4400,
        utilities: 200,
        dateAvailable: '2024-03-15T00:00:00.000Z',
        contactInfo: {
          name: 'Utrecht Housing',
          phone: '+31 30 987 6543',
          email: '<EMAIL>'
        }
      };

      const result = validateProperty(parariusProperty);
      expect(result.error).toBeUndefined();
      expect(result.value.source).toBe('pararius.nl');
      expect(result.value.propertyType).toBe('house');
      expect(result.value.pets).toBe(true);
      expect(result.value.garden).toBe(true);
    });
  });

  describe('Edge cases and data quality', () => {
    test('should handle missing optional fields gracefully', () => {
      const minimalProperty = {
        title: 'Basic Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: 'Amsterdam',
        price: 1500
      };

      const result = validateProperty(minimalProperty);
      expect(result.error).toBeUndefined();
      expect(result.value.propertyType).toBe('woning'); // default
      expect(result.value.bathrooms).toBe('1'); // default
      expect(result.value.isActive).toBe(true); // default
      expect(result.value.images).toEqual([]); // default
    });

    test('should handle very long descriptions', () => {
      const longDescription = 'A'.repeat(4999); // Just under the 5000 limit
      const property = {
        title: 'Property with long description',
        description: longDescription,
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: 'Amsterdam',
        price: 1500
      };

      const result = validateProperty(property);
      expect(result.error).toBeUndefined();
      expect(result.value.description).toBe(longDescription);
    });

    test('should reject description that is too long', () => {
      const tooLongDescription = 'A'.repeat(5001); // Over the 5000 limit
      const property = {
        title: 'Property with too long description',
        description: tooLongDescription,
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: 'Amsterdam',
        price: 1500
      };

      const result = validateProperty(property);
      expect(result.error).toBeDefined();
      expect(result.error.details[0].path).toContain('description');
    });

    test('should handle various price formats', () => {
      const priceFormats = [
        '€ 1500 per maand',
        '€1.500 per maand',
        '1500',
        1500,
        '€ 1,500 per month',
        'Prijs op aanvraag'
      ];

      priceFormats.forEach(price => {
        const property = {
          title: 'Test Property',
          source: 'funda.nl',
          url: 'https://www.funda.nl/test',
          location: 'Amsterdam',
          price: price
        };

        const result = validateProperty(property);
        expect(result.error).toBeUndefined();
        expect(result.value.price).toBe(price);
      });
    });

    test('should handle various room count formats', () => {
      const roomFormats = [
        { rooms: '3', bedrooms: '2' },
        { rooms: 3, bedrooms: 2 },
        { rooms: '3+', bedrooms: '2' },
        { rooms: null, bedrooms: null }
      ];

      roomFormats.forEach(({ rooms, bedrooms }) => {
        const property = {
          title: 'Test Property',
          source: 'funda.nl',
          url: 'https://www.funda.nl/test',
          location: 'Amsterdam',
          price: 1500,
          rooms: rooms,
          bedrooms: bedrooms
        };

        const result = validateProperty(property);
        expect(result.error).toBeUndefined();
        expect(result.value.rooms).toBe(rooms);
        expect(result.value.bedrooms).toBe(bedrooms);
      });
    });

    test('should validate coordinate boundaries', () => {
      const validCoordinates = {
        title: 'Test Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: {
          _unified: {
            address: { city: 'Amsterdam' },
            coordinates: { lat: 52.3676, lng: 4.9041 } // Valid Amsterdam coordinates
          }
        },
        price: 1500
      };

      const result = validateProperty(validCoordinates);
      expect(result.error).toBeUndefined();
    });

    test('should reject invalid coordinates', () => {
      const invalidCoordinates = {
        title: 'Test Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/test',
        location: {
          _unified: {
            address: { city: 'Amsterdam' },
            coordinates: { lat: 95, lng: 200 } // Invalid coordinates
          }
        },
        price: 1500
      };

      const result = validateProperty(invalidCoordinates);
      expect(result.error).toBeDefined();
    });
  });

  describe('Frontend compatibility', () => {
    test('should maintain compatibility with existing frontend data expectations', () => {
      // Simulate data as it would come from existing scrapers
      const legacyFormatProperty = {
        title: 'Legacy Format Property',
        source: 'funda.nl',
        url: 'https://www.funda.nl/legacy',
        location: 'Amsterdam', // Simple string format
        price: '€ 1500 per maand', // String format
        size: '85 m²', // String format
        rooms: '3', // String format
        bedrooms: '2', // String format
        propertyType: 'appartement',
        interior: 'Gestoffeerd',
        images: [
          'https://example.com/image1.jpg',
          'https://example.com/image2.jpg'
        ]
      };

      const result = validateProperty(legacyFormatProperty);
      expect(result.error).toBeUndefined();
      
      // Verify frontend-expected fields are present
      expect(result.value.title).toBeDefined();
      expect(result.value.price).toBeDefined();
      expect(result.value.location).toBeDefined();
      expect(result.value.propertyType).toBeDefined();
      expect(result.value.images).toBeInstanceOf(Array);
      expect(result.value.isActive).toBe(true);
    });
  });

  describe('Performance validation', () => {
    test('should validate properties quickly', () => {
      const property = createMinimalProperty();
      
      const startTime = Date.now();
      const result = validateProperty(property);
      const endTime = Date.now();
      
      expect(result.error).toBeUndefined();
      expect(endTime - startTime).toBeLessThan(10); // Should complete in less than 10ms
    });

    test('should handle batch validation efficiently', () => {
      const properties = Array.from({ length: 100 }, (_, i) => 
        createMinimalProperty({ title: `Property ${i}` })
      );
      
      const startTime = Date.now();
      const results = properties.map(property => validateProperty(property));
      const endTime = Date.now();
      
      expect(results.every(result => !result.error)).toBe(true);
      expect(endTime - startTime).toBeLessThan(100); // Should complete 100 validations in less than 100ms
    });
  });
});