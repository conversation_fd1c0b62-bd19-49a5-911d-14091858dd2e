const request = require('supertest');
const express = require('express');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const path = require('path');
const fs = require('fs');

const documentRoutes = require('../routes/documents');
const auth = require('../middleware/auth');
const { globalErrorHandler } = require('../middleware/errorHandler');
const User = require('../models/User');
const Document = require('../models/Document');
const config = require('../config/config');

// Mock the services
jest.mock('../services/documentVaultService', () => ({
  uploadDocument: jest.fn(),
  getDocuments: jest.fn(),
  downloadDocument: jest.fn(),
  deleteDocument: jest.fn(),
  generateDocumentReport: jest.fn(),
  verifyDocument: jest.fn(),
  getUnverifiedDocuments: jest.fn(),
  getUploadMiddleware: jest.fn().mockReturnValue({
    array: () => (req, res, next) => {
      // Mock multer middleware
      req.files = [{
        filename: 'test-doc.pdf',
        originalname: 'document.pdf',
        path: '/tmp/test-doc.pdf',
        size: 1024000,
        mimetype: 'application/pdf'
      }];
      next();
    }
  })
}));
jest.mock('../models/User');
jest.mock('../models/Document');

const documentVaultService = require('../services/documentVaultService');

describe('Document Routes Integration Tests', () => {
  let app;
  let mockUser;
  let mockAdmin;
  let userToken;
  let adminToken;

  beforeAll(() => {
    // Create Express app for testing
    app = express();
    app.use(express.json());
    app.use('/api/documents', documentRoutes);
    app.use(globalErrorHandler);

    // Create mock users
    mockUser = {
      _id: new mongoose.Types.ObjectId(),
      email: '<EMAIL>',
      role: 'user',
      profile: {
        firstName: 'John',
        lastName: 'Doe'
      }
    };

    mockAdmin = {
      _id: new mongoose.Types.ObjectId(),
      email: '<EMAIL>',
      role: 'admin',
      profile: {
        firstName: 'Admin',
        lastName: 'User'
      }
    };

    // Generate tokens
    userToken = jwt.sign({ _id: mockUser._id }, config.jwtSecret);
    adminToken = jwt.sign({ _id: mockAdmin._id }, config.jwtSecret);
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock User.findOne for auth middleware
    User.findOne.mockImplementation(({ _id }) => {
      if (_id.toString() === mockUser._id.toString()) {
        return Promise.resolve(mockUser);
      }
      if (_id.toString() === mockAdmin._id.toString()) {
        return Promise.resolve(mockAdmin);
      }
      return Promise.resolve(null);
    });

    // Reset document vault service mocks - they're already mocked in the jest.mock() call
  });

  describe('POST /api/documents/upload', () => {
    it('should upload documents successfully', async () => {
      const mockUploadResult = {
        id: 'doc123',
        filename: 'document.pdf',
        type: 'income_proof',
        size: 1024000,
        verified: false
      };

      documentVaultService.uploadDocument.mockResolvedValue(mockUploadResult);

      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${userToken}`)
        .field('type', 'income_proof')
        .attach('documents', Buffer.from('fake pdf content'), 'document.pdf');

      expect(response.status).toBe(201);
      expect(response.body.status).toBe('success');
      expect(response.body.data.documents).toHaveLength(1);
      expect(response.body.data.documents[0]).toEqual(mockUploadResult);
      expect(documentVaultService.uploadDocument).toHaveBeenCalledWith(
        mockUser._id,
        expect.any(Object),
        'income_proof',
        expect.objectContaining({
          ipAddress: expect.any(String)
        })
      );
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .post('/api/documents/upload')
        .field('type', 'income_proof');

      expect(response.status).toBe(401);
    });

    it('should require document type', async () => {
      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${userToken}`)
        .attach('documents', Buffer.from('fake pdf content'), 'document.pdf');

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Document type is required');
    });

    it('should require files to be uploaded', async () => {
      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${userToken}`)
        .field('type', 'income_proof');

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('No files uploaded');
    });

    it('should handle upload errors gracefully', async () => {
      documentVaultService.uploadDocument.mockRejectedValue(new Error('Upload failed'));

      const response = await request(app)
        .post('/api/documents/upload')
        .set('Authorization', `Bearer ${userToken}`)
        .field('type', 'income_proof')
        .attach('documents', Buffer.from('fake pdf content'), 'document.pdf');

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('All uploads failed');
    });
  });

  describe('GET /api/documents', () => {
    it('should get user documents successfully', async () => {
      const mockDocuments = [
        {
          id: 'doc1',
          filename: 'document1.pdf',
          type: 'income_proof',
          verified: true
        },
        {
          id: 'doc2',
          filename: 'document2.pdf',
          type: 'bank_statement',
          verified: false
        }
      ];

      documentVaultService.getDocuments.mockResolvedValue(mockDocuments);

      const response = await request(app)
        .get('/api/documents')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.documents).toEqual(mockDocuments);
      expect(response.body.data.count).toBe(2);
      expect(documentVaultService.getDocuments).toHaveBeenCalledWith(mockUser._id, undefined);
    });

    it('should filter documents by type', async () => {
      documentVaultService.getDocuments.mockResolvedValue([]);

      const response = await request(app)
        .get('/api/documents?type=income_proof')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(documentVaultService.getDocuments).toHaveBeenCalledWith(mockUser._id, 'income_proof');
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/documents');

      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/documents/:documentId/download', () => {
    it('should download document successfully', async () => {
      const mockFileData = {
        data: Buffer.from('file content'),
        filename: 'document.pdf',
        mimeType: 'application/pdf',
        size: 1024
      };

      documentVaultService.downloadDocument.mockResolvedValue(mockFileData);

      const response = await request(app)
        .get('/api/documents/doc123/download')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toBe('application/pdf');
      expect(response.headers['content-disposition']).toBe('attachment; filename="document.pdf"');
      expect(documentVaultService.downloadDocument).toHaveBeenCalledWith(
        'doc123',
        mockUser._id,
        'user',
        expect.any(String)
      );
    });

    it('should handle access denied', async () => {
      documentVaultService.downloadDocument.mockRejectedValue(new Error('Access denied'));

      const response = await request(app)
        .get('/api/documents/doc123/download')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(500);
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/documents/doc123/download');

      expect(response.status).toBe(401);
    });
  });

  describe('DELETE /api/documents/:documentId', () => {
    it('should delete document successfully', async () => {
      documentVaultService.deleteDocument.mockResolvedValue(true);

      const response = await request(app)
        .delete('/api/documents/doc123')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Document deleted successfully');
      expect(documentVaultService.deleteDocument).toHaveBeenCalledWith(
        'doc123',
        mockUser._id,
        'user',
        expect.any(String)
      );
    });

    it('should handle deletion errors', async () => {
      documentVaultService.deleteDocument.mockRejectedValue(new Error('Document not found'));

      const response = await request(app)
        .delete('/api/documents/doc123')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(500);
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .delete('/api/documents/doc123');

      expect(response.status).toBe(401);
    });
  });

  describe('GET /api/documents/report', () => {
    it('should generate document report successfully', async () => {
      const mockReport = {
        userId: mockUser._id,
        totalDocuments: 5,
        verifiedDocuments: 3,
        verificationPercentage: 60
      };

      documentVaultService.generateDocumentReport.mockResolvedValue(mockReport);

      const response = await request(app)
        .get('/api/documents/report')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.report).toEqual(mockReport);
      expect(documentVaultService.generateDocumentReport).toHaveBeenCalledWith(mockUser._id);
    });

    it('should require authentication', async () => {
      const response = await request(app)
        .get('/api/documents/report');

      expect(response.status).toBe(401);
    });
  });

  describe('Admin Routes', () => {
    describe('GET /api/documents/admin/unverified', () => {
      it('should get unverified documents for admin', async () => {
        const mockUnverifiedDocs = [
          {
            id: 'doc1',
            userId: 'user1',
            filename: 'document1.pdf',
            type: 'income_proof'
          }
        ];

        documentVaultService.getUnverifiedDocuments.mockResolvedValue(mockUnverifiedDocs);

        const response = await request(app)
          .get('/api/documents/admin/unverified')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.status).toBe('success');
        expect(response.body.data.documents).toEqual(mockUnverifiedDocs);
        expect(documentVaultService.getUnverifiedDocuments).toHaveBeenCalledWith(50);
      });

      it('should respect limit parameter', async () => {
        documentVaultService.getUnverifiedDocuments.mockResolvedValue([]);

        const response = await request(app)
          .get('/api/documents/admin/unverified?limit=10')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(documentVaultService.getUnverifiedDocuments).toHaveBeenCalledWith(10);
      });

      it('should require admin role', async () => {
        const response = await request(app)
          .get('/api/documents/admin/unverified')
          .set('Authorization', `Bearer ${userToken}`);

        expect(response.status).toBe(403);
        expect(response.body.message).toBe('Admin access required');
      });

      it('should require authentication', async () => {
        const response = await request(app)
          .get('/api/documents/admin/unverified');

        expect(response.status).toBe(401);
      });
    });

    describe('POST /api/documents/admin/:documentId/verify', () => {
      it('should verify document successfully', async () => {
        const mockVerificationResult = {
          id: 'doc123',
          verified: true,
          verifiedBy: mockAdmin._id,
          verifiedAt: new Date()
        };

        documentVaultService.verifyDocument.mockResolvedValue(mockVerificationResult);

        const response = await request(app)
          .post('/api/documents/admin/doc123/verify')
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            verified: true,
            notes: 'Document looks valid'
          });

        expect(response.status).toBe(200);
        expect(response.body.status).toBe('success');
        expect(response.body.message).toBe('Document verified successfully');
        expect(response.body.data).toEqual(mockVerificationResult);
        expect(documentVaultService.verifyDocument).toHaveBeenCalledWith(
          'doc123',
          mockAdmin._id,
          true,
          'Document looks valid'
        );
      });

      it('should reject document successfully', async () => {
        const mockVerificationResult = {
          id: 'doc123',
          verified: false,
          verifiedBy: mockAdmin._id,
          verifiedAt: new Date()
        };

        documentVaultService.verifyDocument.mockResolvedValue(mockVerificationResult);

        const response = await request(app)
          .post('/api/documents/admin/doc123/verify')
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            verified: false,
            notes: 'Document is invalid'
          });

        expect(response.status).toBe(200);
        expect(response.body.message).toBe('Document rejected successfully');
      });

      it('should require verified boolean', async () => {
        const response = await request(app)
          .post('/api/documents/admin/doc123/verify')
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            verified: 'true', // String instead of boolean
            notes: 'Document looks valid'
          });

        expect(response.status).toBe(400);
        expect(response.body.message).toBe('Verified status must be a boolean');
      });

      it('should require admin role', async () => {
        const response = await request(app)
          .post('/api/documents/admin/doc123/verify')
          .set('Authorization', `Bearer ${userToken}`)
          .send({ verified: true });

        expect(response.status).toBe(403);
        expect(response.body.message).toBe('Admin access required');
      });

      it('should require authentication', async () => {
        const response = await request(app)
          .post('/api/documents/admin/doc123/verify')
          .send({ verified: true });

        expect(response.status).toBe(401);
      });
    });

    describe('GET /api/documents/admin/:documentId/download', () => {
      it('should allow admin to download any document', async () => {
        const mockFileData = {
          data: Buffer.from('file content'),
          filename: 'document.pdf',
          mimeType: 'application/pdf',
          size: 1024
        };

        documentVaultService.downloadDocument.mockResolvedValue(mockFileData);

        const response = await request(app)
          .get('/api/documents/admin/doc123/download')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.headers['content-type']).toBe('application/pdf');
        expect(documentVaultService.downloadDocument).toHaveBeenCalledWith(
          'doc123',
          mockAdmin._id,
          'admin',
          expect.any(String)
        );
      });

      it('should require admin role', async () => {
        const response = await request(app)
          .get('/api/documents/admin/doc123/download')
          .set('Authorization', `Bearer ${userToken}`);

        expect(response.status).toBe(403);
        expect(response.body.message).toBe('Admin access required');
      });

      it('should require authentication', async () => {
        const response = await request(app)
          .get('/api/documents/admin/doc123/download');

        expect(response.status).toBe(401);
      });
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting to upload endpoint', async () => {
      // This test would require actual rate limiting implementation
      // For now, we just verify the middleware is applied
      expect(documentRoutes.stack.some(layer => 
        layer.name === 'rateLimitMiddleware' || 
        layer.handle.name === 'rateLimitMiddleware'
      )).toBeTruthy();
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors gracefully', async () => {
      documentVaultService.getDocuments.mockRejectedValue(new Error('Service unavailable'));

      const response = await request(app)
        .get('/api/documents')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(500);
    });

    it('should handle invalid document IDs', async () => {
      documentVaultService.downloadDocument.mockRejectedValue(new Error('Document not found'));

      const response = await request(app)
        .get('/api/documents/invalid-id/download')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(500);
    });
  });

  describe('Security', () => {
    it('should not allow users to access admin endpoints', async () => {
      const response = await request(app)
        .get('/api/documents/admin/unverified')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
    });

    it('should validate JWT tokens properly', async () => {
      const response = await request(app)
        .get('/api/documents')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
    });

    it('should handle missing authorization header', async () => {
      const response = await request(app)
        .get('/api/documents');

      expect(response.status).toBe(401);
    });
  });
});