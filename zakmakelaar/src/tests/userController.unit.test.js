const userController = require('../controllers/userController');

describe('Enhanced Authentication Controller Methods', () => {
  test('should have completeProfile method', () => {
    expect(typeof userController.completeProfile).toBe('function');
  });

  test('should have updateUserType method', () => {
    expect(typeof userController.updateUserType).toBe('function');
  });

  test('should have setLanguagePreference method', () => {
    expect(typeof userController.setLanguagePreference).toBe('function');
  });

  test('should have uploadProfilePicture method', () => {
    expect(typeof userController.uploadProfilePicture).toBe('function');
  });

  test('should have updateProfilePicture method', () => {
    expect(typeof userController.updateProfilePicture).toBe('function');
  });

  test('should have getProfileStatus method', () => {
    expect(typeof userController.getProfileStatus).toBe('function');
  });

  test('should have updateEmployment method', () => {
    expect(typeof userController.updateEmployment).toBe('function');
  });

  test('should have updateSocialPreferences method', () => {
    expect(typeof userController.updateSocialPreferences).toBe('function');
  });

  test('should have validation middleware', () => {
    const validation = require('../middleware/validation');
    expect(validation.validateProfileCompletion).toBeDefined();
    expect(validation.validateUserType).toBeDefined();
    expect(validation.validateLanguagePreference).toBeDefined();
    expect(validation.validateEmployment).toBeDefined();
    expect(validation.validateSocialPreferences).toBeDefined();
  });

  test('should have routes configured', () => {
    const fs = require('fs');
    const path = require('path');
    const authRoutesPath = path.join(__dirname, '../routes/auth.js');
    const authRoutesContent = fs.readFileSync(authRoutesPath, 'utf8');

    expect(authRoutesContent).toContain('/profile/complete');
    expect(authRoutesContent).toContain('/user-type');
    expect(authRoutesContent).toContain('/language');
    expect(authRoutesContent).toContain('/profile-picture');
    expect(authRoutesContent).toContain('/employment');
    expect(authRoutesContent).toContain('/social-preferences');
  });

  test('should have file upload configuration', () => {
    const fs = require('fs');
    const path = require('path');
    const uploadsDir = path.join(__dirname, '../../uploads/profile-pictures');
    
    expect(fs.existsSync(uploadsDir)).toBe(true);
  });
});