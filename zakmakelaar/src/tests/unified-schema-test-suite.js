/**
 * Unified Schema Test Suite
 * 
 * This file runs all tests for the unified scraping schema.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Define the test files to run
const testFiles = [
  'transformationFunctions.test.js',
  'scraper-to-database.integration.test.js',
  'transformation.performance.test.js',
  'cross-source-consistency.test.js',
  'unifiedPropertySchema.test.js',
  'schemaTransformer.test.js',
  'validationEngine.test.js',
  'fieldMappingRegistry.test.js',
  'transformationIntegration.test.js'
];

// Function to run a single test file
function runTest(testFile) {
  console.log(`\n\n========== Running ${testFile} ==========\n`);
  try {
    execSync(`npx jest ${testFile}`, { stdio: 'inherit' });
    return true;
  } catch (error) {
    console.error(`Error running ${testFile}:`, error.message);
    return false;
  }
}

// Function to run all tests
function runAllTests() {
  console.log('Starting Unified Schema Test Suite...\n');
  
  let passedTests = 0;
  let failedTests = 0;
  
  // Run each test file
  for (const testFile of testFiles) {
    const testPath = path.join(__dirname, testFile);
    
    // Check if the test file exists
    if (fs.existsSync(testPath)) {
      const passed = runTest(testFile);
      if (passed) {
        passedTests++;
      } else {
        failedTests++;
      }
    } else {
      console.warn(`Warning: Test file ${testFile} not found. Skipping...`);
    }
  }
  
  // Print summary
  console.log('\n\n========== Test Suite Summary ==========\n');
  console.log(`Total tests: ${passedTests + failedTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${failedTests}`);
  
  return failedTests === 0;
}

// Run the test suite if this file is executed directly
if (require.main === module) {
  const success = runAllTests();
  process.exit(success ? 0 : 1);
}

module.exports = {
  runAllTests,
  runTest
};