/**
 * Tests for the MigrationService
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const { MigrationService } = require('../services/migrationService');
const { createMinimalProperty } = require('../schemas/unifiedPropertySchema');

// Mock models
const mockListingSchema = new mongoose.Schema({
  title: String,
  price: String,
  location: String,
  url: String,
  size: String,
  bedrooms: String,
  rooms: String,
  propertyType: String,
  description: String,
  year: String,
  interior: String,
  source: String,
  images: [String],
  dateAdded: { type: Date, default: Date.now },
  timestamp: { type: Date, default: Date.now }
});

// Mock Property schema with EnhancedProperty methods
const mockPropertySchema = new mongoose.Schema({
  title: String,
  description: String,
  address: {
    street: String,
    houseNumber: String,
    postalCode: String,
    city: String,
    province: String,
    country: { type: String, default: 'Netherlands' }
  },
  propertyType: String,
  size: Number,
  rooms: Number,
  bedrooms: Number,
  status: { type: String, default: 'draft' },
  owner: {
    userId: mongoose.Schema.Types.ObjectId,
    contactPreference: { type: String, default: 'email' }
  },
  
  // Enhanced fields
  unifiedData: { type: Object, default: {} },
  sourceMetadata: {
    website: String,
    externalId: String,
    scrapedAt: Date,
    lastUpdated: Date,
    version: Number
  },
  dataQuality: {
    completeness: Number,
    accuracy: Number,
    lastValidated: Date,
    validationErrors: [String],
    validationWarnings: [String]
  },
  processingMetadata: {
    transformationVersion: String,
    processingTime: Number,
    processingDate: Date,
    errors: [Object],
    warnings: [Object]
  },
  rawData: {
    original: Object,
    processed: Object,
    metadata: Object
  },
  isLegacyMigrated: { type: Boolean, default: false },
  originalListingId: mongoose.Schema.Types.ObjectId
});

// Add the createFromUnifiedSchema static method
mockPropertySchema.statics.createFromUnifiedSchema = async function(unifiedData) {
  const property = new this();
  
  // Map unified schema fields to property model fields
  if (unifiedData.title) property.title = unifiedData.title;
  if (unifiedData.description) property.description = unifiedData.description;
  
  // Map location data
  if (unifiedData.location) {
    if (typeof unifiedData.location === 'string') {
      property.address = {
        city: unifiedData.location,
        country: 'Netherlands'
      };
    } else if (unifiedData.location._unified && unifiedData.location._unified.address) {
      property.address = {
        street: unifiedData.location._unified.address.street,
        houseNumber: unifiedData.location._unified.address.houseNumber,
        postalCode: unifiedData.location._unified.address.postalCode,
        city: unifiedData.location._unified.address.city,
        province: unifiedData.location._unified.address.province,
        country: unifiedData.location._unified.address.country || 'Netherlands'
      };
    }
  }
  
  // Map property type
  if (unifiedData.propertyType) {
    property.propertyType = unifiedData.propertyType;
  }
  
  // Map size and rooms
  if (unifiedData.area) property.size = unifiedData.area;
  if (unifiedData.rooms) property.rooms = typeof unifiedData.rooms === 'string' ? parseInt(unifiedData.rooms) : unifiedData.rooms;
  if (unifiedData.bedrooms) property.bedrooms = typeof unifiedData.bedrooms === 'string' ? parseInt(unifiedData.bedrooms) : unifiedData.bedrooms;
  
  // Store the complete unified data
  property.unifiedData = unifiedData;
  
  return property;
};

// Mock models
let MockListing;
let MockProperty;
let mongoServer;

describe('MigrationService', () => {
  // Set up the in-memory database and models before tests
  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    
    await mongoose.connect(uri);
    
    MockListing = mongoose.model('MockListing', mockListingSchema);
    MockProperty = mongoose.model('MockProperty', mockPropertySchema);
  });
  
  // Clean up after tests
  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });
  
  // Clear the database before each test
  beforeEach(async () => {
    await MockListing.deleteMany({});
    await MockProperty.deleteMany({});
  });
  
  test('should create a new MigrationService instance', () => {
    const migrationService = new MigrationService(MockListing, MockProperty);
    
    expect(migrationService).toBeDefined();
    expect(migrationService.sourceModel).toBe(MockListing);
    expect(migrationService.targetModel).toBe(MockProperty);
    expect(migrationService.options.batchSize).toBe(100);
  });
  
  test('should transform a record', async () => {
    const migrationService = new MigrationService(MockListing, MockProperty);
    
    const sourceRecord = {
      _id: new mongoose.Types.ObjectId(),
      title: 'Test Apartment',
      price: '€ 1.200 per maand',
      location: 'Amsterdam',
      url: 'https://example.com/test',
      size: '80 m²',
      bedrooms: '2',
      rooms: '3',
      propertyType: 'apartment',
      description: 'A nice apartment',
      year: '2010',
      interior: 'Gemeubileerd',
      source: 'funda.nl',
      images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
      dateAdded: new Date('2023-01-01')
    };
    
    const transformedRecord = await migrationService.transformRecord(sourceRecord);
    
    expect(transformedRecord).toBeDefined();
    expect(transformedRecord.title).toBe('Test Apartment');
    expect(transformedRecord.price).toBe('€ 1.200 per maand');
    expect(transformedRecord.location).toBe('Amsterdam');
    expect(transformedRecord.source).toBe('funda.nl');
    expect(transformedRecord.url).toBe('https://example.com/test');
    expect(transformedRecord.propertyType).toBe('apartment');
    expect(transformedRecord.images).toHaveLength(2);
  });
  
  test('should validate a migration', async () => {
    const migrationService = new MigrationService(MockListing, MockProperty);
    
    const sourceRecord = {
      title: 'Test Apartment',
      price: '€ 1.200 per maand',
      location: 'Amsterdam',
      url: 'https://example.com/test',
      source: 'funda.nl'
    };
    
    const transformedRecord = {
      title: 'Test Apartment',
      price: '€ 1.200 per maand',
      location: 'Amsterdam',
      url: 'https://example.com/test',
      source: 'funda.nl'
    };
    
    const validationResult = await migrationService.validateMigration(sourceRecord, transformedRecord);
    
    expect(validationResult.isValid).toBe(true);
    expect(validationResult.errors).toHaveLength(0);
  });
  
  test('should detect validation errors', async () => {
    const migrationService = new MigrationService(MockListing, MockProperty);
    
    const sourceRecord = {
      title: 'Test Apartment',
      price: '€ 1.200 per maand',
      location: 'Amsterdam',
      url: 'https://example.com/test',
      source: 'funda.nl'
    };
    
    const transformedRecord = {
      title: 'Different Title', // Title doesn't match
      price: {}, // Invalid price type
      location: 'Amsterdam',
      url: 'https://example.com/test',
      source: 'funda.nl',
      images: 'not-an-array' // Invalid images type
    };
    
    const validationResult = await migrationService.validateMigration(sourceRecord, transformedRecord);
    
    expect(validationResult.isValid).toBe(false);
    expect(validationResult.errors.length).toBeGreaterThan(0);
    
    // Check for specific errors
    expect(validationResult.errors.some(e => e.field === 'title')).toBe(true);
    expect(validationResult.errors.some(e => e.field === 'price')).toBe(true);
    expect(validationResult.errors.some(e => e.field === 'images')).toBe(true);
  });
  
  test('should create a target record', async () => {
    const migrationService = new MigrationService(MockListing, MockProperty);
    
    const sourceRecord = {
      _id: new mongoose.Types.ObjectId(),
      title: 'Test Apartment',
      price: '€ 1.200 per maand',
      location: 'Amsterdam',
      url: 'https://example.com/test',
      source: 'funda.nl',
      dateAdded: new Date('2023-01-01')
    };
    
    const transformedData = createMinimalProperty({
      title: 'Test Apartment',
      price: '€ 1.200 per maand',
      location: 'Amsterdam',
      url: 'https://example.com/test',
      source: 'funda.nl'
    });
    
    const validationResult = { isValid: true, errors: [] };
    
    const targetRecord = await migrationService.createTargetRecord(
      sourceRecord, 
      transformedData, 
      10, // processingTime
      validationResult
    );
    
    expect(targetRecord).toBeDefined();
    expect(targetRecord.title).toBe('Test Apartment');
    expect(targetRecord.isLegacyMigrated).toBe(true);
    expect(targetRecord.originalListingId).toEqual(sourceRecord._id);
    expect(targetRecord.sourceMetadata.website).toBe('funda.nl');
    expect(targetRecord.dataQuality.completeness).toBeGreaterThan(0);
    expect(targetRecord.processingMetadata.processingTime).toBe(10);
  });
  
  test('should migrate a batch of records', async () => {
    // Create source records
    const sourceRecords = [
      {
        _id: new mongoose.Types.ObjectId(),
        title: 'Apartment 1',
        price: '€ 1.200 per maand',
        location: 'Amsterdam',
        url: 'https://example.com/1',
        source: 'funda.nl',
        dateAdded: new Date('2023-01-01')
      },
      {
        _id: new mongoose.Types.ObjectId(),
        title: 'Apartment 2',
        price: '€ 1.500 per maand',
        location: 'Rotterdam',
        url: 'https://example.com/2',
        source: 'huurwoningen.nl',
        dateAdded: new Date('2023-01-02')
      }
    ];
    
    // Save source records to database
    await MockListing.insertMany(sourceRecords);
    
    // Create migration service
    const migrationService = new MigrationService(MockListing, MockProperty, {
      logProgress: false
    });
    
    // Migrate batch
    const results = await migrationService.migrateBatch(sourceRecords);
    
    // Check results
    expect(results).toHaveLength(2);
    expect(results[0].status).toBe('success');
    expect(results[1].status).toBe('success');
    
    // Check target records in database
    const targetRecords = await MockProperty.find({});
    expect(targetRecords).toHaveLength(2);
    
    // Check migration stats
    const stats = migrationService.getStats();
    expect(stats.processed).toBe(2);
    expect(stats.successful).toBe(2);
    expect(stats.failed).toBe(0);
  });
  
  test('should skip already migrated records', async () => {
    // Create source record
    const sourceRecord = new MockListing({
      title: 'Apartment 1',
      price: '€ 1.200 per maand',
      location: 'Amsterdam',
      url: 'https://example.com/1',
      source: 'funda.nl',
      dateAdded: new Date('2023-01-01')
    });
    await sourceRecord.save();
    
    // Create target record that references the source
    const targetRecord = new MockProperty({
      title: 'Apartment 1',
      isLegacyMigrated: true,
      originalListingId: sourceRecord._id
    });
    await targetRecord.save();
    
    // Create migration service
    const migrationService = new MigrationService(MockListing, MockProperty, {
      logProgress: false
    });
    
    // Migrate batch
    const results = await migrationService.migrateBatch([sourceRecord]);
    
    // Check results
    expect(results).toHaveLength(1);
    expect(results[0].status).toBe('skipped');
    expect(results[0].reason).toBe('already_migrated');
    
    // Check migration stats
    const stats = migrationService.getStats();
    expect(stats.processed).toBe(1);
    expect(stats.skipped).toBe(1);
    expect(stats.successful).toBe(0);
  });
  
  test('should handle validation errors based on validation level', async () => {
    // Create source record with invalid data
    const sourceRecord = new MockListing({
      title: 'Apartment 1',
      // Missing required price field
      location: 'Amsterdam',
      url: 'https://example.com/1',
      source: 'funda.nl'
    });
    await sourceRecord.save();
    
    // Test with standard validation (continue despite errors)
    const standardService = new MigrationService(MockListing, MockProperty, {
      logProgress: false,
      validationLevel: 'standard'
    });
    
    const standardResults = await standardService.migrateBatch([sourceRecord]);
    expect(standardResults[0].status).toBe('success');
    
    await MockProperty.deleteMany({}); // Clean up
    
    // Test with strict validation (fail on errors)
    const strictService = new MigrationService(MockListing, MockProperty, {
      logProgress: false,
      validationLevel: 'strict'
    });
    
    // Mock the validateMigration method to return an error
    strictService.validateMigration = async () => ({
      isValid: false,
      errors: [{ field: 'price', message: 'Required field is missing' }]
    });
    
    const strictResults = await strictService.migrateBatch([sourceRecord]);
    expect(strictResults[0].status).toBe('failed');
    expect(strictResults[0].reason).toBe('validation_error');
    
    // Check migration stats
    const stats = strictService.getStats();
    expect(stats.processed).toBe(1);
    expect(stats.failed).toBe(1);
    expect(stats.validationErrors).toBe(1);
  });
  
  test('should roll back migration', async () => {
    // Create source records
    const sourceRecords = [
      {
        _id: new mongoose.Types.ObjectId(),
        title: 'Apartment 1',
        price: '€ 1.200 per maand',
        location: 'Amsterdam',
        url: 'https://example.com/1',
        source: 'funda.nl'
      },
      {
        _id: new mongoose.Types.ObjectId(),
        title: 'Apartment 2',
        price: '€ 1.500 per maand',
        location: 'Rotterdam',
        url: 'https://example.com/2',
        source: 'huurwoningen.nl'
      }
    ];
    
    // Save source records
    await MockListing.insertMany(sourceRecords);
    
    // Create migration service and migrate records
    const migrationService = new MigrationService(MockListing, MockProperty, {
      logProgress: false
    });
    
    await migrationService.migrateBatch(sourceRecords);
    
    // Verify records were migrated
    const migratedCount = await MockProperty.countDocuments({});
    expect(migratedCount).toBe(2);
    
    // Roll back migration
    await migrationService.rollbackMigration();
    
    // Verify records were deleted
    const remainingCount = await MockProperty.countDocuments({});
    expect(remainingCount).toBe(0);
  });
});