const User = require('../../models/User');

describe('User Model Tests', () => {
  describe('Model Structure', () => {
    test('should have correct schema structure for enhanced profile fields', () => {
      const user = new User({
        email: '<EMAIL>',
        password: 'password123'
      });

      // Test default values
      expect(user.emailVerified).toBe(false);
      expect(user.role).toBe('user');
      expect(user.profile.userType).toEqual([]);
      expect(user.tenantScore.overallScore).toBe(0);
      expect(user.tenantScore.verificationLevel).toBe('unverified');
      expect(user.propertyOwner.isPropertyOwner).toBe(false);
      expect(user.notifications.email.newListings).toBe(true);
      expect(user.aiSettings.tenantScoreVisible).toBe(true);
      expect(user.aiSettings.socialMatchingEnabled).toBe(false);
    });

    test('should accept enhanced profile information', () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        profile: {
          firstName: 'John',
          lastName: 'Doe',
          userType: ['student', 'expat'],
          employment: {
            occupation: 'Software Developer',
            monthlyIncome: 4000,
            incomeVerified: true
          },
          socialPreferences: {
            lookingForRoommate: true,
            roommateCriteria: {
              ageRange: { min: 25, max: 35 },
              gender: 'any'
            },
            isVisible: true
          }
        }
      };

      const user = new User(userData);

      expect(user.profile.firstName).toBe('John');
      expect(user.profile.lastName).toBe('Doe');
      expect(user.profile.userType).toContain('student');
      expect(user.profile.employment.occupation).toBe('Software Developer');
      expect(user.profile.socialPreferences.lookingForRoommate).toBe(true);
    });
  });

  describe('Enhanced Profile Fields', () => {
    test('should accept comprehensive profile information', () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        profile: {
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: new Date('1990-01-01'),
          nationality: 'Dutch',
          phoneNumber: '+31612345678',
          userType: ['student', 'expat'],
          employment: {
            occupation: 'Software Developer',
            employmentType: 'full-time',
            contractType: 'permanent',
            employer: 'Tech Company',
            workLocation: 'Amsterdam',
            monthlyIncome: 4000,
            incomeVerified: true
          },
          rentalHistory: {
            previousAddresses: [{
              address: 'Previous Street 123, Amsterdam',
              landlordName: 'Previous Landlord',
              landlordContact: '<EMAIL>',
              rentAmount: 1200,
              duration: '2 years',
              reasonForLeaving: 'Job relocation'
            }],
            creditScore: 'excellent'
          },
          socialPreferences: {
            lookingForRoommate: true,
            roommateCriteria: {
              ageRange: { min: 25, max: 35 },
              gender: 'any',
              occupation: ['student', 'professional'],
              lifestyle: {
                cleanliness: 'clean',
                noiseLevel: 'quiet',
                socialLevel: 'moderate',
                smokingTolerance: false,
                petTolerance: true,
                guestPolicy: 'moderate'
              }
            },
            isVisible: true
          }
        }
      };

      const user = new User(userData);

      expect(user.profile.firstName).toBe('John');
      expect(user.profile.lastName).toBe('Doe');
      expect(user.profile.userType).toContain('student');
      expect(user.profile.userType).toContain('expat');
      expect(user.profile.employment.occupation).toBe('Software Developer');
      expect(user.profile.employment.monthlyIncome).toBe(4000);
      expect(user.profile.socialPreferences.lookingForRoommate).toBe(true);
      expect(user.profile.socialPreferences.roommateCriteria.ageRange.min).toBe(25);
    });
  });

  describe('Tenant Scoring System', () => {
    test('should initialize tenant score with default values', () => {
      const user = new User({
        email: '<EMAIL>',
        password: 'password123'
      });

      expect(user.tenantScore.overallScore).toBe(0);
      expect(user.tenantScore.components.incomeStability).toBe(0);
      expect(user.tenantScore.components.rentalHistory).toBe(0);
      expect(user.tenantScore.components.creditworthiness).toBe(0);
      expect(user.tenantScore.components.employment).toBe(0);
      expect(user.tenantScore.components.references).toBe(0);
      expect(user.tenantScore.verificationLevel).toBe('unverified');
    });

    test('should accept tenant score components', () => {
      const user = new User({
        email: '<EMAIL>',
        password: 'password123',
        tenantScore: {
          overallScore: 85,
          components: {
            incomeStability: 90,
            rentalHistory: 80,
            creditworthiness: 85,
            employment: 90,
            references: 75
          },
          verificationLevel: 'verified'
        }
      });

      expect(user.tenantScore.overallScore).toBe(85);
      expect(user.tenantScore.components.incomeStability).toBe(90);
      expect(user.tenantScore.verificationLevel).toBe('verified');
    });
  });

  describe('Virtual Fields', () => {
    test('should calculate isProfileComplete virtual field', () => {
      const incompleteUser = new User({
        email: '<EMAIL>',
        password: 'password123'
      });

      const completeUser = new User({
        email: '<EMAIL>',
        password: 'password123',
        profile: {
          firstName: 'Jane',
          lastName: 'Smith',
          userType: ['young_professional'],
          employment: {
            occupation: 'Designer'
          }
        }
      });

      expect(incompleteUser.isProfileComplete).toBe(false);
      expect(completeUser.isProfileComplete).toBe(true);
    });

    test('should calculate tenantScoreGrade virtual field', () => {
      const users = [
        { score: 95, expectedGrade: 'A' },
        { score: 85, expectedGrade: 'B' },
        { score: 75, expectedGrade: 'C' },
        { score: 65, expectedGrade: 'D' },
        { score: 45, expectedGrade: 'F' }
      ];

      for (const { score, expectedGrade } of users) {
        const user = new User({
          email: `grade${score}@example.com`,
          password: 'password123',
          tenantScore: { overallScore: score }
        });

        expect(user.tenantScoreGrade).toBe(expectedGrade);
      }
    });

    test('should calculate fullName virtual field', () => {
      const userWithNames = new User({
        email: '<EMAIL>',
        password: 'password123',
        profile: {
          firstName: 'John',
          lastName: 'Doe'
        }
      });

      const userWithLegacyName = new User({
        email: '<EMAIL>',
        password: 'password123',
        profile: {
          name: 'Jane Smith'
        }
      });

      expect(userWithNames.fullName).toBe('John Doe');
      expect(userWithLegacyName.fullName).toBe('Jane Smith');
    });

    test('should calculate isEligibleForSocialMatching virtual field', () => {
      const eligibleUser = new User({
        email: '<EMAIL>',
        password: 'password123',
        profile: {
          firstName: 'Social',
          lastName: 'User',
          userType: ['student'],
          employment: { occupation: 'Student' },
          socialPreferences: {
            lookingForRoommate: true,
            isVisible: true
          }
        }
      });

      const ineligibleUser = new User({
        email: '<EMAIL>',
        password: 'password123',
        profile: {
          socialPreferences: {
            lookingForRoommate: false,
            isVisible: false
          }
        }
      });

      // First check that profile is complete for eligible user
      expect(eligibleUser.isProfileComplete).toBe(true);
      
      // Then check social matching eligibility
      expect(eligibleUser.isEligibleForSocialMatching).toBe(true);
      expect(ineligibleUser.isEligibleForSocialMatching).toBe(false);
    });
  });

  describe('Document Management', () => {
    test('should accept user documents', () => {
      const user = new User({
        email: '<EMAIL>',
        password: 'password123',
        documents: [{
          id: 'doc123',
          type: 'income_proof',
          filename: 'salary_slip.pdf',
          verified: true,
          expiryDate: new Date('2025-12-31')
        }]
      });

      expect(user.documents).toHaveLength(1);
      expect(user.documents[0].type).toBe('income_proof');
      expect(user.documents[0].filename).toBe('salary_slip.pdf');
      expect(user.documents[0].verified).toBe(true);
    });
  });

  describe('Property Owner Features', () => {
    test('should handle property owner specific fields', () => {
      const propertyOwner = new User({
        email: '<EMAIL>',
        password: 'password123',
        propertyOwner: {
          isPropertyOwner: true,
          properties: ['prop1', 'prop2'],
          verificationStatus: 'verified',
          businessRegistration: 'BUS123456',
          taxNumber: 'TAX789012'
        }
      });

      expect(propertyOwner.propertyOwner.isPropertyOwner).toBe(true);
      expect(propertyOwner.propertyOwner.properties).toHaveLength(2);
      expect(propertyOwner.propertyOwner.verificationStatus).toBe('verified');
    });
  });

  describe('Notification Preferences', () => {
    test('should initialize with default notification preferences', () => {
      const user = new User({
        email: '<EMAIL>',
        password: 'password123'
      });

      expect(user.notifications.email.newListings).toBe(true);
      expect(user.notifications.email.priceChanges).toBe(true);
      expect(user.notifications.sms.urgentAlerts).toBe(false);
      expect(user.notifications.push.newMatches).toBe(true);
    });
  });

  describe('Activity Tracking', () => {
    test('should track user activity', () => {
      const user = new User({
        email: '<EMAIL>',
        password: 'password123',
        activity: {
          loginCount: 5,
          searchHistory: ['Amsterdam', 'Rotterdam'],
          applicationsSent: 3,
          viewingsAttended: 2
        }
      });

      expect(user.activity.loginCount).toBe(5);
      expect(user.activity.searchHistory).toContain('Amsterdam');
      expect(user.activity.applicationsSent).toBe(3);
    });
  });

  describe('AI Settings Enhancement', () => {
    test('should include new AI features in settings', () => {
      const user = new User({
        email: '<EMAIL>',
        password: 'password123',
        aiSettings: {
          tenantScoreVisible: false,
          socialMatchingEnabled: true,
          autoApplyEnabled: true,
          fakeListingDetection: false
        }
      });

      expect(user.aiSettings.tenantScoreVisible).toBe(false);
      expect(user.aiSettings.socialMatchingEnabled).toBe(true);
      expect(user.aiSettings.autoApplyEnabled).toBe(true);
      expect(user.aiSettings.fakeListingDetection).toBe(false);
    });
  });
});