const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const AutoApplicationSettings = require('../../models/AutoApplicationSettings');
const ApplicationQueue = require('../../models/ApplicationQueue');
const ApplicationResult = require('../../models/ApplicationResult');
const User = require('../../models/User');

describe('Auto Application Models', () => {
  let mongoServer;
  let testUserId;

  beforeAll(async () => {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    await mongoose.connect(mongoUri);

    // Create a test user
    const testUser = new User({
      email: '<EMAIL>',
      password: 'testpassword123',
      role: 'user'
    });
    await testUser.save();
    testUserId = testUser._id;
  });

  afterAll(async () => {
    await mongoose.disconnect();
    await mongoServer.stop();
  });

  beforeEach(async () => {
    await AutoApplicationSettings.deleteMany({});
    await ApplicationQueue.deleteMany({});
    await ApplicationResult.deleteMany({});
  });

  describe('AutoApplicationSettings Model', () => {
    test('should create auto application settings with required fields', async () => {
      const settings = new AutoApplicationSettings({
        userId: testUserId,
        enabled: true,
        criteria: {
          maxPrice: 2000,
          minRooms: 2,
          maxRooms: 4,
          propertyTypes: ['apartment', 'house'],
          locations: ['Amsterdam', 'Utrecht']
        },
        personalInfo: {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+31612345678',
          dateOfBirth: new Date('1990-01-01'),
          nationality: 'Dutch',
          occupation: 'Software Engineer',
          employer: 'Tech Company',
          monthlyIncome: 5000,
          moveInDate: new Date('2024-03-01'),
          leaseDuration: 12,
          emergencyContact: {
            name: 'Jane Doe',
            phone: '+31687654321',
            relationship: 'Sister'
          }
        }
      });

      const savedSettings = await settings.save();
      expect(savedSettings._id).toBeDefined();
      expect(savedSettings.userId).toEqual(testUserId);
      expect(savedSettings.enabled).toBe(true);
      expect(savedSettings.criteria.maxPrice).toBe(2000);
      expect(savedSettings.personalInfo.fullName).toBe('John Doe');
    });

    test('should validate required fields', async () => {
      const settings = new AutoApplicationSettings({
        userId: testUserId,
        // Missing required criteria and personalInfo
      });

      await expect(settings.save()).rejects.toThrow();
    });

    test('should calculate virtual fields correctly', async () => {
      const settings = new AutoApplicationSettings({
        userId: testUserId,
        enabled: true,
        criteria: { maxPrice: 2000 },
        personalInfo: {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+31612345678',
          dateOfBirth: new Date('1990-01-01'),
          nationality: 'Dutch',
          occupation: 'Software Engineer',
          employer: 'Tech Company',
          monthlyIncome: 5000,
          moveInDate: new Date('2024-03-01'),
          leaseDuration: 12,
          emergencyContact: {
            name: 'Jane Doe',
            phone: '+31687654321',
            relationship: 'Sister'
          }
        },
        documents: [
          { type: 'income_proof', filename: 'income.pdf', required: true, uploaded: true, verified: true },
          { type: 'id_document', filename: 'id.pdf', required: true, uploaded: true, verified: true }
        ],
        settings: { maxApplicationsPerDay: 5 },
        statistics: { dailyApplicationCount: 2 }
      });

      await settings.save();

      expect(settings.isProfileComplete).toBe(true);
      expect(settings.documentsComplete).toBe(true);
      expect(settings.canAutoApply).toBe(true);
      expect(settings.dailyApplicationsRemaining).toBe(3);
    });

    test('should match listing criteria correctly', async () => {
      const settings = new AutoApplicationSettings({
        userId: testUserId,
        criteria: {
          maxPrice: 2000,
          minRooms: 2,
          maxRooms: 4,
          propertyTypes: ['apartment'],
          locations: ['Amsterdam'],
          excludeKeywords: ['no pets'],
          includeKeywords: ['balcony']
        },
        personalInfo: {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+31612345678',
          dateOfBirth: new Date('1990-01-01'),
          nationality: 'Dutch',
          occupation: 'Software Engineer',
          employer: 'Tech Company',
          monthlyIncome: 5000,
          moveInDate: new Date('2024-03-01'),
          leaseDuration: 12,
          emergencyContact: {
            name: 'Jane Doe',
            phone: '+31687654321',
            relationship: 'Sister'
          }
        }
      });

      await settings.save();

      // Test matching listing
      const matchingListing = {
        price: 1800,
        rooms: 3,
        propertyType: 'apartment',
        location: 'Amsterdam Center',
        title: 'Beautiful apartment with balcony',
        description: 'Nice place with great view'
      };

      expect(settings.matchesCriteria(matchingListing)).toBe(true);

      // Test non-matching listing (too expensive)
      const expensiveListing = {
        ...matchingListing,
        price: 2500
      };

      expect(settings.matchesCriteria(expensiveListing)).toBe(false);

      // Test non-matching listing (excluded keyword)
      const excludedListing = {
        ...matchingListing,
        title: 'Nice apartment - no pets allowed'
      };

      expect(settings.matchesCriteria(excludedListing)).toBe(false);
    });
  });

  describe('ApplicationQueue Model', () => {
    test('should create queue item with required fields', async () => {
      const queueItem = new ApplicationQueue({
        userId: testUserId,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://funda.nl/listing/123',
        scheduledAt: new Date(),
        applicationData: {
          personalInfo: {
            fullName: 'John Doe',
            email: '<EMAIL>',
            phone: '+31612345678'
          },
          preferences: {
            template: 'professional',
            language: 'english'
          }
        },
        listingSnapshot: {
          title: 'Test Property',
          address: 'Test Street 123',
          price: 1800,
          rooms: 3
        }
      });

      const savedItem = await queueItem.save();
      expect(savedItem._id).toBeDefined();
      expect(savedItem.status).toBe('pending');
      expect(savedItem.attempts).toBe(0);
      expect(savedItem.randomDelay).toBeGreaterThan(0);
    });

    test('should calculate virtual fields correctly', async () => {
      const now = new Date();
      const pastDate = new Date(now.getTime() - 60000); // 1 minute ago

      const queueItem = new ApplicationQueue({
        userId: testUserId,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://funda.nl/listing/123',
        scheduledAt: pastDate,
        applicationData: {
          personalInfo: {
            fullName: 'John Doe',
            email: '<EMAIL>',
            phone: '+31612345678'
          }
        }
      });

      await queueItem.save();

      expect(queueItem.isReady).toBe(true);
      expect(queueItem.canRetry).toBe(false); // Not failed yet
    });

    test('should handle retry logic correctly', async () => {
      const queueItem = new ApplicationQueue({
        userId: testUserId,
        listingId: new mongoose.Types.ObjectId(),
        listingUrl: 'https://funda.nl/listing/123',
        scheduledAt: new Date(),
        applicationData: {
          personalInfo: {
            fullName: 'John Doe',
            email: '<EMAIL>',
            phone: '+31612345678'
          }
        }
      });

      await queueItem.save();

      // Test retry increment
      await queueItem.incrementAttempt();
      expect(queueItem.attempts).toBe(1);
      expect(queueItem.status).toBe('retrying');
      expect(queueItem.delayUntil).toBeDefined();
      expect(queueItem.delayUntil.getTime()).toBeGreaterThan(Date.now());
    });
  });

  describe('ApplicationResult Model', () => {
    test('should create application result with required fields', async () => {
      const queueItemId = new mongoose.Types.ObjectId();
      const listingId = new mongoose.Types.ObjectId();

      const result = new ApplicationResult({
        userId: testUserId,
        listingId: listingId,
        queueItemId: queueItemId,
        status: 'submitted',
        response: {
          success: true,
          message: 'Application submitted successfully',
          responseTime: 2500
        },
        metrics: {
          processingTime: 180000,
          formComplexity: 'moderate',
          fieldsDetected: 12,
          fieldsSuccessfullyFilled: 12,
          successProbability: 75
        },
        listingSnapshot: {
          title: 'Test Property',
          address: 'Test Street 123',
          price: 1800,
          rooms: 3,
          url: 'https://funda.nl/listing/123'
        }
      });

      const savedResult = await result.save();
      expect(savedResult._id).toBeDefined();
      expect(savedResult.status).toBe('submitted');
      expect(savedResult.response.success).toBe(true);
      expect(savedResult.submittedAt).toBeDefined();
    });

    test('should calculate virtual fields correctly', async () => {
      const result = new ApplicationResult({
        userId: testUserId,
        listingId: new mongoose.Types.ObjectId(),
        queueItemId: new mongoose.Types.ObjectId(),
        status: 'submitted',
        response: { success: true },
        landlordResponse: {
          responseReceived: true,
          responseDate: new Date(),
          viewingInvite: { invited: true },
          finalDecision: 'accepted'
        }
      });

      await result.save();

      expect(result.isSuccessful).toBe(true);
      expect(result.successScore).toBe(100); // 40 + 20 + 20 + 20
      expect(result.daysSinceSubmission).toBe(0);
    });

    test('should calculate success probability correctly', async () => {
      const result = new ApplicationResult({
        userId: testUserId,
        listingId: new mongoose.Types.ObjectId(),
        queueItemId: new mongoose.Types.ObjectId(),
        status: 'submitted',
        response: { success: true },
        metrics: {
          formComplexity: 'simple',
          processingTime: 120000 // 2 minutes (faster than average)
        },
        analytics: {
          marketCondition: 'low_demand',
          competitionLevel: 'low'
        }
      });

      const probability = result.calculateSuccessProbability();
      // Base 50 + simple form 20 + fast processing 10 + low demand 15 + low competition 20 = 115 (capped at 100)
      expect(probability).toBe(100);
    });
  });

  describe('Model Integration', () => {
    test('should work together in a complete workflow', async () => {
      // 1. Create auto application settings
      const settings = new AutoApplicationSettings({
        userId: testUserId,
        enabled: true,
        criteria: { maxPrice: 2000 },
        personalInfo: {
          fullName: 'John Doe',
          email: '<EMAIL>',
          phone: '+31612345678',
          dateOfBirth: new Date('1990-01-01'),
          nationality: 'Dutch',
          occupation: 'Software Engineer',
          employer: 'Tech Company',
          monthlyIncome: 5000,
          moveInDate: new Date('2024-03-01'),
          leaseDuration: 12,
          emergencyContact: {
            name: 'Jane Doe',
            phone: '+31687654321',
            relationship: 'Sister'
          }
        }
      });
      await settings.save();

      // 2. Create queue item
      const listingId = new mongoose.Types.ObjectId();
      const queueItem = new ApplicationQueue({
        userId: testUserId,
        listingId: listingId,
        listingUrl: 'https://funda.nl/listing/123',
        scheduledAt: new Date(),
        applicationData: {
          personalInfo: settings.personalInfo,
          preferences: {
            template: 'professional',
            language: 'english'
          }
        }
      });
      await queueItem.save();

      // 3. Create application result
      const result = new ApplicationResult({
        userId: testUserId,
        listingId: listingId,
        queueItemId: queueItem._id,
        status: 'submitted',
        response: { success: true }
      });
      await result.save();

      // 4. Update statistics
      await settings.incrementApplicationCount();
      await settings.recordSuccess();

      // Verify the workflow
      const updatedSettings = await AutoApplicationSettings.findById(settings._id);
      expect(updatedSettings.statistics.totalApplications).toBe(1);
      expect(updatedSettings.statistics.successfulApplications).toBe(1);
      expect(updatedSettings.statistics.dailyApplicationCount).toBe(1);

      const foundQueueItem = await ApplicationQueue.findById(queueItem._id);
      expect(foundQueueItem).toBeDefined();

      const foundResult = await ApplicationResult.findById(result._id);
      expect(foundResult.isSuccessful).toBe(true);
    });
  });
});