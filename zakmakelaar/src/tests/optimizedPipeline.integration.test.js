/**
 * Optimized Pipeline Integration Tests
 * 
 * This file contains integration tests for the complete optimized transformation pipeline,
 * including caching, concurrent processing, and database optimization.
 */

const { SchemaTransformer } = require('../services/schemaTransformer');
const { OptimizedSchemaTransformer } = require('../services/transformationOptimizer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { MappingConfigLoader } = require('../services/mappingConfigLoader');
const { DatabaseOptimizer } = require('../services/databaseOptimizer');
const { rawScraperData } = require('./testData/unifiedSchemaTestData');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

describe('Optimized Pipeline Integration', () => {
  let mongoServer;
  let connection;
  let PropertyModel;
  let registry;
  let baseTransformer;
  let optimizedTransformer;
  
  beforeAll(async () => {
    // Set up in-memory MongoDB server
    mongoServer = await MongoMemoryServer.create();
    const uri = mongoServer.getUri();
    
    // Connect to in-memory database
    connection = await mongoose.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    // Create test model
    const PropertySchema = new mongoose.Schema({
      title: String,
      description: String,
      url: String,
      source: String,
      location: mongoose.Schema.Types.Mixed,
      price: mongoose.Schema.Types.Mixed,
      propertyType: String,
      size: String,
      area: Number,
      rooms: mongoose.Schema.Types.Mixed,
      bedrooms: mongoose.Schema.Types.Mixed,
      bathrooms: mongoose.Schema.Types.Mixed,
      year: String,
      interior: String,
      furnished: Boolean,
      images: [String],
      dateAdded: Date,
      _internal: {
        sourceMetadata: {
          website: String,
          externalId: String,
          scrapedAt: Date,
          lastUpdated: Date,
          version: Number
        },
        rawData: mongoose.Schema.Types.Mixed,
        dataQuality: {
          completeness: Number,
          accuracy: Number,
          lastValidated: Date,
          validationErrors: [String]
        }
      }
    });
    
    PropertyModel = mongoose.model('Property', PropertySchema);
    
    // Create optimal indexes
    await DatabaseOptimizer.createOptimalIndexes(PropertyModel);
    
    // Set up the transformation pipeline components
    registry = new FieldMappingRegistry();
    const loader = new MappingConfigLoader(registry);
    
    // Load mappings for all sources
    loader.loadFromObject('funda.nl', {
      'title': 'title',
      'description': 'description',
      'url': 'url',
      'source': { value: 'funda.nl' },
      'location': {
        path: 'location',
        transform: 'normalizeLocation'
      },
      'price': {
        path: 'price',
        transform: 'normalizePrice'
      },
      'propertyType': {
        path: 'propertyType',
        transform: 'normalizePropertyType'
      },
      'size': {
        path: 'size',
        transform: 'formatSizeString'
      },
      'area': {
        path: 'size',
        transform: 'extractNumericSize'
      },
      'rooms': {
        path: 'rooms',
        transform: 'normalizeRooms'
      },
      'bedrooms': {
        path: 'bedrooms',
        transform: 'normalizeRooms'
      },
      'year': {
        path: 'year',
        transform: 'normalizeYear'
      },
      'interior': {
        path: 'interior',
        transform: 'normalizeInterior'
      },
      'furnished': {
        path: 'interior',
        transform: 'inferFurnishedStatus'
      },
      'images': {
        path: 'images',
        transform: 'normalizeImageArray'
      },
      'dateAdded': {
        transform: 'getCurrentISOString'
      }
    });
    
    // Create base transformer
    baseTransformer = new SchemaTransformer(registry);
    
    // Create optimized transformer
    optimizedTransformer = new OptimizedSchemaTransformer(baseTransformer, {
      cache: {
        stdTTL: 60, // 1 minute for tests
        maxKeys: 1000
      },
      concurrent: {
        maxWorkers: 2, // Limit to 2 workers for tests
        minBatchSize: 5
      }
    });
  });
  
  afterAll(async () => {
    // Disconnect and stop MongoDB server
    await mongoose.disconnect();
    await mongoServer.stop();
  });
  
  beforeEach(async () => {
    // Clear the collection before each test
    await PropertyModel.deleteMany({});
    
    // Clear the transformer cache
    optimizedTransformer.clearCache();
  });
  
  test('should transform and store properties efficiently', async () => {
    // Create test properties
    const properties = Array(20).fill().map((_, i) => ({
      ...rawScraperData.funda,
      title: `Property ${i}`,
      url: `https://www.funda.nl/huur/amsterdam/appartement-${i}/`
    }));
    
    // Transform properties with optimized transformer
    const startTime = process.hrtime();
    const batchResult = await optimizedTransformer.batchTransform(properties, 'funda.nl', {
      useConcurrent: true,
      useCache: true
    });
    const endTime = process.hrtime(startTime);
    const executionTimeMs = (endTime[0] * 1000) + (endTime[1] / 1000000);
    
    // Verify batch result
    expect(batchResult.results).toHaveLength(20);
    expect(batchResult.success).toBe(true);
    expect(batchResult.errorCount).toBe(0);
    
    // Verify performance
    expect(executionTimeMs).toBeLessThan(2000); // Should complete in under 2 seconds
    
    // Optimize documents for storage
    const optimizedDocuments = batchResult.results.map(doc => 
      DatabaseOptimizer.optimizeForStorage(doc)
    );
    
    // Store documents in database
    await PropertyModel.insertMany(optimizedDocuments);
    
    // Verify documents were stored
    const count = await PropertyModel.countDocuments();
    expect(count).toBe(20);
    
    // Test optimized query
    const { query, options } = DatabaseOptimizer.optimizeQuery({
      location: 'Amsterdam',
      propertyType: 'apartment'
    });
    
    // Execute optimized query
    const queryStartTime = process.hrtime();
    const results = await PropertyModel.find(query, options.projection).lean();
    const queryEndTime = process.hrtime(queryStartTime);
    const queryExecutionTimeMs = (queryEndTime[0] * 1000) + (queryEndTime[1] / 1000000);
    
    // Verify query results
    expect(results.length).toBeGreaterThan(0);
    
    // Verify query performance
    expect(queryExecutionTimeMs).toBeLessThan(100); // Should complete in under 100ms
  });
  
  test('should benefit from caching in end-to-end pipeline', async () => {
    // Create test properties with some duplicates
    const properties = Array(30).fill().map((_, i) => ({
      ...rawScraperData.funda,
      title: `Property ${i % 10}`, // Only 10 unique titles
      url: `https://www.funda.nl/huur/amsterdam/appartement-${i % 10}/` // Only 10 unique URLs
    }));
    
    // First run - no cache
    const firstRunStartTime = process.hrtime();
    await optimizedTransformer.batchTransform(properties, 'funda.nl', {
      useConcurrent: true,
      useCache: true
    });
    const firstRunEndTime = process.hrtime(firstRunStartTime);
    const firstRunExecutionTimeMs = (firstRunEndTime[0] * 1000) + (firstRunEndTime[1] / 1000000);
    
    // Second run - should benefit from cache
    const secondRunStartTime = process.hrtime();
    const secondRunResult = await optimizedTransformer.batchTransform(properties, 'funda.nl', {
      useConcurrent: true,
      useCache: true
    });
    const secondRunEndTime = process.hrtime(secondRunStartTime);
    const secondRunExecutionTimeMs = (secondRunEndTime[0] * 1000) + (secondRunEndTime[1] / 1000000);
    
    // Verify that the second run is significantly faster due to caching
    expect(secondRunExecutionTimeMs).toBeLessThan(firstRunExecutionTimeMs * 0.7); // At least 30% faster
    
    // Store the results in the database
    const optimizedDocuments = secondRunResult.results.map(doc => 
      DatabaseOptimizer.optimizeForStorage(doc)
    );
    
    await PropertyModel.insertMany(optimizedDocuments);
    
    // Get database statistics
    const stats = await DatabaseOptimizer.getDatabaseStats(PropertyModel);
    
    // Verify statistics
    expect(stats).toHaveProperty('totalDocuments', 30);
    expect(stats).toHaveProperty('indexSize');
    expect(stats).toHaveProperty('avgDocumentSize');
  });
});