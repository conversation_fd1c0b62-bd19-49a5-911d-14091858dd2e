// Mock dependencies
jest.mock('../models/Document');
jest.mock('../models/User');
jest.mock('fs', () => ({
  promises: {
    mkdir: jest.fn(),
    readFile: jest.fn(),
    writeFile: jest.fn(),
    unlink: jest.fn(),
  }
}));
jest.mock('../services/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
}));

const documentVaultService = require('../services/documentVaultService');
const Document = require('../models/Document');
const User = require('../models/User');
const fs = require('fs').promises;
const crypto = require('crypto');

describe('DocumentVaultService', () => {
  const mockUserId = '507f1f77bcf86cd799439011';
  const mockDocumentId = '507f1f77bcf86cd799439012';
  const mockAdminId = '507f1f77bcf86cd799439013';

  const mockFile = {
    filename: 'test-document.pdf',
    originalname: 'salary_slip.pdf',
    path: '/tmp/test-document.pdf',
    size: 1024000,
    mimetype: 'application/pdf'
  };

  const mockDocument = {
    _id: mockDocumentId,
    userId: mockUserId,
    filename: 'test-document.pdf',
    originalName: 'salary_slip.pdf',
    type: 'income_proof',
    size: 1024000,
    mimeType: 'application/pdf',
    encryptedPath: '/uploads/documents/test-document.pdf.enc',
    verified: false,
    encryptionKey: 'mock-encryption-key',
    metadata: {
      securityLevel: 'high',
      containsPII: true
    },
    createdAt: new Date(),
    isExpired: false,
    humanReadableSize: '1.00 MB',
    canAccess: jest.fn(),
    logAccess: jest.fn(),
    save: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock implementations
    Document.prototype.save = jest.fn().mockResolvedValue(mockDocument);
    Document.findById = jest.fn();
    Document.find = jest.fn();
    Document.findByUserAndType = jest.fn();
    Document.findUnverified = jest.fn();
    Document.findByIdAndDelete = jest.fn();
    
    User.findById = jest.fn();
    User.findByIdAndUpdate = jest.fn();
    User.findOneAndUpdate = jest.fn();
    
    fs.mkdir.mockResolvedValue();
    fs.readFile.mockResolvedValue(Buffer.from('mock file content'));
    fs.writeFile.mockResolvedValue();
    fs.unlink.mockResolvedValue();
  });

  describe('uploadDocument', () => {
    it('should successfully upload and encrypt a document', async () => {
      const mockSavedDocument = { ...mockDocument, save: jest.fn().mockResolvedValue(mockDocument) };
      Document.mockImplementation(() => mockSavedDocument);

      const result = await documentVaultService.uploadDocument(
        mockUserId,
        mockFile,
        'income_proof',
        { expiryDate: '2024-12-31' }
      );

      expect(result).toEqual({
        id: mockDocumentId,
        filename: 'salary_slip.pdf',
        type: 'income_proof',
        size: 1024000,
        humanReadableSize: '1.00 MB',
        verified: false,
        createdAt: expect.any(Date),
        expiryDate: expect.any(Date),
        isExpired: false
      });

      expect(fs.readFile).toHaveBeenCalledWith(mockFile.path);
      expect(fs.writeFile).toHaveBeenCalled();
      expect(fs.unlink).toHaveBeenCalledWith(mockFile.path);
      expect(mockSavedDocument.save).toHaveBeenCalled();
    });

    it('should reject files that are too large', async () => {
      const largeFile = { ...mockFile, size: 15 * 1024 * 1024 }; // 15MB

      await expect(
        documentVaultService.uploadDocument(mockUserId, largeFile, 'income_proof')
      ).rejects.toThrow('File size exceeds maximum allowed size');
    });

    it('should reject unsupported file types', async () => {
      const unsupportedFile = { ...mockFile, mimetype: 'application/exe' };

      await expect(
        documentVaultService.uploadDocument(mockUserId, unsupportedFile, 'income_proof')
      ).rejects.toThrow('File type application/exe is not allowed');
    });

    it('should reject invalid document types', async () => {
      await expect(
        documentVaultService.uploadDocument(mockUserId, mockFile, 'invalid_type')
      ).rejects.toThrow('Invalid document type: invalid_type');
    });

    it('should clean up file on upload failure', async () => {
      Document.mockImplementation(() => {
        throw new Error('Database error');
      });

      await expect(
        documentVaultService.uploadDocument(mockUserId, mockFile, 'income_proof')
      ).rejects.toThrow('Database error');

      expect(fs.unlink).toHaveBeenCalledWith(mockFile.path);
    });
  });

  describe('getDocuments', () => {
    it('should return user documents with proper formatting', async () => {
      const mockDocuments = [mockDocument];
      Document.findByUserAndType.mockResolvedValue(mockDocuments);

      const result = await documentVaultService.getDocuments(mockUserId);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: mockDocumentId,
        filename: 'salary_slip.pdf',
        type: 'income_proof',
        size: 1024000,
        humanReadableSize: '1.00 MB',
        verified: false,
        verifiedAt: undefined,
        createdAt: expect.any(Date),
        expiryDate: undefined,
        isExpired: false,
        metadata: {
          pageCount: undefined,
          language: undefined,
          securityLevel: 'high'
        }
      });

      expect(Document.findByUserAndType).toHaveBeenCalledWith(mockUserId, null);
    });

    it('should filter documents by type when specified', async () => {
      Document.findByUserAndType.mockResolvedValue([]);

      await documentVaultService.getDocuments(mockUserId, 'income_proof');

      expect(Document.findByUserAndType).toHaveBeenCalledWith(mockUserId, 'income_proof');
    });
  });

  describe('downloadDocument', () => {
    it('should successfully download and decrypt a document', async () => {
      const mockDecryptedData = Buffer.from('decrypted content');
      mockDocument.canAccess.mockReturnValue(true);
      Document.findById.mockResolvedValue(mockDocument);

      // Mock the private decryption method
      documentVaultService._decryptFile = jest.fn().mockResolvedValue(mockDecryptedData);

      const result = await documentVaultService.downloadDocument(
        mockDocumentId,
        mockUserId,
        'user',
        '127.0.0.1'
      );

      expect(result).toEqual({
        data: mockDecryptedData,
        filename: 'salary_slip.pdf',
        mimeType: 'application/pdf',
        size: 1024000
      });

      expect(mockDocument.canAccess).toHaveBeenCalledWith(mockUserId, 'user');
      expect(mockDocument.logAccess).toHaveBeenCalledWith(mockUserId, 'download', '127.0.0.1');
    });

    it('should deny access to unauthorized users', async () => {
      mockDocument.canAccess.mockReturnValue(false);
      Document.findById.mockResolvedValue(mockDocument);

      await expect(
        documentVaultService.downloadDocument(mockDocumentId, 'unauthorized-user', 'user', '127.0.0.1')
      ).rejects.toThrow('Access denied');
    });

    it('should reject expired documents', async () => {
      const expiredDocument = { ...mockDocument, isExpired: true };
      expiredDocument.canAccess.mockReturnValue(true);
      Document.findById.mockResolvedValue(expiredDocument);

      await expect(
        documentVaultService.downloadDocument(mockDocumentId, mockUserId, 'user', '127.0.0.1')
      ).rejects.toThrow('Document has expired');
    });

    it('should throw error for non-existent documents', async () => {
      Document.findById.mockResolvedValue(null);

      await expect(
        documentVaultService.downloadDocument(mockDocumentId, mockUserId, 'user', '127.0.0.1')
      ).rejects.toThrow('Document not found');
    });
  });

  describe('deleteDocument', () => {
    it('should successfully delete a document and clean up files', async () => {
      mockDocument.canAccess.mockReturnValue(true);
      mockDocument.thumbnailPath = '/uploads/thumbnails/thumb.jpg';
      Document.findById.mockResolvedValue(mockDocument);
      Document.findByIdAndDelete.mockResolvedValue(mockDocument);

      const result = await documentVaultService.deleteDocument(
        mockDocumentId,
        mockUserId,
        'user',
        '127.0.0.1'
      );

      expect(result).toBe(true);
      expect(mockDocument.logAccess).toHaveBeenCalledWith(mockUserId, 'delete', '127.0.0.1');
      expect(fs.unlink).toHaveBeenCalledWith(mockDocument.encryptedPath);
      expect(fs.unlink).toHaveBeenCalledWith(mockDocument.thumbnailPath);
      expect(Document.findByIdAndDelete).toHaveBeenCalledWith(mockDocumentId);
    });

    it('should deny deletion to unauthorized users', async () => {
      mockDocument.canAccess.mockReturnValue(false);
      Document.findById.mockResolvedValue(mockDocument);

      await expect(
        documentVaultService.deleteDocument(mockDocumentId, 'unauthorized-user', 'user', '127.0.0.1')
      ).rejects.toThrow('Access denied');
    });

    it('should handle file cleanup errors gracefully', async () => {
      mockDocument.canAccess.mockReturnValue(true);
      Document.findById.mockResolvedValue(mockDocument);
      Document.findByIdAndDelete.mockResolvedValue(mockDocument);
      fs.unlink.mockRejectedValue(new Error('File not found'));

      const result = await documentVaultService.deleteDocument(
        mockDocumentId,
        mockUserId,
        'user',
        '127.0.0.1'
      );

      expect(result).toBe(true); // Should still succeed despite file cleanup error
    });
  });

  describe('verifyDocument', () => {
    it('should successfully verify a document', async () => {
      Document.findById.mockResolvedValue(mockDocument);
      User.findOneAndUpdate.mockResolvedValue();

      const result = await documentVaultService.verifyDocument(
        mockDocumentId,
        mockAdminId,
        true,
        'Document looks valid'
      );

      expect(result).toEqual({
        id: mockDocumentId,
        verified: true,
        verifiedBy: mockAdminId,
        verifiedAt: expect.any(Date)
      });

      expect(mockDocument.verified).toBe(true);
      expect(mockDocument.verifiedBy).toBe(mockAdminId);
      expect(mockDocument.verifiedAt).toBeInstanceOf(Date);
      expect(mockDocument.metadata.verificationNotes).toBe('Document looks valid');
      expect(mockDocument.save).toHaveBeenCalled();
      expect(mockDocument.logAccess).toHaveBeenCalledWith(mockAdminId, 'verify', null);
    });

    it('should reject verification for non-existent documents', async () => {
      Document.findById.mockResolvedValue(null);

      await expect(
        documentVaultService.verifyDocument(mockDocumentId, mockAdminId, true)
      ).rejects.toThrow('Document not found');
    });
  });

  describe('generateDocumentReport', () => {
    it('should generate comprehensive document report', async () => {
      const mockDocuments = [
        { ...mockDocument, type: 'income_proof', verified: true, size: 1000000 },
        { ...mockDocument, type: 'bank_statement', verified: false, size: 500000, isExpired: true },
        { ...mockDocument, type: 'income_proof', verified: true, size: 750000 }
      ];

      Document.find.mockReturnValue({
        sort: jest.fn().mockResolvedValue(mockDocuments)
      });

      const result = await documentVaultService.generateDocumentReport(mockUserId);

      expect(result).toEqual({
        userId: mockUserId,
        generatedAt: expect.any(Date),
        totalDocuments: 3,
        verifiedDocuments: 2,
        expiredDocuments: 1,
        documentsByType: {
          income_proof: {
            count: 2,
            verified: 2,
            totalSize: 1750000
          },
          bank_statement: {
            count: 1,
            verified: 0,
            totalSize: 500000
          }
        },
        totalSize: 2250000,
        securitySummary: {
          high: 3,
          medium: 0,
          low: 0
        },
        verificationPercentage: 67
      });
    });

    it('should handle empty document list', async () => {
      Document.find.mockReturnValue({
        sort: jest.fn().mockResolvedValue([])
      });

      const result = await documentVaultService.generateDocumentReport(mockUserId);

      expect(result.totalDocuments).toBe(0);
      expect(result.verificationPercentage).toBe(0);
    });
  });

  describe('getUnverifiedDocuments', () => {
    it('should return unverified documents for admin review', async () => {
      const mockUnverifiedDocs = [{
        ...mockDocument,
        userId: {
          _id: mockUserId,
          email: '<EMAIL>',
          profile: {
            firstName: 'John',
            lastName: 'Doe'
          }
        }
      }];

      Document.findUnverified.mockReturnValue({
        limit: jest.fn().mockReturnValue({
          sort: jest.fn().mockResolvedValue(mockUnverifiedDocs)
        })
      });

      const result = await documentVaultService.getUnverifiedDocuments(10);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        id: mockDocumentId,
        userId: mockUserId,
        userEmail: '<EMAIL>',
        userName: 'John Doe',
        filename: 'salary_slip.pdf',
        type: 'income_proof',
        size: '1.00 MB',
        uploadedAt: expect.any(Date),
        metadata: {
          securityLevel: 'high',
          containsPII: true
        }
      });
    });
  });

  describe('File Validation', () => {
    it('should validate file types correctly', () => {
      const validTypes = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain'
      ];

      validTypes.forEach(mimeType => {
        const file = { ...mockFile, mimetype: mimeType };
        expect(() => documentVaultService._validateFile(file)).not.toThrow();
      });
    });

    it('should reject invalid file types', () => {
      const invalidTypes = [
        'application/exe',
        'video/mp4',
        'audio/mp3',
        'application/zip'
      ];

      invalidTypes.forEach(mimeType => {
        const file = { ...mockFile, mimetype: mimeType };
        expect(() => documentVaultService._validateFile(file)).toThrow();
      });
    });
  });

  describe('Access Control', () => {
    it('should allow document owner to access their documents', () => {
      const document = {
        userId: { toString: () => mockUserId },
        canAccess: function(userId, userRole) {
          return this.userId.toString() === userId.toString();
        }
      };

      expect(document.canAccess(mockUserId, 'user')).toBe(true);
    });

    it('should allow admin to access any document', () => {
      const document = {
        userId: { toString: () => mockUserId },
        canAccess: function(userId, userRole) {
          if (this.userId.toString() === userId.toString()) return true;
          if (userRole === 'admin') return true;
          return false;
        }
      };

      expect(document.canAccess('different-user-id', 'admin')).toBe(true);
    });

    it('should deny access to unauthorized users', () => {
      const document = {
        userId: { toString: () => mockUserId },
        canAccess: function(userId, userRole) {
          if (this.userId.toString() === userId.toString()) return true;
          if (userRole === 'admin') return true;
          return false;
        }
      };

      expect(document.canAccess('different-user-id', 'user')).toBe(false);
    });
  });

  describe('Encryption and Security', () => {
    it('should extract metadata correctly for different file types', () => {
      const pdfFile = { ...mockFile, mimetype: 'application/pdf', originalname: 'bank_statement.pdf' };
      const metadata = documentVaultService._extractMetadata(pdfFile);

      expect(metadata.securityLevel).toBe('high');
      expect(metadata.containsPII).toBe(true); // 'bank' keyword detected
    });

    it('should detect PII in filenames', () => {
      const piiFiles = [
        { originalname: 'passport_copy.pdf' },
        { originalname: 'salary_slip.pdf' },
        { originalname: 'bank_statement.pdf' },
        { originalname: 'id_document.jpg' }
      ];

      piiFiles.forEach(file => {
        const metadata = documentVaultService._extractMetadata({ ...mockFile, ...file });
        expect(metadata.containsPII).toBe(true);
      });
    });

    it('should handle encryption key generation', () => {
      const document = new Document({
        userId: mockUserId,
        filename: 'test.pdf',
        originalName: 'test.pdf',
        type: 'other',
        size: 1000,
        mimeType: 'application/pdf',
        encryptedPath: '/path/to/file'
      });

      // Simulate pre-save middleware
      if (!document.encryptionKey) {
        document.encryptionKey = crypto.randomBytes(32).toString('hex');
      }

      expect(document.encryptionKey).toBeDefined();
      expect(document.encryptionKey).toHaveLength(64); // 32 bytes = 64 hex chars
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      Document.findById.mockRejectedValue(new Error('Database connection failed'));

      await expect(
        documentVaultService.downloadDocument(mockDocumentId, mockUserId, 'user', '127.0.0.1')
      ).rejects.toThrow('Database connection failed');
    });

    it('should handle file system errors during upload', async () => {
      fs.readFile.mockRejectedValue(new Error('File read error'));

      await expect(
        documentVaultService.uploadDocument(mockUserId, mockFile, 'income_proof')
      ).rejects.toThrow();

      expect(fs.unlink).toHaveBeenCalledWith(mockFile.path); // Cleanup should still happen
    });

    it('should handle missing files during deletion', async () => {
      mockDocument.canAccess.mockReturnValue(true);
      Document.findById.mockResolvedValue(mockDocument);
      Document.findByIdAndDelete.mockResolvedValue(mockDocument);
      fs.unlink.mockRejectedValue(new Error('File not found'));

      // Should not throw error, just log warning
      const result = await documentVaultService.deleteDocument(
        mockDocumentId,
        mockUserId,
        'user',
        '127.0.0.1'
      );

      expect(result).toBe(true);
    });
  });

  describe('Integration with User Model', () => {
    it('should update user document references on upload', async () => {
      const mockSavedDocument = { ...mockDocument, save: jest.fn().mockResolvedValue(mockDocument) };
      Document.mockImplementation(() => mockSavedDocument);

      await documentVaultService.uploadDocument(mockUserId, mockFile, 'income_proof');

      expect(User.findByIdAndUpdate).toHaveBeenCalledWith(
        mockUserId,
        {
          $push: {
            documents: {
              id: expect.any(String),
              type: 'income_proof',
              filename: '',
              uploadDate: expect.any(Date),
              verified: false
            }
          }
        }
      );
    });

    it('should remove user document references on deletion', async () => {
      mockDocument.canAccess.mockReturnValue(true);
      Document.findById.mockResolvedValue(mockDocument);
      Document.findByIdAndDelete.mockResolvedValue(mockDocument);

      await documentVaultService.deleteDocument(mockDocumentId, mockUserId, 'user', '127.0.0.1');

      expect(User.findByIdAndUpdate).toHaveBeenCalledWith(
        mockUserId,
        {
          $pull: {
            documents: { id: mockDocumentId.toString() }
          }
        }
      );
    });

    it('should update user document verification status', async () => {
      Document.findById.mockResolvedValue(mockDocument);

      await documentVaultService.verifyDocument(mockDocumentId, mockAdminId, true);

      expect(User.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: mockUserId, 'documents.id': mockDocumentId.toString() },
        { $set: { 'documents.$.verified': true } }
      );
    });
  });
});