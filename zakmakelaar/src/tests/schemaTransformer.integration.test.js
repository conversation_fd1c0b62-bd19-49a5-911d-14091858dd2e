/**
 * Schema Transformer Integration Tests
 * 
 * This file contains integration tests for the SchemaTransformer class,
 * testing it with real-world data from different scrapers.
 */

const { SchemaTransformer } = require('../services/schemaTransformer');
const { FieldMappingRegistry } = require('../services/fieldMappingRegistry');
const { MappingConfigLoader } = require('../services/mappingConfigLoader');
const { validateProperty } = require('../schemas/unifiedPropertySchema');
const path = require('path');

describe('SchemaTransformer Integration', () => {
  let registry;
  let transformer;
  let configLoader;
  
  beforeEach(async () => {
    // Create registry and load default mappings
    registry = new FieldMappingRegistry();
    configLoader = new MappingConfigLoader(registry);
    await configLoader.loadDefaults();
    
    // Create transformer
    transformer = new SchemaTransformer(registry);
  });
  
  test('should transform Funda sample data', async () => {
    const fundaSample = {
      title: 'Modern Apartment in Amsterdam',
      description: 'Beautiful modern apartment in the center of Amsterdam',
      url: 'https://www.funda.nl/huur/amsterdam/appartement-12345678',
      price: '€1.250 per month',
      propertyType: 'Appartement',
      location: 'Amsterdam, Noord-Holland',
      size: '75 m²',
      rooms: '3 kamers (2 slaapkamers)',
      year: 'Bouwjaar 2015',
      interior: 'Gemeubileerd',
      images: [
        'https://cloud.funda.nl/image1.jpg',
        'https://cloud.funda.nl/image2.jpg'
      ],
      energyLabel: 'A',
      garden: 'Ja',
      balcony: 'Ja',
      parking: 'Nee'
    };
    
    const result = await transformer.transform(fundaSample, 'funda');
    
    // Validate basic transformation
    expect(result).toHaveProperty('title', 'Modern Apartment in Amsterdam');
    expect(result).toHaveProperty('source', 'funda.nl');
    expect(result).toHaveProperty('url', 'https://www.funda.nl/huur/amsterdam/appartement-12345678');
    expect(result).toHaveProperty('location', 'Amsterdam, Noord-Holland');
    
    // Check transformed fields
    expect(result).toHaveProperty('propertyType', 'apartment');
    expect(typeof result.price).toBe('number'); // Price is normalized to a number
    expect(result).toHaveProperty('size', '75 m²');
    expect(result).toHaveProperty('area', 75);
    expect(result).toHaveProperty('year', '2015');
    expect(result).toHaveProperty('interior', 'Gemeubileerd');
    expect(result).toHaveProperty('furnished', true);
    expect(result).toHaveProperty('garden', true);
    expect(result).toHaveProperty('balcony', true);
    expect(result).toHaveProperty('parking', false);
    expect(result).toHaveProperty('energyLabel', 'A');
    expect(result.images).toHaveLength(2);
    
    // Validate against schema
    const validation = validateProperty(result);
    expect(validation.error).toBeUndefined();
    
    // Check data quality
    const quality = transformer.calculateDataQuality(result);
    expect(quality.completeness).toBeGreaterThanOrEqual(80);
  });
  
  test('should transform Huurwoningen sample data', async () => {
    const huurwoningSample = {
      title: 'Spacious House with Garden',
      description: 'Spacious family house with large garden in Rotterdam',
      url: 'https://www.huurwoningen.nl/huren/rotterdam/huis-12345',
      price: '1500 euro',
      propertyType: 'Huis',
      location: 'Rotterdam',
      size: '120m2',
      rooms: '5',
      bedrooms: '3',
      year: '1980',
      interior: 'Gestoffeerd',
      images: [
        'https://www.huurwoningen.nl/img1.jpg',
        'https://www.huurwoningen.nl/img2.jpg',
        'https://www.huurwoningen.nl/img3.jpg'
      ],
      garden: true,
      balcony: false,
      parking: true
    };
    
    const result = await transformer.transform(huurwoningSample, 'huurwoningen');
    
    // Validate basic transformation
    expect(result).toHaveProperty('title', 'Spacious House with Garden');
    expect(result).toHaveProperty('source', 'huurwoningen.nl');
    expect(result).toHaveProperty('url', 'https://www.huurwoningen.nl/huren/rotterdam/huis-12345');
    expect(result).toHaveProperty('location', 'Rotterdam');
    
    // Check transformed fields
    expect(result).toHaveProperty('propertyType', 'house');
    expect(typeof result.price).toBe('number'); // Price is normalized to a number
    expect(result).toHaveProperty('size', '120 m²');
    expect(result).toHaveProperty('area', 120);
    expect(result.rooms).toBeTruthy(); // Rooms exists and is truthy
    expect(result.bedrooms).toBeTruthy(); // Bedrooms exists and is truthy
    expect(result).toHaveProperty('year', '1980');
    expect(result).toHaveProperty('interior', 'Gestoffeerd');
    expect(result).toHaveProperty('furnished', false);
    expect(result).toHaveProperty('garden', true);
    expect(result).toHaveProperty('balcony', false);
    expect(result).toHaveProperty('parking', true);
    expect(result.images).toHaveLength(3);
    
    // Validate against schema
    const validation = validateProperty(result);
    expect(validation.error).toBeUndefined();
  });
  
  test('should transform Pararius sample data', async () => {
    const parariusSample = {
      title: 'Studio Apartment in Utrecht',
      description: 'Cozy studio apartment near Utrecht Central Station',
      url: 'https://www.pararius.nl/appartement-te-huur/utrecht/12345',
      price: '€ 950 per month',
      propertyType: 'Studio',
      location: 'Utrecht, Utrecht',
      size: '35m²',
      rooms: '1',
      bedrooms: '0',
      bathrooms: '1',
      year: '2010',
      interior: 'Furnished',
      images: [
        'https://www.pararius.nl/image1.jpg'
      ],
      balcony: 'Yes',
      energyLabel: 'B'
    };
    
    const result = await transformer.transform(parariusSample, 'pararius');
    
    // Validate basic transformation
    expect(result).toHaveProperty('title', 'Studio Apartment in Utrecht');
    expect(result).toHaveProperty('source', 'pararius.nl');
    expect(result).toHaveProperty('url', 'https://www.pararius.nl/appartement-te-huur/utrecht/12345');
    expect(result).toHaveProperty('location', 'Utrecht, Utrecht');
    
    // Check transformed fields
    expect(result).toHaveProperty('propertyType', 'studio');
    expect(typeof result.price).toBe('number'); // Price is normalized to a number
    expect(result).toHaveProperty('size', '35 m²');
    expect(result).toHaveProperty('area', 35);
    expect(result.rooms).toBeTruthy(); // Rooms exists and is truthy
    expect(result.bedrooms).toBeDefined(); // Bedrooms exists
    expect(result.bathrooms).toBeTruthy(); // Bathrooms exists and is truthy
    expect(result).toHaveProperty('year', '2010');
    expect(result).toHaveProperty('interior', 'Gemeubileerd');
    expect(result).toHaveProperty('furnished', true);
    expect(result).toHaveProperty('garden', false);
    expect(result).toHaveProperty('balcony', true);
    expect(result).toHaveProperty('energyLabel', 'B');
    expect(result.images).toHaveLength(1);
    
    // Validate against schema
    const validation = validateProperty(result);
    expect(validation.error).toBeUndefined();
  });
  
  test('should handle edge cases and incomplete data', async () => {
    const incompleteData = {
      title: 'Incomplete Listing',
      url: 'https://example.com/incomplete',
      location: 'Unknown'
      // Missing many fields
    };
    
    const result = await transformer.transform(incompleteData, 'funda');
    
    // Should still have required fields with defaults
    expect(result).toHaveProperty('title', 'Incomplete Listing');
    expect(result).toHaveProperty('source', 'funda.nl');
    expect(result).toHaveProperty('url', 'https://example.com/incomplete');
    expect(result).toHaveProperty('location', 'Unknown');
    expect(result).toHaveProperty('propertyType', 'woning');
    
    // Should have data quality metrics
    const quality = transformer.calculateDataQuality(result);
    expect(quality.completeness).toBeDefined();
    
    // Should still be valid against schema (with allowUnknown: true)
    const validation = validateProperty(result, { allowUnknown: true });
    expect(validation.error).toBeUndefined();
  });
  
  test('should handle unusual data formats', async () => {
    const unusualData = {
      title: 'Unusual Data Formats',
      description: 'Property with unusual data formats',
      url: 'https://example.com/unusual',
      price: 'Price on request',
      propertyType: 'Unknown Type',
      location: 'Somewhere',
      size: 'Spacious',
      rooms: 'Multiple',
      year: 'Old'
    };
    
    const result = await transformer.transform(unusualData, 'funda');
    
    // Should handle unusual formats gracefully
    expect(result).toHaveProperty('title', 'Unusual Data Formats');
    expect(result).toHaveProperty('price', 'Price on request');
    expect(result).toHaveProperty('propertyType', 'woning');
    
    // For unusual data formats, we don't validate against the schema
    // Just check that we have the basic fields
    expect(result).toHaveProperty('title');
    expect(result).toHaveProperty('source');
    expect(result).toHaveProperty('url');
    expect(result).toHaveProperty('location');
  });
  
  test('should batch transform multiple properties', async () => {
    const properties = [
      {
        title: 'Property 1',
        description: 'First property',
        url: 'https://example.com/1',
        price: '€1000',
        location: 'Amsterdam',
        propertyType: 'Apartment',
        size: '50m²'
      },
      {
        title: 'Property 2',
        description: 'Second property',
        url: 'https://example.com/2',
        price: '€1500',
        location: 'Rotterdam',
        propertyType: 'House',
        size: '100m²'
      },
      {
        title: 'Property 3',
        description: 'Third property',
        url: 'https://example.com/3',
        price: '€800',
        location: 'Utrecht',
        propertyType: 'Studio',
        size: '30m²'
      }
    ];
    
    const result = await transformer.batchTransform(properties, 'funda');
    
    expect(result.success).toBe(true);
    expect(result.totalProcessed).toBe(3);
    expect(result.successCount).toBe(3);
    expect(result.errorCount).toBe(0);
    expect(result.results).toHaveLength(3);
    
    // All results should be valid
    for (const property of result.results) {
      const validation = validateProperty(property);
      expect(validation.error).toBeUndefined();
    }
  });
});