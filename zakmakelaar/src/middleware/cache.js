const cacheService = require('../services/cacheService');
const config = require('../config/config');

// Generate cache key from request
const generateCacheKey = (req) => {
  const baseKey = req.route.path.replace(/:/g, '');
  const params = JSON.stringify(req.params);
  const query = JSON.stringify(req.query);
  const userId = req.user ? req.user._id : 'anonymous';
  
  return `api:${baseKey}:${userId}:${Buffer.from(params + query).toString('base64')}`;
};

// Cache middleware factory
const cache = (ttl = config.cacheDefaultTTL) => {
  return async (req, res, next) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }

    try {
      const cacheKey = generateCacheKey(req);
      
      // Try to get from cache
      const cachedData = await cacheService.get(cacheKey);
      
      if (cachedData) {
        // Add cache headers
        res.set({
          'X-Cache': 'HIT',
          'X-Cache-Key': cacheKey,
          'Cache-Control': `public, max-age=${ttl}`
        });
        
        return res.json(cachedData);
      }

      // Store original res.json method
      const originalJson = res.json;
      
      // Override res.json to cache the response
      res.json = function(data) {
        // Cache the response data
        cacheService.set(cacheKey, data, ttl).catch(err => {
          console.error('Cache set error:', err);
        });
        
        // Add cache headers
        res.set({
          'X-Cache': 'MISS',
          'X-Cache-Key': cacheKey,
          'Cache-Control': `public, max-age=${ttl}`
        });
        
        // Call original json method
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      console.error('Cache middleware error:', error);
      next();
    }
  };
};

// Cache invalidation middleware
const invalidateCache = (patterns) => {
  return async (req, res, next) => {
    // Store original methods
    const originalJson = res.json;
    const originalSend = res.send;
    
    // Override response methods to invalidate cache after successful response
    const invalidateAfterResponse = function(data) {
      // Only invalidate on successful responses (2xx status codes)
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Invalidate cache patterns
        if (Array.isArray(patterns)) {
          patterns.forEach(pattern => {
            cacheService.delPattern(pattern).catch(err => {
              console.error('Cache invalidation error:', err);
            });
          });
        } else if (typeof patterns === 'string') {
          cacheService.delPattern(patterns).catch(err => {
            console.error('Cache invalidation error:', err);
          });
        }
      }
      
      return data;
    };
    
    res.json = function(data) {
      invalidateAfterResponse(data);
      return originalJson.call(this, data);
    };
    
    res.send = function(data) {
      invalidateAfterResponse(data);
      return originalSend.call(this, data);
    };

    next();
  };
};

// Specific cache configurations for different endpoints
const cacheConfigs = {
  // Cache listings for 5 minutes
  listings: cache(config.cacheListingsTTL),
  
  // Cache user data for 30 minutes
  user: cache(config.cacheUserTTL),
  
  // Cache general data for 1 hour
  general: cache(config.cacheDefaultTTL),
  
  // Short cache for frequently changing data (1 minute)
  short: cache(60),
  
  // Long cache for rarely changing data (24 hours)
  long: cache(86400)
};

// Cache invalidation patterns
const invalidationPatterns = {
  // Invalidate all listing caches when listings are updated
  listings: ['api:*listings*'],
  
  // Invalidate user caches when user data is updated
  user: (userId) => [`api:*users*${userId}*`, `api:*preferences*${userId}*`],
  
  // Invalidate all caches (use sparingly)
  all: ['api:*']
};

module.exports = {
  cache,
  invalidateCache,
  cacheConfigs,
  invalidationPatterns,
  generateCacheKey
};
