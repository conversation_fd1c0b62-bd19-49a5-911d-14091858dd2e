const { logHelpers } = require('../services/logger');

/**
 * Enhanced error handling utility for graceful degradation
 */
class ErrorHandler {
  constructor() {
    this.fallbackStrategies = new Map();
    this.errorCounts = new Map();
    this.circuitBreakers = new Map();
  }

  /**
   * Register a fallback strategy for an operation
   * @param {string} operation - Operation name
   * @param {Function} fallbackFn - Fallback function
   */
  registerFallback(operation, fallbackFn) {
    this.fallbackStrategies.set(operation, fallbackFn);
  }

  /**
   * Execute operation with error handling and fallback
   * @param {string} operation - Operation name
   * @param {Function} primaryFn - Primary function to execute
   * @param {Object} options - Options for error handling
   * @returns {Promise} Result or fallback result
   */
  async executeWithFallback(operation, primaryFn, options = {}) {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      circuitBreakerThreshold = 5,
      circuitBreakerTimeout = 60000,
      logErrors = true,
      metadata = {}
    } = options;

    // Check circuit breaker
    if (this.isCircuitOpen(operation)) {
      logHelpers.logPerformance(`${operation}-circuit-open`, 0, {
        ...metadata,
        reason: "Circuit breaker is open"
      });
      
      return this.executeFallback(operation, new Error("Circuit breaker is open"), metadata);
    }

    let lastError;
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        const result = await primaryFn();
        
        // Reset error count on success
        this.resetErrorCount(operation);
        
        return result;
        
      } catch (error) {
        lastError = error;
        attempt++;
        
        if (logErrors) {
          logHelpers.logPerformance(`${operation}-error-attempt-${attempt}`, 0, {
            ...metadata,
            error: error.message,
            attempt,
            maxRetries
          });
        }

        // Increment error count
        this.incrementErrorCount(operation);
        
        // Check if we should open circuit breaker
        if (this.getErrorCount(operation) >= circuitBreakerThreshold) {
          this.openCircuit(operation, circuitBreakerTimeout);
        }

        // Wait before retry (except on last attempt)
        if (attempt < maxRetries) {
          await this.delay(retryDelay * attempt); // Exponential backoff
        }
      }
    }

    // All retries failed, try fallback
    return this.executeFallback(operation, lastError, metadata);
  }

  /**
   * Execute fallback strategy
   * @private
   */
  async executeFallback(operation, error, metadata = {}) {
    const fallbackFn = this.fallbackStrategies.get(operation);
    
    if (fallbackFn) {
      try {
        logHelpers.logPerformance(`${operation}-fallback-executing`, 0, {
          ...metadata,
          originalError: error.message
        });
        
        const result = await fallbackFn(error, metadata);
        
        logHelpers.logPerformance(`${operation}-fallback-success`, 0, {
          ...metadata,
          originalError: error.message
        });
        
        return result;
        
      } catch (fallbackError) {
        logHelpers.logPerformance(`${operation}-fallback-failed`, 0, {
          ...metadata,
          originalError: error.message,
          fallbackError: fallbackError.message
        });
        
        throw new Error(`Primary operation failed: ${error.message}. Fallback also failed: ${fallbackError.message}`);
      }
    } else {
      logHelpers.logPerformance(`${operation}-no-fallback`, 0, {
        ...metadata,
        error: error.message
      });
      
      throw error;
    }
  }

  /**
   * Circuit breaker methods
   */
  isCircuitOpen(operation) {
    const breaker = this.circuitBreakers.get(operation);
    if (!breaker) return false;
    
    return breaker.isOpen && Date.now() < breaker.openUntil;
  }

  openCircuit(operation, timeout) {
    this.circuitBreakers.set(operation, {
      isOpen: true,
      openUntil: Date.now() + timeout,
      openedAt: Date.now()
    });
    
    logHelpers.logPerformance(`${operation}-circuit-opened`, 0, {
      timeout: `${timeout}ms`,
      errorCount: this.getErrorCount(operation)
    });
  }

  closeCircuit(operation) {
    this.circuitBreakers.delete(operation);
    logHelpers.logPerformance(`${operation}-circuit-closed`, 0);
  }

  /**
   * Error counting methods
   */
  incrementErrorCount(operation) {
    const current = this.errorCounts.get(operation) || 0;
    this.errorCounts.set(operation, current + 1);
  }

  getErrorCount(operation) {
    return this.errorCounts.get(operation) || 0;
  }

  resetErrorCount(operation) {
    this.errorCounts.delete(operation);
  }

  /**
   * Utility methods
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get error statistics
   */
  getErrorStats() {
    const stats = {};
    
    for (const [operation, count] of this.errorCounts.entries()) {
      const breaker = this.circuitBreakers.get(operation);
      stats[operation] = {
        errorCount: count,
        circuitOpen: this.isCircuitOpen(operation),
        circuitOpenUntil: breaker?.openUntil || null
      };
    }
    
    return stats;
  }

  /**
   * Reset all error counts and circuit breakers
   */
  reset() {
    this.errorCounts.clear();
    this.circuitBreakers.clear();
  }
}

/**
 * Specific error handlers for common scenarios
 */
class DatabaseErrorHandler extends ErrorHandler {
  constructor() {
    super();
    
    // Register common database fallbacks
    this.registerFallback('quick-stats', this.quickStatsFallback.bind(this));
    this.registerFallback('search', this.searchFallback.bind(this));
  }

  async quickStatsFallback(error, metadata) {
    // Try to get cached data first
    try {
      const cacheService = require('../services/cacheService');
      const cached = await cacheService.get('quick-stats');
      
      if (cached) {
        return {
          ...cached,
          cached: true,
          fallback: true,
          error: error.message
        };
      }
    } catch (cacheError) {
      logHelpers.logCache('get', 'quick-stats', false, cacheError, 'fallback');
    }

    // Return default values as last resort
    return {
      totalListings: 0,
      averagePrice: 0,
      newToday: 0,
      cached: false,
      fallback: true,
      error: error.message,
      degraded: true
    };
  }

  async searchFallback(error, metadata) {
    return {
      listings: [],
      pagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasNextPage: false,
        hasPrevPage: false,
        limit: 20
      },
      searchParams: metadata.searchParams || {},
      performance: {
        duration: "0ms",
        resultsFound: 0
      },
      error: error.message,
      fallback: true
    };
  }
}

// Create singleton instances
const errorHandler = new ErrorHandler();
const dbErrorHandler = new DatabaseErrorHandler();

module.exports = {
  ErrorHandler,
  DatabaseErrorHandler,
  errorHandler,
  dbErrorHandler
};