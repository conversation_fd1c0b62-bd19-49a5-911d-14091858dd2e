// Custom error classes for better error handling in the scraper

class ScraperError extends Error {
  constructor(message, code, retryable = false, details = {}) {
    super(message);
    this.name = 'ScraperError';
    this.code = code;
    this.retryable = retryable;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

class NetworkError extends ScraperError {
  constructor(message, details = {}) {
    super(message, 'NETWORK_ERROR', true, details);
    this.name = 'NetworkError';
  }
}

class TimeoutError extends ScraperError {
  constructor(message, details = {}) {
    super(message, 'TIMEOUT_ERROR', true, details);
    this.name = 'TimeoutError';
  }
}

class ParsingError extends ScraperError {
  constructor(message, details = {}) {
    super(message, 'PARSING_ERROR', false, details);
    this.name = 'ParsingError';
  }
}

class BrowserError extends ScraperError {
  constructor(message, details = {}) {
    super(message, 'BROWSER_ERROR', true, details);
    this.name = 'BrowserError';
  }
}

class RateLimitError extends ScraperError {
  constructor(message, details = {}) {
    super(message, 'RATE_LIMIT_ERROR', true, details);
    this.name = 'RateLimitError';
  }
}

class ValidationError extends ScraperError {
  constructor(message, details = {}) {
    super(message, 'VALIDATION_ERROR', false, details);
    this.name = 'ValidationError';
  }
}

class DatabaseError extends ScraperError {
  constructor(message, details = {}) {
    super(message, 'DATABASE_ERROR', true, details);
    this.name = 'DatabaseError';
  }
}

// Error classification helper
function classifyError(error) {
  const errorMessage = error.message.toLowerCase();
  const errorName = error.name;

  // Network-related errors
  if (errorMessage.includes('econnreset') || 
      errorMessage.includes('enotfound') || 
      errorMessage.includes('econnrefused') ||
      errorMessage.includes('network') ||
      errorMessage.includes('connection')) {
    return new NetworkError(error.message, { originalError: error });
  }

  // Timeout errors
  if (errorMessage.includes('timeout') || 
      errorName === 'TimeoutError' ||
      errorMessage.includes('navigation timeout')) {
    return new TimeoutError(error.message, { originalError: error });
  }

  // Browser/Puppeteer errors
  if (errorMessage.includes('browser') ||
      errorMessage.includes('page') ||
      errorMessage.includes('puppeteer') ||
      errorMessage.includes('chrome') ||
      errorMessage.includes('target closed')) {
    return new BrowserError(error.message, { originalError: error });
  }

  // Rate limiting
  if (errorMessage.includes('rate limit') ||
      errorMessage.includes('too many requests') ||
      errorMessage.includes('429')) {
    return new RateLimitError(error.message, { originalError: error });
  }

  // Database errors
  if (errorMessage.includes('mongo') ||
      errorMessage.includes('database') ||
      error.code === 11000) {
    return new DatabaseError(error.message, { originalError: error });
  }

  // Parsing errors
  if (errorMessage.includes('json') ||
      errorMessage.includes('parse') ||
      errorMessage.includes('syntax')) {
    return new ParsingError(error.message, { originalError: error });
  }

  // Default to generic scraper error
  return new ScraperError(error.message, 'UNKNOWN_ERROR', false, { originalError: error });
}

// Retry strategy helper
function getRetryStrategy(error) {
  if (!error.retryable) {
    return { shouldRetry: false, delay: 0, maxRetries: 0 };
  }

  const strategies = {
    NETWORK_ERROR: { shouldRetry: true, delay: 5000, maxRetries: 3 },
    TIMEOUT_ERROR: { shouldRetry: true, delay: 10000, maxRetries: 2 },
    BROWSER_ERROR: { shouldRetry: true, delay: 15000, maxRetries: 2 },
    RATE_LIMIT_ERROR: { shouldRetry: true, delay: 60000, maxRetries: 1 },
    DATABASE_ERROR: { shouldRetry: true, delay: 3000, maxRetries: 2 },
    UNKNOWN_ERROR: { shouldRetry: true, delay: 5000, maxRetries: 1 }
  };

  return strategies[error.code] || { shouldRetry: false, delay: 0, maxRetries: 0 };
}

// Error logging helper
function logError(error, context = {}) {
  const logData = {
    timestamp: new Date().toISOString(),
    error: {
      name: error.name,
      message: error.message,
      code: error.code || 'UNKNOWN',
      retryable: error.retryable || false,
      stack: error.stack
    },
    context
  };

  if (error.details) {
    logData.error.details = error.details;
  }

  console.error('🚨 SCRAPER ERROR:', JSON.stringify(logData, null, 2));
  
  // In production, you might want to send this to a logging service
  // like Winston, Sentry, or CloudWatch
}

// Error recovery helper
async function handleErrorWithRecovery(error, context, retryFunction) {
  const classifiedError = classifyError(error);
  const retryStrategy = getRetryStrategy(classifiedError);
  
  logError(classifiedError, context);

  if (retryStrategy.shouldRetry && context.retryCount < retryStrategy.maxRetries) {
    console.log(`🔄 Retrying in ${retryStrategy.delay}ms... (attempt ${context.retryCount + 1}/${retryStrategy.maxRetries})`);
    
    await new Promise(resolve => setTimeout(resolve, retryStrategy.delay));
    
    try {
      return await retryFunction({ ...context, retryCount: context.retryCount + 1 });
    } catch (retryError) {
      return handleErrorWithRecovery(retryError, { ...context, retryCount: context.retryCount + 1 }, retryFunction);
    }
  }

  // No more retries, throw the classified error
  throw classifiedError;
}

// Health check helper
function createHealthCheck() {
  const health = {
    status: 'healthy',
    lastError: null,
    errorCount: 0,
    lastSuccessfulScrape: null,
    consecutiveFailures: 0
  };

  return {
    recordSuccess() {
      health.status = 'healthy';
      health.lastSuccessfulScrape = new Date().toISOString();
      health.consecutiveFailures = 0;
    },

    recordError(error) {
      health.lastError = {
        message: error.message,
        code: error.code,
        timestamp: new Date().toISOString()
      };
      health.errorCount++;
      health.consecutiveFailures++;

      if (health.consecutiveFailures >= 3) {
        health.status = 'unhealthy';
      } else if (health.consecutiveFailures >= 1) {
        health.status = 'degraded';
      }
    },

    getHealth() {
      return { ...health };
    },

    isHealthy() {
      return health.status === 'healthy';
    }
  };
}

module.exports = {
  ScraperError,
  NetworkError,
  TimeoutError,
  ParsingError,
  BrowserError,
  RateLimitError,
  ValidationError,
  DatabaseError,
  classifyError,
  getRetryStrategy,
  logError,
  handleErrorWithRecovery,
  createHealthCheck
};
