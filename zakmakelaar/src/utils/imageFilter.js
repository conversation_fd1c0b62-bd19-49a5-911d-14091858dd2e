/**
 * Image Filtering Utility
 * 
 * Filters out placeholder images such as SVG logos, map tiles, and other non-property images
 * from the images array to ensure frontend displays only valid property photos.
 */

/**
 * Patterns for identifying placeholder/non-property images
 */
const PLACEHOLDER_PATTERNS = [
  // Funda logo SVGs
  /assets\.fstatic\.nl.*\.svg$/i,
  /shared\/images\/.*logo.*\.svg$/i,
  
  // Map tiles and location images
  /marketinsightsassets\.funda\.nl\/maps\//i,
  /maps\/map_.*\d+x\d+/i,
  
  // Generic logo patterns
  /logo/i,
  
  // Very small images (likely thumbnails or icons - only if they're explicitly small)
  /(icon|thumb|thumbnail)_?\d*x?\d*\.(jpg|png|webp)$/i, // matches patterns like icon_32x32.png, thumb_64x64.jpg
  /\b\d{1,2}x\d{1,2}\.(jpg|png|webp)$/i, // matches patterns like 32x32.png, 64x64.jpg
  
  // Site branding images
  /funda-logo/i,
  /site-logo/i,
  /brand/i
];

/**
 * File extensions that are valid for property images
 */
const VALID_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp'];

/**
 * Minimum dimensions for property images (to filter out icons/thumbnails)
 */
const MIN_IMAGE_DIMENSIONS = {
  width: 200,
  height: 150
};

/**
 * Check if an image URL is a placeholder/non-property image
 * @param {string} imageUrl - Image URL to check
 * @returns {boolean} True if image is a placeholder, false if it's a valid property image
 */
function isPlaceholderImage(imageUrl) {
  if (!imageUrl || typeof imageUrl !== 'string') {
    return true;
  }

  // Check against known placeholder patterns
  for (const pattern of PLACEHOLDER_PATTERNS) {
    if (pattern.test(imageUrl)) {
      return true;
    }
  }

  // Check file extension
  const hasValidExtension = VALID_IMAGE_EXTENSIONS.some(ext => 
    imageUrl.toLowerCase().includes(ext)
  );
  
  if (!hasValidExtension) {
    return true;
  }

  // Already handled by the regex patterns above, no need for additional dimension checking

  return false;
}

/**
 * Check if an image URL is a valid property image
 * @param {string} imageUrl - Image URL to check
 * @returns {boolean} True if image is valid for property display
 */
function isValidPropertyImage(imageUrl) {
  return !isPlaceholderImage(imageUrl);
}

/**
 * Filter out placeholder images from an array of image URLs
 * @param {string[]} images - Array of image URLs
 * @returns {string[]} Filtered array containing only valid property images
 */
function filterPlaceholderImages(images) {
  if (!Array.isArray(images)) {
    return [];
  }

  return images.filter(imageUrl => isValidPropertyImage(imageUrl));
}

/**
 * Filter images and ensure at least one valid image exists
 * @param {string[]} images - Array of image URLs
 * @param {string} fallbackImage - Optional fallback image URL
 * @returns {string[]} Filtered array with at least one image
 */
function filterImagesWithFallback(images, fallbackImage = null) {
  const filteredImages = filterPlaceholderImages(images);
  
  // If no valid images remain and we have a fallback, use it
  if (filteredImages.length === 0 && fallbackImage && isValidPropertyImage(fallbackImage)) {
    return [fallbackImage];
  }
  
  return filteredImages;
}

/**
 * Get statistics about image filtering for a given array
 * @param {string[]} originalImages - Original image array
 * @returns {object} Statistics about filtering results
 */
function getFilteringStats(originalImages) {
  if (!Array.isArray(originalImages)) {
    return {
      original: 0,
      filtered: 0,
      removed: 0,
      placeholders: []
    };
  }

  const filtered = filterPlaceholderImages(originalImages);
  const placeholders = originalImages.filter(img => isPlaceholderImage(img));

  return {
    original: originalImages.length,
    filtered: filtered.length,
    removed: originalImages.length - filtered.length,
    placeholders: placeholders
  };
}

/**
 * Advanced filtering with additional checks
 * @param {string[]} images - Array of image URLs
 * @param {object} options - Filtering options
 * @returns {string[]} Filtered image array
 */
function filterImagesAdvanced(images, options = {}) {
  const {
    maxImages = null,
    requireMinDimensions = true,
    allowMapImages = false,
    customPatterns = []
  } = options;

  if (!Array.isArray(images)) {
    return [];
  }

  let patterns = [...PLACEHOLDER_PATTERNS];
  
  // Add custom patterns if provided
  if (customPatterns.length > 0) {
    patterns = [...patterns, ...customPatterns];
  }
  
  // Remove map image patterns if allowed
  if (allowMapImages) {
    patterns = patterns.filter(pattern => 
      !pattern.toString().includes('marketinsightsassets') &&
      !pattern.toString().includes('maps')
    );
  }

  const filtered = images.filter(imageUrl => {
    if (!imageUrl || typeof imageUrl !== 'string') {
      return false;
    }

    // Check against patterns
    for (const pattern of patterns) {
      if (pattern.test(imageUrl)) {
        return false;
      }
    }

    // Check file extension
    const hasValidExtension = VALID_IMAGE_EXTENSIONS.some(ext => 
      imageUrl.toLowerCase().includes(ext)
    );
    
    if (!hasValidExtension) {
      return false;
    }

    // Check dimensions if required
    if (requireMinDimensions) {
      const dimensionMatch = imageUrl.match(/(\d+)x(\d+)/);
      if (dimensionMatch) {
        const width = parseInt(dimensionMatch[1]);
        const height = parseInt(dimensionMatch[2]);
        
        if (width < MIN_IMAGE_DIMENSIONS.width || height < MIN_IMAGE_DIMENSIONS.height) {
          return false;
        }
      }
    }

    return true;
  });

  // Limit number of images if specified
  if (maxImages && maxImages > 0) {
    return filtered.slice(0, maxImages);
  }

  return filtered;
}

module.exports = {
  isPlaceholderImage,
  isValidPropertyImage,
  filterPlaceholderImages,
  filterImagesWithFallback,
  filterImagesAdvanced,
  getFilteringStats,
  PLACEHOLDER_PATTERNS,
  VALID_IMAGE_EXTENSIONS,
  MIN_IMAGE_DIMENSIONS
};
