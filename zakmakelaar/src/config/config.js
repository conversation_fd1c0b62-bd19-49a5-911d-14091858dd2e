require("dotenv").config();

// AI Configuration (Google AI only)
const googleAIConfig = {
  apiKey: process.env.GOOGLE_AI_API_KEY,
  model: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash", // Updated to latest model
  maxTokens: parseInt(process.env.GOOGLE_AI_MAX_TOKENS) || 4000,
  temperature: parseFloat(process.env.GOOGLE_AI_TEMPERATURE) || 0.7,
  // Model configurations for different tasks
  models: {
    analysis: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
    matching: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
    summarization: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
    translation: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
    conversation: process.env.GOOGLE_AI_MODEL || "gemini-2.0-flash",
  },
};

module.exports = {
  // Server Configuration
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || "development",

  // Database Configuration
  mongoURI: process.env.MONGO_URI || "mongodb://localhost:27017/zakmakelaar",

  // JWT Configuration
  jwtSecret: process.env.JWT_SECRET || "fallback-secret-change-in-production",
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || "7d",

  // SendGrid Configuration
  sendgridApiKey: process.env.SENDGRID_API_KEY,
  sendgridFromEmail:
    process.env.SENDGRID_FROM_EMAIL || "<EMAIL>",

  // Twilio Configuration
  twilioAccountSid: process.env.TWILIO_ACCOUNT_SID,
  twilioAuthToken: process.env.TWILIO_AUTH_TOKEN,
  twilioWhatsAppFrom:
    process.env.TWILIO_WHATSAPP_FROM || "whatsapp:+***********",

  // Rate Limiting Configuration
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
  rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,

  // Scraping Configuration
  scrapingIntervalMinutes: parseInt(process.env.SCRAPING_INTERVAL_MINUTES) || 5,
  scrapingTimeoutMs: parseInt(process.env.SCRAPING_TIMEOUT_MS) || 60000,

  // CORS Configuration
  corsOrigin: process.env.CORS_ORIGIN
    ? process.env.CORS_ORIGIN.split(",")
    : ["http://localhost:3000"],

  // Redis Configuration
  redisHost: process.env.REDIS_HOST || "localhost",
  redisPort: parseInt(process.env.REDIS_PORT) || 6379,
  redisPassword: process.env.REDIS_PASSWORD || null,

  // Cache Configuration
  cacheDefaultTTL: parseInt(process.env.CACHE_DEFAULT_TTL) || 3600, // 1 hour
  cacheListingsTTL: parseInt(process.env.CACHE_LISTINGS_TTL) || 300, // 5 minutes
  cacheUserTTL: parseInt(process.env.CACHE_USER_TTL) || 1800, // 30 minutes

  // Auto-Application Configuration
  autoApplicationPeriodicProcessing:
    process.env.AUTO_APPLICATION_PERIODIC_PROCESSING !== "false", // Default enabled
  autoApplicationProcessingIntervalMinutes:
    parseInt(process.env.AUTO_APPLICATION_PROCESSING_INTERVAL_MINUTES) || 30,
  autoApplicationProcessOnStartup:
    process.env.AUTO_APPLICATION_PROCESS_ON_STARTUP !== "false", // Default enabled

  // Scraper Agent Configuration
  scraperAgentAutoStart: process.env.SCRAPER_AGENT_AUTO_START !== "false", // Default enabled

  // AI Configuration - Google AI only
  googleAI: googleAIConfig,
};
