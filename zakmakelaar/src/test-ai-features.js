const mongoose = require("mongoose");
const config = require("./config/config");
const aiService = require("./services/aiService");

// Test data
const sampleListing = {
  title: "Modern 2-Bedroom Apartment in Amsterdam Centrum",
  price: "€2,200",
  location: "Amsterdam, Centrum",
  size: "85 m²",
  rooms: "3",
  propertyType: "apartment",
  interior: "gestoffeerd",
  year: "2015",
  url: "https://example.com/listing/123",
};

const sampleUserPreferences = {
  location: "Amsterdam",
  budget: 2500,
  rooms: 2,
  propertyType: "apartment",
  minSize: 70,
  maxSize: 100,
  interior: "gestoffeerd",
  parking: true,
  balcony: false,
  garden: false,
  petsAllowed: true,
  studentFriendly: false,
  expatFriendly: true,
  commuteTime: 30,
  preferredNeighborhoods: ["Centrum", "Jordaan", "De Pijp"],
  excludedNeighborhoods: ["Zuidoost"],
};

const sampleContract = `
Huurcontract voor woonruimte

Artikel 1 - Huurder en verhuurder
De verhuurder: [<PERSON><PERSON> verhuurder]
De huurder: [<PERSON><PERSON> huurder]

Artikel 2 - Gehuurde ruimte
Het gehuurde object: [Adres en beschrijving]
De huurprijs: €2,200 per maand
De servicekosten: €150 per maand

Artikel 3 - Duur van de huurovereenkomst
De huurovereenkomst wordt aangegaan voor onbepaalde tijd.

Artikel 4 - Betaling
De huurprijs wordt maandelijks vooruitbetaald.

Artikel 5 - Indexering
De huurprijs wordt jaarlijks geïndexeerd volgens de consumentenprijsindex.

Artikel 6 - Onderhoud
De verhuurder is verplicht het gehuurde in goede staat te onderhouden.

Artikel 7 - Gebruik
De huurder mag het gehuurde alleen gebruiken als woonruimte.

Artikel 8 - Opzegging
De huurovereenkomst kan worden opgezegd met inachtneming van een opzegtermijn van 1 maand.

Artikel 9 - Borg
De huurder stelt een borg van €2,200 ter beschikking van de verhuurder.

Artikel 10 - Toepasselijk recht
Op deze huurovereenkomst is Nederlands recht van toepassing.
`;

const sampleUserProfile = {
  name: "Emma Johnson",
  income: 4500,
  occupation: "Software Engineer",
  employmentType: "full-time",
  contractType: "permanent",
  moveInDate: new Date("2024-03-01"),
  leaseDuration: 12,
  hasGuarantor: false,
  creditScore: "excellent",
  rentalHistory: {
    previousRentals: ["Previous apartment in Utrecht"],
    evictions: false,
    paymentIssues: false,
  },
};

async function testAIFeatures() {
  try {
    console.log("🤖 Testing ZakMakelaar AI Features with OpenRouter\n");

    // Test 1: AI-powered listing matching
    console.log("📊 Test 1: AI Listing Matching");
    console.log("=".repeat(50));
    const matchResult = await aiService.matchListingToUser(
      sampleListing,
      sampleUserPreferences
    );
    console.log("Match Score:", matchResult.score);
    console.log("Recommendation:", matchResult.recommendation);
    console.log("Reasoning:", matchResult.matchReasoning);
    console.log("Key Highlights:", matchResult.keyHighlights);
    console.log("Potential Concerns:", matchResult.potentialConcerns);
    console.log();

    // Test 2: Contract analysis
    console.log("📋 Test 2: Contract Analysis");
    console.log("=".repeat(50));
    const contractAnalysis = await aiService.analyzeContract(
      sampleContract,
      "dutch"
    );
    console.log("Risk Level:", contractAnalysis.riskLevel);
    console.log("Compliance Score:", contractAnalysis.complianceScore);
    console.log("Key Clauses:", contractAnalysis.keyClauses);
    console.log("Potential Issues:", contractAnalysis.potentialIssues);
    console.log("Legal Advice:", contractAnalysis.legalAdvice);
    console.log();

    // Test 3: Application message generation
    console.log("✍️ Test 3: Application Message Generation");
    console.log("=".repeat(50));
    const applicationMessage = await aiService.generateApplicationMessage(
      sampleListing,
      sampleUserProfile,
      "professional"
    );
    console.log("Generated Message:");
    console.log(applicationMessage.message);
    console.log();

    // Test 4: Market analysis
    console.log("📈 Test 4: Market Analysis");
    console.log("=".repeat(50));
    const marketAnalysis = await aiService.analyzeMarketTrends(
      "Amsterdam",
      "apartment",
      {
        averagePrice: 2200,
        priceTrend: "increasing",
        demandLevel: "high",
        supplyLevel: "low",
      }
    );
    console.log("Market Trend:", marketAnalysis.marketTrend);
    console.log("Price Prediction:", marketAnalysis.pricePrediction);
    console.log("Demand Level:", marketAnalysis.demandLevel);
    console.log("Key Insights:", marketAnalysis.keyInsights);
    console.log("Recommendations:", marketAnalysis.recommendations);
    console.log();

    // Test 5: Listing summarization
    console.log("📝 Test 5: Listing Summarization");
    console.log("=".repeat(50));
    const summary = await aiService.summarizeListing(sampleListing, "english");
    console.log("Summary:", summary.summary);
    console.log();

    // Test 6: Content translation
    console.log("🌐 Test 6: Content Translation");
    console.log("=".repeat(50));
    const translation = await aiService.translateContent(
      "Modern appartement in het centrum van Amsterdam met 2 slaapkamers en een balkon.",
      "dutch",
      "english"
    );
    console.log("Original:", translation.original);
    console.log("Translation:", translation.translation);
    console.log();

    console.log("✅ All AI tests completed successfully!");
    console.log("\n🎯 AI Features Summary:");
    console.log("- Smart listing matching with detailed scoring");
    console.log("- Contract analysis with legal compliance checking");
    console.log("- Personalized application message generation");
    console.log("- Market trend analysis and predictions");
    console.log("- Intelligent listing summarization");
    console.log("- Multi-language content translation");
  } catch (error) {
    console.error("❌ AI test failed:", error.message);
    console.error(
      "Make sure you have set up your OpenRouter API key in the .env file"
    );
    console.error("Get your API key from: https://openrouter.ai/keys");
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testAIFeatures()
    .then(() => {
      console.log("\n🎉 AI features are ready to use!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Test failed:", error);
      process.exit(1);
    });
}

module.exports = { testAIFeatures };
