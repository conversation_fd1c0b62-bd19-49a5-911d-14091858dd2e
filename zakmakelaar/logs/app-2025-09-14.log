{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'ENCRYPTION_MASTER_KEY not set in environment. Using generated key.',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'Scheduling periodic auto-application processing every 30 minutes',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:39:58'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:40:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:40:00'
}
{
  message: 'Starting scraper agent automatically on startup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:40:01'
}
{
  message: 'No existing transformation metrics found, starting fresh',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:40:01'
}
{
  message: 'Transformation monitor initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:40:01'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:40:01'
}
{
  message: 'Running initial auto-application processing for existing listings',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:40:03'
}
{
  message: 'Processing existing listings for auto-application',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:40:03'
}
{
  service: 'zakmakelaar-api',
  applicationsCreated: 0,
  usersProcessed: 0,
  listingsProcessed: undefined,
  level: 'info',
  message: 'Initial auto-application processing completed',
  timestamp: '2025-09-14 02:40:03'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/ai/application/test-submit',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 404,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:40:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 400,
  responseTime: '8ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:40:44'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 201,
  responseTime: '68ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:40:54'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/ai/application/submit',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 500,
  responseTime: '23ms',
  userId: new ObjectId('68c61d26636f64398e3c7f6a'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:41:06'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/ai/application/submit',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 500,
  responseTime: '14ms',
  userId: new ObjectId('68c61d26636f64398e3c7f6a'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:42:09'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'ENCRYPTION_MASTER_KEY not set in environment. Using generated key.',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Scheduling periodic auto-application processing every 30 minutes',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:31'
}
{
  message: 'Starting scraper agent automatically on startup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:34'
}
{
  message: 'No existing transformation metrics found, starting fresh',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:34'
}
{
  message: 'Transformation monitor initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:34'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:42:35'
}
{
  message: 'Running initial auto-application processing for existing listings',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:36'
}
{
  message: 'Processing existing listings for auto-application',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:42:36'
}
{
  service: 'zakmakelaar-api',
  applicationsCreated: 0,
  usersProcessed: 0,
  listingsProcessed: undefined,
  level: 'info',
  message: 'Initial auto-application processing completed',
  timestamp: '2025-09-14 02:42:36'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:44:38',
  level: 'info',
  category: 'ai_performance',
  operation: 'application_submission',
  duration: '17ms',
  model: 'manual',
  tokensUsed: undefined,
  environment: 'development',
  message: 'AI Performance'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/ai/application/submit',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 200,
  responseTime: '31ms',
  userId: new ObjectId('68c61d26636f64398e3c7f6a'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:44:38'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/ai/application/submit',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 401,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:44:55'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:45:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:45:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:45:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/applications',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 404,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:45:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/user/applications',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 404,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:45:19'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:45:44'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '15ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:47:28'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?sortBy=dateAdded&sortOrder=desc&page=1&limit=20',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
  statusCode: 200,
  responseTime: '28ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:47:28'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:47:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:47:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:47:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:48:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 3,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=3',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:48:40'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:50:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 3,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=3',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:51:05'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 4,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=4',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:51:25'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:52:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:52:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:52:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 201,
  responseTime: '99ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:53:02'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:53:02',
  level: 'info',
  category: 'ai_performance',
  operation: 'application_submission',
  duration: '13ms',
  model: 'manual',
  tokensUsed: undefined,
  environment: 'development',
  message: 'AI Performance'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/ai/application/submit',
  ip: '::1',
  userAgent: 'curl/8.12.1',
  statusCode: 200,
  responseTime: '23ms',
  userId: new ObjectId('68c61ffe3ab2469ad30cf6c7'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:53:02'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:53:17'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 4,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=4',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:54:01'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 5,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=5',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:54:36'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:55:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:55:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:55:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 3,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=3',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:56:20'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 5,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=5',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:57:06'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:57:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:57:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:57:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:57:35'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 6,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=6',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:57:44'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:58:39'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 4,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=4',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:59:18'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:00:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:00:00'
}
{
  message: 'Starting periodic auto-application processing for existing listings',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:00:00'
}
{
  message: 'Processing existing listings for auto-application',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:00:00'
}
{
  service: 'zakmakelaar-api',
  duration: '7ms',
  applicationsCreated: 0,
  usersProcessed: 0,
  listingsProcessed: undefined,
  level: 'info',
  message: 'Periodic auto-application processing completed',
  timestamp: '2025-09-14 03:00:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:00:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 401,
  responseTime: '14ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:00:13'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 6,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=6',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:00:15'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 401,
  responseTime: '8ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:00:32'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:01:03'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/login',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 401,
  responseTime: '5ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:01:03'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 7,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=7',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:01:08'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 3,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=3',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:01:46'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 201,
  responseTime: '119ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:07'
}
{
  message: 'Getting settings for user 68c6221e3ab2469ad30cf6db',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:08'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-request',
  duration: '0ms',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  timestamp: '2025-09-14 03:02:08',
  level: 'info',
  message: 'Performance Metric'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-total-count',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 03:02:08'
}
{
  message: 'Getting settings for user 68c6221e3ab2469ad30cf6db',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:08'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-new-today',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 03:02:08'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '12ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 03:02:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '15ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:08'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  collection: 'listings',
  duration: '37ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 03:02:08'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-calculation',
  duration: '37ms',
  cached: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  hasErrors: false,
  errorCount: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 03:02:08'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-response',
  duration: '40ms',
  status: 'success',
  cached: false,
  degraded: false,
  hasErrors: false,
  hasWarnings: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 03:02:08'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  duration: '41ms',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  hasError: false,
  responseSize: 214,
  threshold: '500ms',
  withinThreshold: true,
  monitorId: 'quick-stats-1757815328515-3nvq70cdo',
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 03:02:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '42ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auto-application/status',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '62ms',
  userId: new ObjectId('68c6221e3ab2469ad30cf6db'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:08'
}
{
  message: 'Getting settings for user 68c6221e3ab2469ad30cf6db',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auto-application/settings/68c6221e3ab2469ad30cf6db',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '26ms',
  userId: new ObjectId('68c6221e3ab2469ad30cf6db'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:08'
}
{
  message: 'Getting application stats for user 68c6221e3ab2469ad30cf6db',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:08'
}
{
  message: 'Getting settings for user 68c6221e3ab2469ad30cf6db',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auto-application/stats/68c6221e3ab2469ad30cf6db',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '33ms',
  userId: new ObjectId('68c6221e3ab2469ad30cf6db'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/cities',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '4ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:14'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/ai/market-analysis',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '5ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:17'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/ai/market-analysis',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:17'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 5,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=5',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:02:17'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:31',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/ai/match',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 404,
  responseTime: '11ms',
  userId: new ObjectId('68c6221e3ab2469ad30cf6db'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:41'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'PUT',
  url: '/api/auth/users/68c6221e3ab2469ad30cf6db/preferences',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '33ms',
  userId: new ObjectId('68c6221e3ab2469ad30cf6db'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:41'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auth/preferences',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 404,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:41'
}
{
  message: 'Getting settings for user 68c6221e3ab2469ad30cf6db',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:42'
}
{
  message: 'Getting settings for user 68c6221e3ab2469ad30cf6db',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:42'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auto-application/status',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 304,
  responseTime: '35ms',
  userId: new ObjectId('68c6221e3ab2469ad30cf6db'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:42'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '4ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 03:02:42'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc&minPrice=1200&maxPrice=2500&minRooms=2&maxRooms=4&propertyTypes=woning,appartement,huis&cities=Amsterdam,amsterdam,Amsterdam+Centrum,Amsterdam+Noord,Amsterdam+Zuid,Amsterdam+West,Amsterdam+Oost',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:42'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '7ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 03:02:42'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc&minPrice=1200&maxPrice=2500&minRooms=2&maxRooms=4&propertyTypes=woning,appartement,huis&cities=Amsterdam,amsterdam,Amsterdam+Centrum,Amsterdam+Noord,Amsterdam+Zuid,Amsterdam+West,Amsterdam+Oost',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '11ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:42'
}
{
  message: 'Getting settings for user 68c6221e3ab2469ad30cf6db',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:42'
}
{
  message: 'Getting application stats for user 68c6221e3ab2469ad30cf6db',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:42'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auto-application/settings/68c6221e3ab2469ad30cf6db',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '12ms',
  userId: new ObjectId('68c6221e3ab2469ad30cf6db'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:42'
}
{
  message: 'Getting settings for user 68c6221e3ab2469ad30cf6db',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 03:02:42'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/auto-application/stats/68c6221e3ab2469ad30cf6db',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 304,
  responseTime: '37ms',
  userId: new ObjectId('68c6221e3ab2469ad30cf6db'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:02:42'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:03:24'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-request',
  duration: '0ms',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  timestamp: '2025-09-14 03:03:25',
  level: 'info',
  message: 'Performance Metric'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-total-count',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 03:03:25'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-new-today',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 03:03:25'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  collection: 'listings',
  duration: '26ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 03:03:25'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-calculation',
  duration: '26ms',
  cached: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  hasErrors: false,
  errorCount: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 03:03:25'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-response',
  duration: '33ms',
  status: 'success',
  cached: false,
  degraded: false,
  hasErrors: false,
  hasWarnings: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 03:03:25'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  duration: '40ms',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  hasError: false,
  responseSize: 214,
  threshold: '500ms',
  withinThreshold: true,
  monitorId: 'quick-stats-1757815405217-62his5moq',
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 03:03:25'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:127.0.0.1',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '45ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 03:03:25'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 7,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=7',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:03:36'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 3,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=3',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:04:04'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 8,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=8',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:04:27'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 4,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=4',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 03:04:39'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Error handling service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'ENCRYPTION_MASTER_KEY not set in environment. Using generated key.',
  level: 'warn',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Notification queue processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Scheduling periodic auto-application processing every 30 minutes',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'production',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'WebSocket service initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'NotificationScheduler initialized with cron jobs',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Started scheduled job: dailySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Started scheduled job: weeklySummary',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Started scheduled job: cleanup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'NotificationScheduler started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Auto-application processing started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Auto-application service initialized and started',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Document vault directories initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:15'
}
{
  message: 'Starting scraper agent automatically on startup',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:18'
}
{
  message: 'No existing transformation metrics found, starting fresh',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:18'
}
{
  message: 'Transformation monitor initialized',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:18'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:13:19'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-request',
  duration: '0ms',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  timestamp: '2025-09-14 02:13:20',
  level: 'info',
  message: 'Performance Metric'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '8ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:13:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc&minPrice=1200&maxPrice=2500&minRooms=2&maxRooms=4&propertyTypes=woning,appartement,huis&cities=Amsterdam,amsterdam,Amsterdam+Centrum,Amsterdam+Noord,Amsterdam+Zuid,Amsterdam+West,Amsterdam+Oost',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '15ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:13:20'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-total-count',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:13:20'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-new-today',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:13:20'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  collection: 'listings',
  duration: '19ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:13:20'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-calculation',
  duration: '19ms',
  cached: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  hasErrors: false,
  errorCount: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:13:20'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-response',
  duration: '21ms',
  status: 'success',
  cached: false,
  degraded: false,
  hasErrors: false,
  hasWarnings: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:13:20'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  duration: '28ms',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  hasError: false,
  responseSize: 214,
  threshold: '500ms',
  withinThreshold: true,
  monitorId: 'quick-stats-1757816000292-x0vle1gar',
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:13:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '29ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:13:20'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '2ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:13:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '7ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:13:20'
}
{
  message: 'Running initial auto-application processing for existing listings',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:20'
}
{
  message: 'Processing existing listings for auto-application',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:13:20'
}
{
  service: 'zakmakelaar-api',
  applicationsCreated: 0,
  usersProcessed: 0,
  listingsProcessed: undefined,
  level: 'info',
  message: 'Initial auto-application processing completed',
  timestamp: '2025-09-14 02:13:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::ffff:**********',
  userAgent: 'curl/8.12.1',
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:13:46'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:13:49'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:14:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:14:49'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:15:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:15:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:15:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:15:49'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  attempt: 1,
  maxAttempts: 4,
  error: "Network.enable timed out. Increase the 'protocolTimeout' setting in launch/connect calls for a higher timeout if needed.",
  level: 'error',
  message: 'Error during Funda scraping',
  timestamp: '2025-09-14 02:16:19'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: NetworkError: Network.enable timed out. Increase the 'protocolTimeout' setting in launch/connect calls for a higher timeout if needed.
      at classifyError (/app/src/utils/scraperErrors.js:74:12)
      at ScrapingMetrics.recordScrapeFailure (/app/src/services/scraperUtils.js:402:29)
      at scrapeFunda (/app/src/services/scrapers/fundaScraper.js:1123:23)
      at async scrapeAll (/app/src/services/scraper.js:201:27)
      at async startAgent (/app/src/services/scraper.js:56:5)
      at async Timeout._onTimeout (/app/src/index.js:962:24) {
    code: 'NETWORK_ERROR',
    retryable: true,
    details: {
      originalError: ProtocolError: Network.enable timed out. Increase the 'protocolTimeout' setting in launch/connect calls for a higher timeout if needed.
          at <instance_members_initializer> (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/common/CallbackRegistry.js:102:14)
          at new Callback (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/common/CallbackRegistry.js:106:16)
          at CallbackRegistry.create (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/common/CallbackRegistry.js:24:26)
          at Connection._rawSend (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/Connection.js:99:26)
          at CdpCDPSession.send (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/CdpSession.js:73:33)
          at NetworkManager.addClient (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/NetworkManager.js:65:24)
          at FrameManager.initialize (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/FrameManager.js:174:38)
          at #initialize (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/Page.js:291:36)
          at CdpPage._create (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/Page.js:103:31)
          at /app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/Target.js:205:42
    },
    timestamp: '2025-09-14T02:16:19.002Z'
  },
  context: 'scrape_failure',
  site: null,
  totalScrapes: 3,
  failedScrapes: 1,
  level: 'error',
  message: 'Scrape failure',
  timestamp: '2025-09-14 02:16:19'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  maxAttempts: 4,
  saved: 0,
  duplicates: 0,
  level: 'error',
  message: 'Funda scraping failed after max attempts',
  timestamp: '2025-09-14 02:16:19'
}
{
  service: 'zakmakelaar-api',
  alert: {
    level: 'warning',
    type: 'performance',
    message: 'Average transformation time (180292ms) exceeds threshold (500ms)',
    value: 180292,
    threshold: 500,
    timestamp: '2025-09-14T02:16:19.004Z'
  },
  level: 'warn',
  message: 'Average transformation time (180292ms) exceeds threshold (500ms)',
  timestamp: '2025-09-14 02:16:19'
}
{
  service: 'zakmakelaar-api',
  message: 'Scraper agent started successfully Agent started successfully',
  level: 'info',
  timestamp: '2025-09-14 02:16:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:16:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:16:50'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:17:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:17:50'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  attempt: 1,
  maxAttempts: 4,
  error: "Runtime.enable timed out. Increase the 'protocolTimeout' setting in launch/connect calls for a higher timeout if needed.",
  level: 'error',
  message: 'Error during Funda scraping',
  timestamp: '2025-09-14 02:18:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  error: TimeoutError: Runtime.enable timed out. Increase the 'protocolTimeout' setting in launch/connect calls for a higher timeout if needed.
      at classifyError (/app/src/utils/scraperErrors.js:81:12)
      at ScrapingMetrics.recordScrapeFailure (/app/src/services/scraperUtils.js:402:29)
      at scrapeFunda (/app/src/services/scrapers/fundaScraper.js:1123:23)
      at runNextTicks (node:internal/process/task_queues:60:5)
      at listOnTimeout (node:internal/timers:538:9)
      at process.processTimers (node:internal/timers:512:7)
      at async Promise.allSettled (index 0)
      at async Job.job (/app/src/index.js:794:21) {
    code: 'TIMEOUT_ERROR',
    retryable: true,
    details: {
      originalError: ProtocolError: Runtime.enable timed out. Increase the 'protocolTimeout' setting in launch/connect calls for a higher timeout if needed.
          at <instance_members_initializer> (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/common/CallbackRegistry.js:102:14)
          at new Callback (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/common/CallbackRegistry.js:106:16)
          at CallbackRegistry.create (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/common/CallbackRegistry.js:24:26)
          at Connection._rawSend (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/Connection.js:99:26)
          at CdpCDPSession.send (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/CdpSession.js:73:33)
          at FrameManager.initialize (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/FrameManager.js:181:24)
          at #initialize (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/Page.js:291:36)
          at CdpPage._create (/app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/Page.js:103:31)
          at /app/node_modules/puppeteer-core/lib/cjs/puppeteer/cdp/Target.js:205:42
          at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    },
    timestamp: '2025-09-14T02:18:00.262Z'
  },
  context: 'scrape_failure',
  site: null,
  totalScrapes: 3,
  failedScrapes: 2,
  level: 'error',
  message: 'Scrape failure',
  timestamp: '2025-09-14 02:18:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  maxAttempts: 4,
  saved: 0,
  duplicates: 0,
  level: 'error',
  message: 'Funda scraping failed after max attempts',
  timestamp: '2025-09-14 02:18:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  listings: 0,
  level: 'info',
  message: 'funda scraping completed',
  timestamp: '2025-09-14 02:18:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '180257ms',
  listingsProcessed: 0,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-09-14 02:18:00'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:18:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:18:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:18:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:18:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/auth/register',
  ip: '::ffff:**********',
  userAgent: 'curl/8.12.1',
  statusCode: 201,
  responseTime: '85ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:18:38'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:18:38',
  level: 'info',
  category: 'ai_performance',
  operation: 'application_submission',
  duration: '29ms',
  model: 'manual',
  tokensUsed: undefined,
  environment: 'production',
  message: 'AI Performance'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'POST',
  url: '/api/ai/application/submit',
  ip: '::ffff:**********',
  userAgent: 'curl/8.12.1',
  statusCode: 200,
  responseTime: '39ms',
  userId: new ObjectId('68c625fee69bea7580e44153'),
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:18:38'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:18:50'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs',
  ip: '::ffff:**********',
  userAgent: 'curl/8.12.1',
  statusCode: 301,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:18:59'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:19:20'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-request',
  duration: '0ms',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  timestamp: '2025-09-14 02:19:28',
  level: 'info',
  message: 'Performance Metric'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-total-count',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:19:28'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-new-today',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:19:28'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  collection: 'listings',
  duration: '7ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:19:28'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-calculation',
  duration: '7ms',
  cached: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  hasErrors: false,
  errorCount: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:19:28'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-response',
  duration: '9ms',
  status: 'success',
  cached: false,
  degraded: false,
  hasErrors: false,
  hasWarnings: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:19:28'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  duration: '9ms',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  hasError: false,
  responseSize: 213,
  threshold: '500ms',
  withinThreshold: true,
  monitorId: 'quick-stats-1757816368580-qutzs0k6i',
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:19:28'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '11ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:19:28'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '2ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:19:28'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc&minPrice=1200&maxPrice=2500&minRooms=2&maxRooms=4&propertyTypes=woning,appartement,huis&cities=Amsterdam,amsterdam,Amsterdam+Centrum,Amsterdam+Noord,Amsterdam+Zuid,Amsterdam+West,Amsterdam+Oost',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:19:28'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '2ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:19:28'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 304,
  responseTime: '5ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:19:28'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:19:50'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:20:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:20:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:20:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:20:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:20:50'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:21:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:21:50'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:22:20'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:22:51'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:22:57'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:23:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:23:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:23:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:23:21'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:23:51'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-request',
  duration: '0ms',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  timestamp: '2025-09-14 02:23:57',
  level: 'info',
  message: 'Performance Metric'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-total-count',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:23:57'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-new-today',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:23:57'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  collection: 'listings',
  duration: '10ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:23:57'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-calculation',
  duration: '10ms',
  cached: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  hasErrors: false,
  errorCount: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:23:57'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-response',
  duration: '10ms',
  status: 'success',
  cached: false,
  degraded: false,
  hasErrors: false,
  hasWarnings: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:23:57'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  duration: '12ms',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  hasError: false,
  responseSize: 214,
  threshold: '500ms',
  withinThreshold: true,
  monitorId: 'quick-stats-1757816637644-66qafw2pv',
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:23:57'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '13ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:23:57'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '2ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:24:04'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?sortBy=dateAdded&sortOrder=desc&page=1&limit=20',
  ip: '::ffff:**********',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
  statusCode: 200,
  responseTime: '5ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:24:04'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/',
  ip: '::ffff:**********',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
  statusCode: 304,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:24:09'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-init.js',
  ip: '::ffff:**********',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
  statusCode: 304,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:24:09'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui.css',
  ip: '::ffff:**********',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
  statusCode: 200,
  responseTime: '8ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:24:09'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-standalone-preset.js',
  ip: '::ffff:**********',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
  statusCode: 200,
  responseTime: '8ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:24:09'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-bundle.js',
  ip: '::ffff:**********',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
  statusCode: 200,
  responseTime: '12ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:24:09'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/favicon-32x32.png',
  ip: '::ffff:**********',
  userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:24:10'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:24:21'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:24:42'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:24:51'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:25:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:25:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:25:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:25:21'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:25:51'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 3,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=3',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:25:53'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:26:21'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:26:51'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:27:21'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:27:41'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:27:51'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:27:56'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:28:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:28:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:28:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:28:18'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:28:21'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-request',
  duration: '0ms',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  timestamp: '2025-09-14 02:28:30',
  level: 'info',
  message: 'Performance Metric'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '6ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:28:30'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc&minPrice=1200&maxPrice=2500&minRooms=2&maxRooms=4&propertyTypes=woning,appartement,huis&cities=Amsterdam,amsterdam,Amsterdam+Centrum,Amsterdam+Noord,Amsterdam+Zuid,Amsterdam+West,Amsterdam+Oost',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '10ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:28:30'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-total-count',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:28:30'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-new-today',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:28:30'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  collection: 'listings',
  duration: '13ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:28:30'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-calculation',
  duration: '13ms',
  cached: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  hasErrors: false,
  errorCount: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:28:30'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-response',
  duration: '16ms',
  status: 'success',
  cached: false,
  degraded: false,
  hasErrors: false,
  hasWarnings: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:28:30'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  duration: '19ms',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  hasError: false,
  responseSize: 214,
  threshold: '500ms',
  withinThreshold: true,
  monitorId: 'quick-stats-1757816910801-io6tzcwbu',
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:28:30'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '22ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:28:30'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '2ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:28:30'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 304,
  responseTime: '4ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:28:30'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 4,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=4',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:28:46'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:28:52'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:29:22'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:29:52'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:30:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:30:00'
}
{
  message: 'Starting periodic auto-application processing for existing listings',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:30:00'
}
{
  message: 'Processing existing listings for auto-application',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:30:00'
}
{
  service: 'zakmakelaar-api',
  duration: '14ms',
  applicationsCreated: 0,
  usersProcessed: 0,
  listingsProcessed: undefined,
  level: 'info',
  message: 'Periodic auto-application processing completed',
  timestamp: '2025-09-14 02:30:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:30:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:30:22'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 3,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=3',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:30:52'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:30:52'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 3,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=3',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:31:04'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:31:22'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:31:24'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 5,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=5',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:31:47'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:31:52'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:32:22'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:32:52'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:33:03'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:33:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:33:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:33:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:33:22'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 4,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=4',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:33:42'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:33:52'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 4,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=4',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:33:53'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:34:22'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 3,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=3',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:34:29'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 6,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=6',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:34:49'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:34:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:35:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:35:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:35:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:35:23'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:35:53'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 3,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=3',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:35:59'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:36:23'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 5,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=5',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:36:39'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:36:53'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 5,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=5',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:37:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 4,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=4',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:37:14'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:37:23'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:37:53'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:38:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:38:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:38:15',
  totalErrors: 0,
  errorsByCategory: {
    NETWORK: 0,
    FORM: 0,
    DETECTION: 0,
    DATA: 0,
    SYSTEM: 0,
    RATE_LIMIT: 0,
    CAPTCHA: 0,
    AUTHENTICATION: 0
  },
  systemHealth: 'healthy',
  level: 'info',
  message: 'Health check completed:'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 7,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=7',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:38:16'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 2,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=2',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:38:19'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:38:23'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 4,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=4',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:38:51'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:38:53'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-request',
  duration: '0ms',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  timestamp: '2025-09-14 02:39:12',
  level: 'info',
  message: 'Performance Metric'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-total-count',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:39:12'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats-new-today',
  collection: 'listings',
  duration: '0ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:39:12'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  collection: 'listings',
  duration: '14ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:39:12'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-calculation',
  duration: '14ms',
  cached: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  hasErrors: false,
  errorCount: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:39:12'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats-response',
  duration: '14ms',
  status: 'success',
  cached: false,
  degraded: false,
  hasErrors: false,
  hasWarnings: false,
  totalListings: 0,
  averagePrice: 0,
  newToday: 0,
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:39:12'
}
{
  service: 'zakmakelaar-api',
  operation: 'quick-stats',
  duration: '16ms',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  hasError: false,
  responseSize: 214,
  threshold: '500ms',
  withinThreshold: true,
  monitorId: 'quick-stats-1757817552131-at588vb4h',
  level: 'info',
  message: 'Performance Metric',
  timestamp: '2025-09-14 02:39:12'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings/quick-stats',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '17ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:39:12'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '3ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:39:12'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc&minPrice=1200&maxPrice=2500&minRooms=2&maxRooms=4&propertyTypes=woning,appartement,huis&cities=Amsterdam,amsterdam,Amsterdam+Centrum,Amsterdam+Noord,Amsterdam+Zuid,Amsterdam+West,Amsterdam+Oost',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:39:12'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '3ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-09-14 02:39:12'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?limit=10&sortBy=dateAdded&sortOrder=desc',
  ip: '::ffff:**********',
  userAgent: 'okhttp/4.9.2',
  statusCode: 200,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:39:12'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:39:23'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 6,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=6',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:39:50'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:39:53'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:40:00'
}
{
  component: 'scraper',
  message: 'Running enabled scrapers: funda',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-09-14 02:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 1,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?selected_area=%5B%22nl%22%5D&availability=%5B%22available%22,%22negotiations%22%5D&object_type=%5B%22house%22,%22apartment%22%5D&sort=%22date_down%22',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:40:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 6,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=6',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:40:08'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/health',
  ip: '::1',
  userAgent: undefined,
  statusCode: 200,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-09-14 02:40:23'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  pageNumber: 5,
  pageUrl: 'https://www.funda.nl/zoeken/huur/?page=5',
  level: 'info',
  message: 'Navigating to Funda page',
  timestamp: '2025-09-14 02:40:26'
}
