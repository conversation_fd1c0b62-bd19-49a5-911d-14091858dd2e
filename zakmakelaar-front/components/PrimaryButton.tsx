import React, { useEffect } from 'react';
import { 
  StyleSheet, 
  TouchableOpacity, 
  Text, 
  ActivityIndicator,
  ViewStyle,
  StyleProp,
  TextStyle,
  ColorValue
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Animated, { 
  useAnimatedStyle, 
  useSharedValue, 
  withTiming,
  withSequence,
  withRepeat,
  withDelay,
  FadeIn,
  FadeInUp,
  Easing
} from 'react-native-reanimated';

interface PrimaryButtonProps {
  title: string;
  onPress: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  icon?: React.ReactNode;
  iconName?: keyof typeof Ionicons.glyphMap;
  delay?: number;
  primaryColor?: string;
  secondaryColor?: string;
  withGlow?: boolean;
  variant?: 'primary' | 'secondary' | 'outline';
}

const AnimatedTouchable = Animated.createAnimatedComponent(TouchableOpacity);

export const PrimaryButton: React.FC<PrimaryButtonProps> = ({
  title,
  onPress,
  isLoading = false,
  disabled = false,
  style,
  textStyle,
  icon,
  iconName,
  delay = 0,
  primaryColor = '#4361ee',
  secondaryColor = '#f72585',
  withGlow = true,
  variant = 'primary'
}) => {
  // Animation values
  const scale = useSharedValue(1);
  const glowOpacity = useSharedValue(0.5);
  const loadingRotation = useSharedValue(0);
  
  // Start glow animation
  useEffect(() => {
    if (withGlow && !disabled && !isLoading) {
      glowOpacity.value = withRepeat(
        withSequence(
          withTiming(0.7, { duration: 1500, easing: Easing.inOut(Easing.ease) }),
          withTiming(0.5, { duration: 1500, easing: Easing.inOut(Easing.ease) })
        ),
        -1,
        true
      );
    } else {
      glowOpacity.value = withTiming(0, { duration: 300 });
    }
    
    // Loading animation
    if (isLoading) {
      loadingRotation.value = withRepeat(
        withTiming(360, { 
          duration: 1000, 
          easing: Easing.linear 
        }),
        -1,
        false
      );
    }
  }, [withGlow, disabled, isLoading]);
  
  const handlePressIn = () => {
    scale.value = withTiming(0.95, { duration: 100 });
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };
  
  const handlePressOut = () => {
    scale.value = withTiming(1, { duration: 200 });
  };
  
  const handlePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onPress();
  };
  
  // Get gradient colors based on variant
  const getGradientColors = (variant: 'primary' | 'secondary' | 'outline' | undefined, disabled: boolean, primaryColor: string, secondaryColor: string): [ColorValue, ColorValue] => {
    if (disabled) return ['#9ca3af', '#9ca3af'] as [ColorValue, ColorValue];
    
    switch (variant) {
      case 'secondary':
        return ['#64748b', '#475569'] as [ColorValue, ColorValue];
      case 'outline':
        return ['transparent', 'transparent'] as [ColorValue, ColorValue];
      case 'primary':
      default:
        return [primaryColor, secondaryColor] as [ColorValue, ColorValue];
    }
  };
  
  // Animated styles
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }]
    };
  });
  
  const glowStyle = useAnimatedStyle(() => {
    return {
      opacity: glowOpacity.value
    };
  });
  
  const loadingStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${loadingRotation.value}deg` }]
    };
  });

  return (
    <Animated.View 
      style={[styles.buttonContainer, style, animatedStyle]}
      entering={FadeInUp.delay(delay).duration(400)}
    >
      {/* Button glow effect */}
      {withGlow && !disabled && !isLoading && (
        <Animated.View style={[styles.buttonGlow, glowStyle]}>
          <LinearGradient
            colors={[primaryColor, secondaryColor]}
            style={styles.buttonGlowGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        </Animated.View>
      )}
      
      {/* Actual button */}
      <TouchableOpacity
        style={[
          styles.button,
          disabled || isLoading ? styles.buttonDisabled : null,
        ]}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled || isLoading}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={getGradientColors(variant, disabled, primaryColor, secondaryColor)}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.buttonGradient}
        >
          {isLoading ? (
            <Animated.View style={loadingStyle}>
              <ActivityIndicator color="#FFFFFF" size="small" />
            </Animated.View>
          ) : (
            <>
              {icon}
              {iconName && (
                <Ionicons 
                  name={iconName} 
                  size={18} 
                  color="#FFFFFF" 
                  style={styles.buttonIcon} 
                />
              )}
              <Text style={[
                styles.buttonText, 
                variant === 'outline' && styles.outlineButtonText,
                textStyle
              ]}>{title}</Text>
            </>
          )}
        </LinearGradient>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    position: 'relative',
    width: '100%',
    height: 56,
    marginVertical: 8,
  },
  buttonGlow: {
    position: 'absolute',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    borderRadius: 16,
    overflow: 'hidden',
  },
  buttonGlowGradient: {
    width: '100%',
    height: '100%',
  },
  button: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  buttonGradient: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    paddingHorizontal: 24,
  },
  buttonDisabled: {
    borderColor: 'transparent',
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  outlineButtonText: {
    color: '#4361ee',
  },
});