import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

// This component provides placeholder images for development
// In a production app, these would be replaced with actual image assets

interface PlaceholderImageProps {
  type: string;
  width?: number;
  height?: number;
  backgroundColor?: string;
  textColor?: string;
}

export const PlaceholderImage: React.FC<PlaceholderImageProps> = ({
  type,
  width = 100,
  height = 100,
  backgroundColor = '#e5e7eb',
  textColor = '#6b7280',
}) => {
  return (
    <View style={[
      styles.container,
      { width, height, backgroundColor }
    ]}>
      <Text style={[styles.text, { color: textColor }]}>{type}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 14,
    fontWeight: '500',
  },
});

// Export placeholder image objects that can be used in place of require() statements
export const placeholderImages = {
  'profile-student': { uri: 'placeholder-profile-student' },
  'profile-expat': { uri: 'placeholder-profile-expat' },
  'profile-professional': { uri: 'placeholder-profile-professional' },
  'property-apartment': { uri: 'placeholder-property-apartment' },
  'property-house': { uri: 'placeholder-property-house' },
  'property-room': { uri: 'placeholder-property-room' },
  'property-studio': { uri: 'placeholder-property-studio' },
};