import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

// Define profile types
const PROFILE_TYPES = [
  {
    id: 'student',
    title: 'Student',
    description: 'Looking for affordable housing near educational institutions',
    icon: 'school-outline',
  },
  {
    id: 'expat',
    title: 'Expat',
    description: 'Relocating to the Netherlands from another country',
    icon: 'airplane-outline',
  },
  {
    id: 'professional',
    title: 'Professional',
    description: 'Working professional seeking quality housing',
    icon: 'briefcase-outline',
  },
];

interface ProfileTypeStepProps {
  selectedProfile: 'student' | 'expat' | 'professional' | null;
  onSelectProfile: (profile: 'student' | 'expat' | 'professional') => void;
}

export const ProfileTypeStep: React.FC<ProfileTypeStepProps> = ({
  selectedProfile,
  onSelectProfile,
}) => {
  // Handle profile selection
  const handleSelectProfile = (profile: 'student' | 'expat' | 'professional') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onSelectProfile(profile);
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>What best describes you?</Text>
      <Text style={styles.subtitle}>
        This helps us personalize your rental search experience
      </Text>
      
      <View style={styles.profilesContainer}>
        {PROFILE_TYPES.map((profile, index) => (
          <Animated.View
            key={profile.id}
            entering={FadeIn.delay(index * 100).duration(400)}
          >
            <TouchableOpacity
              style={[
                styles.profileCard,
                selectedProfile === profile.id && styles.selectedProfileCard,
              ]}
              onPress={() => handleSelectProfile(profile.id as 'student' | 'expat' | 'professional')}
            >
              <View 
                style={[
                  styles.iconContainer,
                  selectedProfile === profile.id && styles.selectedIconContainer,
                ]}
              >
                <Ionicons 
                  name={profile.icon as any} 
                  size={24} 
                  color={selectedProfile === profile.id ? THEME.light : THEME.accent} 
                />
              </View>
              
              <View style={styles.profileContent}>
                <Text 
                  style={[
                    styles.profileTitle,
                    selectedProfile === profile.id && styles.selectedProfileTitle,
                  ]}
                >
                  {profile.title}
                </Text>
                <Text 
                  style={[
                    styles.profileDescription,
                    selectedProfile === profile.id && styles.selectedProfileDescription,
                  ]}
                >
                  {profile.description}
                </Text>
              </View>
              
              <View 
                style={[
                  styles.checkContainer,
                  selectedProfile === profile.id && styles.selectedCheckContainer,
                ]}
              >
                {selectedProfile === profile.id && (
                  <Ionicons name="checkmark" size={20} color={THEME.light} />
                )}
              </View>
            </TouchableOpacity>
          </Animated.View>
        ))}
      </View>
      
      <View style={styles.infoContainer}>
        <Ionicons name="information-circle-outline" size={20} color={THEME.gray} />
        <Text style={styles.infoText}>
          Your profile type helps us suggest appropriate properties and budget ranges
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 24,
  },
  profilesContainer: {
    marginBottom: 24,
  },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedProfileCard: {
    backgroundColor: 'rgba(67, 97, 238, 0.05)',
    borderColor: THEME.primary,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  selectedIconContainer: {
    backgroundColor: THEME.accent,
  },
  profileContent: {
    flex: 1,
  },
  profileTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  selectedProfileTitle: {
    color: THEME.primary,
  },
  profileDescription: {
    fontSize: 14,
    color: THEME.gray,
  },
  selectedProfileDescription: {
    color: '#4b5563',
  },
  checkContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedCheckContainer: {
    backgroundColor: THEME.primary,
    borderColor: THEME.primary,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: THEME.gray,
    marginLeft: 8,
    lineHeight: 20,
  },
});