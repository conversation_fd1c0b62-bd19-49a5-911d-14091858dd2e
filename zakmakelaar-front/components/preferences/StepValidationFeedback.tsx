import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';

interface StepValidationFeedbackProps {
  isValid: boolean;
  message: string;
  showIcon?: boolean;
}

export const StepValidationFeedback: React.FC<StepValidationFeedbackProps> = ({
  isValid,
  message,
  showIcon = true
}) => {
  if (!message) return null;
  
  return (
    <Animated.View 
      style={[
        styles.container,
        isValid ? styles.validContainer : styles.invalidContainer
      ]}
      entering={FadeIn.duration(300)}
      exiting={FadeOut.duration(300)}
    >
      {showIcon && (
        <Ionicons
          name={isValid ? "checkmark-circle" : "alert-circle"}
          size={20}
          color={isValid ? "#10b981" : "#ef4444"}
          style={styles.icon}
        />
      )}
      <Text style={[
        styles.message,
        isValid ? styles.validMessage : styles.invalidMessage
      ]}>
        {message}
      </Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    marginVertical: 8,
  },
  validContainer: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(16, 185, 129, 0.2)',
  },
  invalidContainer: {
    backgroundColor: 'rgba(239, 68, 68, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(239, 68, 68, 0.2)',
  },
  icon: {
    marginRight: 8,
  },
  message: {
    fontSize: 14,
    flex: 1,
    lineHeight: 20,
  },
  validMessage: {
    color: '#10b981',
  },
  invalidMessage: {
    color: '#ef4444',
  },
});

export default StepValidationFeedback;