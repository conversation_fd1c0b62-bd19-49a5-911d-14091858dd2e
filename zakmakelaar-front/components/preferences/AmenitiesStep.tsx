import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

// Amenities with icons
const AMENITIES = [
  {
    id: 'furnished',
    title: 'Furnished',
    icon: 'bed-outline',
  },
  {
    id: 'parking',
    title: 'Parking',
    icon: 'car-outline',
  },
  {
    id: 'balcony',
    title: 'Balcony',
    icon: 'sunny-outline',
  },
  {
    id: 'garden',
    title: 'Garden',
    icon: 'leaf-outline',
  },
  {
    id: 'pets',
    title: 'Pet<PERSON> Allowed',
    icon: 'paw-outline',
  },
  {
    id: 'internet',
    title: 'Internet',
    icon: 'wifi-outline',
  },
  {
    id: 'washer',
    title: 'Washer',
    icon: 'water-outline',
  },
  {
    id: 'dryer',
    title: 'Dryer',
    icon: 'sunny-outline',
  },
  {
    id: 'dishwasher',
    title: 'Dishwasher',
    icon: 'restaurant-outline',
  },
  {
    id: 'elevator',
    title: 'Elevator',
    icon: 'arrow-up-outline',
  },
  {
    id: 'airconditioning',
    title: 'Air Conditioning',
    icon: 'snow-outline',
  },
  {
    id: 'heating',
    title: 'Heating',
    icon: 'flame-outline',
  },
];

// Amenity categories
const AMENITY_CATEGORIES = [
  {
    id: 'essential',
    title: 'Essential Amenities',
    amenities: ['furnished', 'internet', 'washer', 'heating'],
  },
  {
    id: 'outdoor',
    title: 'Outdoor Features',
    amenities: ['parking', 'balcony', 'garden'],
  },
  {
    id: 'comfort',
    title: 'Comfort & Convenience',
    amenities: ['elevator', 'airconditioning', 'dishwasher', 'dryer'],
  },
  {
    id: 'policy',
    title: 'Policies',
    amenities: ['pets'],
  },
];

interface AmenitiesStepProps {
  selectedAmenities: string[];
  onUpdateAmenities: (amenities: string[]) => void;
}

export const AmenitiesStep: React.FC<AmenitiesStepProps> = ({
  selectedAmenities,
  onUpdateAmenities,
}) => {
  // State
  const [localSelectedAmenities, setLocalSelectedAmenities] = useState<string[]>(selectedAmenities);
  
  // Update local state when props change
  useEffect(() => {
    setLocalSelectedAmenities(selectedAmenities);
  }, [selectedAmenities]);
  
  // Handle amenity selection
  const handleSelectAmenity = (amenityId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    let newSelectedAmenities: string[];
    
    if (localSelectedAmenities.includes(amenityId)) {
      // Remove amenity if already selected
      newSelectedAmenities = localSelectedAmenities.filter(id => id !== amenityId);
    } else {
      // Add amenity if not selected
      newSelectedAmenities = [...localSelectedAmenities, amenityId];
    }
    
    // Update local state
    setLocalSelectedAmenities(newSelectedAmenities);
    
    // Update parent component
    onUpdateAmenities(newSelectedAmenities);
  };
  
  // Get amenity by ID
  const getAmenity = (amenityId: string) => {
    return AMENITIES.find(amenity => amenity.id === amenityId);
  };
  
  // Handle select all in category
  const handleSelectAllInCategory = (categoryId: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    const category = AMENITY_CATEGORIES.find(cat => cat.id === categoryId);
    if (!category) return;
    
    // Check if all amenities in category are already selected
    const allSelected = category.amenities.every(amenityId => 
      localSelectedAmenities.includes(amenityId)
    );
    
    let newSelectedAmenities: string[];
    
    if (allSelected) {
      // Remove all amenities in category
      newSelectedAmenities = localSelectedAmenities.filter(id => 
        !category.amenities.includes(id)
      );
    } else {
      // Add all amenities in category
      const amenityIdsToAdd = category.amenities.filter(id => 
        !localSelectedAmenities.includes(id)
      );
      newSelectedAmenities = [...localSelectedAmenities, ...amenityIdsToAdd];
    }
    
    // Update local state
    setLocalSelectedAmenities(newSelectedAmenities);
    
    // Update parent component
    onUpdateAmenities(newSelectedAmenities);
  };
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Select Preferred Amenities</Text>
      <Text style={styles.subtitle}>
        Choose the amenities that are important to you
      </Text>
      
      {/* Amenities by category */}
      {AMENITY_CATEGORIES.map((category, categoryIndex) => (
        <Animated.View
          key={category.id}
          style={styles.categoryContainer}
          entering={FadeIn.delay(categoryIndex * 100).duration(400)}
        >
          <View style={styles.categoryHeader}>
            <Text style={styles.categoryTitle}>{category.title}</Text>
            <TouchableOpacity
              style={styles.selectAllButton}
              onPress={() => handleSelectAllInCategory(category.id)}
            >
              <Text style={styles.selectAllText}>
                {category.amenities.every(id => localSelectedAmenities.includes(id))
                  ? 'Deselect All'
                  : 'Select All'
                }
              </Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.amenitiesGrid}>
            {category.amenities.map(amenityId => {
              const amenity = getAmenity(amenityId);
              if (!amenity) return null;
              
              const isSelected = localSelectedAmenities.includes(amenityId);
              
              return (
                <TouchableOpacity
                  key={amenityId}
                  style={[
                    styles.amenityCard,
                    isSelected && styles.selectedAmenityCard,
                  ]}
                  onPress={() => handleSelectAmenity(amenityId)}
                >
                  <View 
                    style={[
                      styles.amenityIconContainer,
                      isSelected && styles.selectedAmenityIconContainer,
                    ]}
                  >
                    <Ionicons 
                      name={amenity.icon as any} 
                      size={24} 
                      color={isSelected ? THEME.light : THEME.accent} 
                    />
                  </View>
                  <Text 
                    style={[
                      styles.amenityTitle,
                      isSelected && styles.selectedAmenityTitle,
                    ]}
                  >
                    {amenity.title}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </Animated.View>
      ))}
      
      {/* Selected amenities summary */}
      {localSelectedAmenities.length > 0 && (
        <View style={styles.selectedSummaryContainer}>
          <Text style={styles.selectedSummaryTitle}>
            Selected Amenities ({localSelectedAmenities.length})
          </Text>
          <View style={styles.selectedChipsContainer}>
            {localSelectedAmenities.map(amenityId => {
              const amenity = getAmenity(amenityId);
              if (!amenity) return null;
              
              return (
                <View key={amenityId} style={styles.selectedChip}>
                  <Ionicons name={amenity.icon as any} size={16} color={THEME.accent} />
                  <Text style={styles.selectedChipText}>{amenity.title}</Text>
                </View>
              );
            })}
          </View>
        </View>
      )}
      
      {/* Info message */}
      <View style={styles.infoContainer}>
        <Ionicons name="information-circle-outline" size={20} color={THEME.gray} />
        <Text style={styles.infoText}>
          Selecting amenities helps our AI find properties that match your lifestyle needs
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 24,
  },
  categoryContainer: {
    marginBottom: 24,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  selectAllButton: {
    padding: 4,
  },
  selectAllText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '500',
  },
  amenitiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  amenityCard: {
    width: '50%',
    paddingHorizontal: 4,
    marginBottom: 8,
  },
  amenityCardInner: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    alignItems: 'center',
  },
  selectedAmenityCardInner: {
    backgroundColor: 'rgba(67, 97, 238, 0.05)',
    borderColor: THEME.primary,
  },
  amenityIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  selectedAmenityIconContainer: {
    backgroundColor: THEME.accent,
  },
  amenityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
    textAlign: 'center',
  },
  selectedAmenityTitle: {
    color: THEME.primary,
  },
  selectedSummaryContainer: {
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  selectedSummaryTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 12,
  },
  selectedChipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  selectedChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  selectedChipText: {
    fontSize: 14,
    color: THEME.accent,
    marginLeft: 4,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: THEME.gray,
    marginLeft: 8,
    lineHeight: 20,
  },
  amenityCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    width: '47%',
  },
  selectedAmenityCard: {
    backgroundColor: 'rgba(67, 97, 238, 0.05)',
    borderColor: THEME.primary,
  },
});