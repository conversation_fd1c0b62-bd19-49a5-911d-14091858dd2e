import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
// import { useAuthStore } from '../../store/authStore'; // Commented out as it's not used
import { propertyOwnerService } from '../../services';
import { SimpleFormInput } from '../SimpleFormInput';
import { PrimaryButton } from '../PrimaryButton';

interface PropertyOwnerRegistrationData {
  businessRegistration: string;
  companyName: string;
  address: string;
  phone: string;
  website?: string;
  description?: string;
}

export const PropertyOwnerRegistrationScreen: React.FC = () => {
  const router = useRouter();
  // const { user } = useAuthStore(); // Commented out as it's not used in this component
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<PropertyOwnerRegistrationData>({
    businessRegistration: '',
    companyName: '',
    address: '',
    phone: '',
    website: '',
    description: '',
  });

  const handleInputChange = (field: keyof PropertyOwnerRegistrationData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    if (!formData.businessRegistration.trim()) {
      Alert.alert('Error', 'Business registration number is required');
      return false;
    }
    if (!formData.companyName.trim()) {
      Alert.alert('Error', 'Company name is required');
      return false;
    }
    if (!formData.address.trim()) {
      Alert.alert('Error', 'Business address is required');
      return false;
    }
    if (!formData.phone.trim()) {
      Alert.alert('Error', 'Contact phone is required');
      return false;
    }
    return true;
  };

  const handleRegistration = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      await propertyOwnerService.registerPropertyOwner(formData); // Not storing result as it's not used
      Alert.alert(
        'Success',
        'Property owner registration completed successfully!',
        [{ text: 'OK', onPress: () => router.push('/property-owner/dashboard') }]
      );
    } catch (error: any) {
      Alert.alert('Registration Error', error.message || 'Failed to register as property owner');
    } finally {
      setLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#4361ee" />
          </TouchableOpacity>
          <Text style={styles.title}>Property Owner Registration</Text>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Business Information</Text>
          
          <SimpleFormInput

            label="Business Registration Number (KVK)"
            placeholder="Enter your KVK number"
            value={formData.businessRegistration}
            onChangeText={(text: string) => handleInputChange('businessRegistration', text)}
            keyboardType="numeric"
            // required - removed as not supported by SimpleFormInput
          />

          <SimpleFormInput

            label="Company Name"
            placeholder="Enter your company name"
            value={formData.companyName}
            onChangeText={(text: string) => handleInputChange('companyName', text)}
            // required - removed as not supported by SimpleFormInput
          />

          <SimpleFormInput

            label="Business Address"
            placeholder="Enter your business address"
            value={formData.address}
            onChangeText={(text: string) => handleInputChange('address', text)}
            multiline
            // required - removed as not supported by SimpleFormInput
          />

          <SimpleFormInput

            label="Contact Phone"
            placeholder="Enter your phone number"
            value={formData.phone}
            onChangeText={(text: string) => handleInputChange('phone', text)}
            keyboardType="phone-pad"
            // required - removed as not supported by SimpleFormInput
          />

          <SimpleFormInput

            label="Website (Optional)"
            placeholder="Enter your company website"
            value={formData.website}
            onChangeText={(text: string) => handleInputChange('website', text)}
            keyboardType="url"
          />

          <SimpleFormInput

            label="Description (Optional)"
            placeholder="Tell us about your property business"
            value={formData.description}
            onChangeText={(text: string) => handleInputChange('description', text)}
            multiline
            numberOfLines={4}
          />

          <PrimaryButton
            title={loading ? 'Processing...' : 'Register as Property Owner'}
            onPress={handleRegistration}
            disabled={loading}
            isLoading={loading}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
    paddingTop: 20,
  },
  backButton: {
    marginRight: 15,
    padding: 5,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 20,
  },
});
