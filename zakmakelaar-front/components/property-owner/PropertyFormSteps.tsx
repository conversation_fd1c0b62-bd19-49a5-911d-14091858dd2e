import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Switch,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface PropertyFormData {
  title: string;
  description: string;
  address: {
    street: string;
    houseNumber: string;
    postalCode: string;
    city: string;
    province: string;
  };
  propertyType: 'apartment' | 'house' | 'studio' | 'room';
  size: string;
  rooms: string;
  bedrooms: string;
  bathrooms: string;
  rent: {
    amount: string;
    deposit: string;
    utilities: string;
    serviceCharges: string;
  };
  features: {
    furnished: boolean;
    interior: 'kaal' | 'gestoffeerd' | 'gemeubileerd';
    parking: boolean;
    balcony: boolean;
    garden: boolean;
    elevator: boolean;
    energyLabel: string;
  };
  policies: {
    petsAllowed: boolean;
    smokingAllowed: boolean;
    studentsAllowed: boolean;
    expatFriendly: boolean;
    minimumIncome: string;
    maximumOccupants: string;
  };
  photos: {
    uri: string;
    name: string;
    type: string;
    isPrimary: boolean;
  }[];
}

interface PropertyFormStepsProps {
  formData: PropertyFormData;
  setFormData: React.Dispatch<React.SetStateAction<PropertyFormData>>;
  updateFormData: (section: keyof PropertyFormData, field: string, value: any) => void;
  takePhoto: () => void;
  selectFromGallery: () => void;
  removePhoto: (index: number) => void;
  setPrimaryPhoto: (index: number) => void;
}

export const PropertyFormSteps = {
  Step1: ({ formData, setFormData }: PropertyFormStepsProps) => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Basic Information</Text>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Property Title *</Text>
        <TextInput
          style={styles.input}
          value={formData.title}
          onChangeText={(value) => setFormData(prev => ({ ...prev, title: value }))}
          placeholder="e.g., Modern Apartment in City Center"
          placeholderTextColor="#999"
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Description *</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={formData.description}
          onChangeText={(value) => setFormData(prev => ({ ...prev, description: value }))}
          placeholder="Describe your property, its features, and location..."
          placeholderTextColor="#999"
          multiline
          numberOfLines={4}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Property Type *</Text>
        <View style={styles.propertyTypeContainer}>
          {(['apartment', 'house', 'studio', 'room'] as const).map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.propertyTypeButton,
                formData.propertyType === type && styles.propertyTypeButtonActive,
              ]}
              onPress={() => setFormData(prev => ({ ...prev, propertyType: type }))}
            >
              <Text
                style={[
                  styles.propertyTypeText,
                  formData.propertyType === type && styles.propertyTypeTextActive,
                ]}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  ),

  Step2: ({ formData, updateFormData, setFormData }: PropertyFormStepsProps) => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Address & Details</Text>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 3 }]}>
          <Text style={styles.label}>Street *</Text>
          <TextInput
            style={styles.input}
            value={formData.address.street}
            onChangeText={(value) => updateFormData('address', 'street', value)}
            placeholder="Street name"
            placeholderTextColor="#999"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Number *</Text>
          <TextInput
            style={styles.input}
            value={formData.address.houseNumber}
            onChangeText={(value) => updateFormData('address', 'houseNumber', value)}
            placeholder="123"
            placeholderTextColor="#999"
          />
        </View>
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Postal Code *</Text>
          <TextInput
            style={styles.input}
            value={formData.address.postalCode}
            onChangeText={(value) => updateFormData('address', 'postalCode', value)}
            placeholder="1234AB"
            placeholderTextColor="#999"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 2, marginLeft: 12 }]}>
          <Text style={styles.label}>City *</Text>
          <TextInput
            style={styles.input}
            value={formData.address.city}
            onChangeText={(value) => updateFormData('address', 'city', value)}
            placeholder="Amsterdam"
            placeholderTextColor="#999"
          />
        </View>
      </View>

      <View style={styles.inputGroup}>
        <Text style={styles.label}>Province</Text>
        <TextInput
          style={styles.input}
          value={formData.address.province}
          onChangeText={(value) => updateFormData('address', 'province', value)}
          placeholder="Noord-Holland"
          placeholderTextColor="#999"
        />
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Size (m²) *</Text>
          <TextInput
            style={styles.input}
            value={formData.size}
            onChangeText={(value) => setFormData(prev => ({ ...prev, size: value }))}
            placeholder="75"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Rooms *</Text>
          <TextInput
            style={styles.input}
            value={formData.rooms}
            onChangeText={(value) => setFormData(prev => ({ ...prev, rooms: value }))}
            placeholder="3"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Bedrooms *</Text>
          <TextInput
            style={styles.input}
            value={formData.bedrooms}
            onChangeText={(value) => setFormData(prev => ({ ...prev, bedrooms: value }))}
            placeholder="2"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Bathrooms *</Text>
          <TextInput
            style={styles.input}
            value={formData.bathrooms}
            onChangeText={(value) => setFormData(prev => ({ ...prev, bathrooms: value }))}
            placeholder="1"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  ),

  Step3: ({ formData, updateFormData }: PropertyFormStepsProps) => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Rental Information</Text>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Monthly Rent (€) *</Text>
          <TextInput
            style={styles.input}
            value={formData.rent.amount}
            onChangeText={(value) => updateFormData('rent', 'amount', value)}
            placeholder="1200"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Deposit (€) *</Text>
          <TextInput
            style={styles.input}
            value={formData.rent.deposit}
            onChangeText={(value) => updateFormData('rent', 'deposit', value)}
            placeholder="2400"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Utilities (€)</Text>
          <TextInput
            style={styles.input}
            value={formData.rent.utilities}
            onChangeText={(value) => updateFormData('rent', 'utilities', value)}
            placeholder="150"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Service Charges (€)</Text>
          <TextInput
            style={styles.input}
            value={formData.rent.serviceCharges}
            onChangeText={(value) => updateFormData('rent', 'serviceCharges', value)}
            placeholder="50"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  ),

  Step4: ({ formData, updateFormData }: PropertyFormStepsProps) => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Features & Policies</Text>

      <Text style={styles.sectionTitle}>Property Features</Text>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Furnished</Text>
        <Switch
          value={formData.features.furnished}
          onValueChange={(value) => updateFormData('features', 'furnished', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Parking</Text>
        <Switch
          value={formData.features.parking}
          onValueChange={(value) => updateFormData('features', 'parking', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Balcony</Text>
        <Switch
          value={formData.features.balcony}
          onValueChange={(value) => updateFormData('features', 'balcony', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Garden</Text>
        <Switch
          value={formData.features.garden}
          onValueChange={(value) => updateFormData('features', 'garden', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Elevator</Text>
        <Switch
          value={formData.features.elevator}
          onValueChange={(value) => updateFormData('features', 'elevator', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <Text style={styles.sectionTitle}>Rental Policies</Text>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Pets Allowed</Text>
        <Switch
          value={formData.policies.petsAllowed}
          onValueChange={(value) => updateFormData('policies', 'petsAllowed', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Smoking Allowed</Text>
        <Switch
          value={formData.policies.smokingAllowed}
          onValueChange={(value) => updateFormData('policies', 'smokingAllowed', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Students Welcome</Text>
        <Switch
          value={formData.policies.studentsAllowed}
          onValueChange={(value) => updateFormData('policies', 'studentsAllowed', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={styles.switchLabel}>Expat Friendly</Text>
        <Switch
          value={formData.policies.expatFriendly}
          onValueChange={(value) => updateFormData('policies', 'expatFriendly', value)}
          trackColor={{ false: '#E5E5E5', true: '#007AFF' }}
          thumbColor="#FFFFFF"
        />
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, { flex: 1 }]}>
          <Text style={styles.label}>Min. Income (€)</Text>
          <TextInput
            style={styles.input}
            value={formData.policies.minimumIncome}
            onChangeText={(value) => updateFormData('policies', 'minimumIncome', value)}
            placeholder="3600"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
        <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
          <Text style={styles.label}>Max. Occupants</Text>
          <TextInput
            style={styles.input}
            value={formData.policies.maximumOccupants}
            onChangeText={(value) => updateFormData('policies', 'maximumOccupants', value)}
            placeholder="2"
            placeholderTextColor="#999"
            keyboardType="numeric"
          />
        </View>
      </View>
    </View>
  ),

  Step5: ({ formData, takePhoto, selectFromGallery, removePhoto, setPrimaryPhoto }: PropertyFormStepsProps) => (
    <View style={styles.stepContent}>
      <Text style={styles.stepTitle}>Property Photos</Text>
      <Text style={styles.sectionSubtitle}>Add photos to showcase your property</Text>

      {/* Photo Actions */}
      <View style={styles.photoActions}>
        <TouchableOpacity style={styles.photoActionButton} onPress={takePhoto}>
          <Ionicons name="camera" size={24} color="#007AFF" />
          <Text style={styles.photoActionText}>Take Photo</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.photoActionButton} onPress={selectFromGallery}>
          <Ionicons name="images" size={24} color="#007AFF" />
          <Text style={styles.photoActionText}>Choose from Gallery</Text>
        </TouchableOpacity>
      </View>

      {/* Photo Grid */}
      {formData.photos.length > 0 && (
        <View style={styles.photoGrid}>
          <Text style={styles.sectionTitle}>Selected Photos ({formData.photos.length})</Text>
          <View style={styles.photoList}>
            {formData.photos.map((item, index) => (
              <View key={index} style={styles.photoItem}>
                <Image source={{ uri: item.uri }} style={styles.photoImage} />

                {/* Primary Photo Badge */}
                {item.isPrimary && (
                  <View style={styles.primaryBadge}>
                    <Text style={styles.primaryBadgeText}>Primary</Text>
                  </View>
                )}

                {/* Photo Actions */}
                <View style={styles.photoItemActions}>
                  {!item.isPrimary && (
                    <TouchableOpacity
                      style={styles.photoActionIcon}
                      onPress={() => setPrimaryPhoto(index)}
                    >
                      <Ionicons name="star-outline" size={20} color="#FFF" />
                    </TouchableOpacity>
                  )}

                  <TouchableOpacity
                    style={[styles.photoActionIcon, styles.removeAction]}
                    onPress={() => removePhoto(index)}
                  >
                    <Ionicons name="trash-outline" size={20} color="#FFF" />
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Photo Tips */}
      <View style={styles.photoTips}>
        <Text style={styles.tipsTitle}>📸 Photo Tips</Text>
        <Text style={styles.tipText}>• Take photos in good lighting</Text>
        <Text style={styles.tipText}>• Show different rooms and angles</Text>
        <Text style={styles.tipText}>• Include exterior and interior views</Text>
        <Text style={styles.tipText}>• First photo will be the main listing photo</Text>
      </View>
    </View>
  ),
};

const styles = StyleSheet.create({
  stepContent: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    margin: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A1A1A',
    marginTop: 20,
    marginBottom: 12,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1A1A1A',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  propertyTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  propertyTypeButton: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    backgroundColor: '#FFFFFF',
  },
  propertyTypeButtonActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  propertyTypeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
  },
  propertyTypeTextActive: {
    color: '#FFFFFF',
  },
  switchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  switchLabel: {
    fontSize: 16,
    color: '#333333',
    flex: 1,
  },
  // Photo styles
  photoActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  photoActionButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    minWidth: 120,
  },
  photoActionText: {
    color: '#007AFF',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
  },
  photoGrid: {
    marginBottom: 24,
  },
  photoList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingTop: 12,
    justifyContent: 'space-between',
  },
  photoItem: {
    width: '48%',
    marginBottom: 12,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#F0F0F0',
    position: 'relative',
  },
  photoImage: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  primaryBadge: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#007AFF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  primaryBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  photoItemActions: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    gap: 8,
  },
  photoActionIcon: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 16,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeAction: {
    backgroundColor: 'rgba(255, 59, 48, 0.8)',
  },
  photoTips: {
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 12,
  },
  tipText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
    lineHeight: 20,
  },
});