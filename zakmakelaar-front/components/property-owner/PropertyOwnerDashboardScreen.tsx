import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useAuthStore } from '../../store/authStore';
import { propertyOwnerService } from '../../services/propertyOwnerService';
import { PropertyMetricsCard } from './PropertyMetricsCard';
import { PropertyCard } from './PropertyCard';
import { TenantApplicationCard } from './TenantApplicationCard';
import { EmptyStateCard } from '../dashboard/EmptyStateCard';
import { PrimaryButton } from '../PrimaryButton';

const { width } = Dimensions.get('window');

interface PropertyOwnerDashboardData {
  owner: {
    id: string;
    name: string;
    email: string;
    isVerified: boolean;
    totalProperties: number;
    totalApplications: number;
    occupancyRate: number;
  };
  properties: any[];
  applications: any[];
  recentActivity: any[];
}

export const PropertyOwnerDashboardScreen: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState<PropertyOwnerDashboardData | null>(null);

  const loadDashboardData = useCallback(async () => {
    try {
      const data = await propertyOwnerService.getDashboardData();
      setDashboardData(data);
    } catch (error: any) {
      Alert.alert('Error', error.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  }, [loadDashboardData]);

  const handleAddProperty = () => {
    // Using as any to bypass TypeScript route checking
    router.push('/property-owner/add-property' as any);
  };

  const handlePropertyPress = (property: any) => {
    // Using as any to bypass TypeScript route checking
    router.push(`/property-owner/property/${property.id}` as any);
  };

  const handleApplicationPress = (application: any) => {
    // Using as any to bypass TypeScript route checking
    router.push(`/property-owner/application/${application.id}` as any);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading dashboard...</Text>
      </View>
    );
  }

  if (!dashboardData) {
    return (
      <EmptyStateCard
        title="Dashboard Unavailable"
        message="Unable to load your property owner dashboard"
        icon="alert-circle"
        actionLabel="Retry"
        onAction={loadDashboardData}
      />
    );
  }

  const { owner, properties, applications, recentActivity } = dashboardData;

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.title}>Property Owner Dashboard</Text>
        <View style={styles.verificationBadge}>
          <Ionicons
            name={owner.isVerified ? 'checkmark-circle' : 'alert-circle'}
            size={16}
            color={owner.isVerified ? '#10b981' : '#f59e0b'}
          />
          <Text style={styles.verificationText}>
            {owner.isVerified ? 'Verified' : 'Pending Verification'}
          </Text>
        </View>
      </View>

      {/* Metrics Overview */}
      <View style={styles.metricsContainer}>
        <PropertyMetricsCard
          title="Total Properties"
          value={owner.totalProperties.toString()}
          icon="home"
          color="#4361ee"
        />
        <PropertyMetricsCard
          title="Active Applications"
          value={owner.totalApplications.toString()}
          icon="people"
          color="#10b981"
        />
        <PropertyMetricsCard
          title="Occupancy Rate"
          value={`${owner.occupancyRate}%`}
          icon="trending-up"
          color="#f72585"
        />
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionsRow}>
          <PrimaryButton
            title="Add Property"
            onPress={handleAddProperty}
            icon="add"
            style={styles.actionButton}
          />
          <PrimaryButton
            title="View Applications"
            onPress={() => router.push('/property-owner/applications' as any)}
            icon="list"
            style={styles.actionButton}
            variant="secondary"
          />
        </View>
      </View>

      {/* Properties Section */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Your Properties</Text>
          <TouchableOpacity onPress={() => router.push('/property-owner/properties' as any)}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        
        {properties.length === 0 ? (
          <EmptyStateCard
            title="No Properties Yet"
            message="Start by adding your first property"
            icon="home-outline"
            actionLabel="Add Property"
            onAction={handleAddProperty}
          />
        ) : (
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {properties.map((property) => (
              <PropertyCard
                key={property.id}
                property={property}
                onPress={() => handlePropertyPress(property)}
              />
            ))}
          </ScrollView>
        )}
      </View>

      {/* Recent Applications */}
      <View style={styles.sectionContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Applications</Text>
          <TouchableOpacity onPress={() => router.push('/property-owner/applications' as any)}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        
        {applications.length === 0 ? (
          <EmptyStateCard
            title="No Applications Yet"
            message="Applications will appear here when tenants apply" icon={''}          />
        ) : (
          applications.slice(0, 3).map((application) => (
            <TenantApplicationCard
              key={application.id}
              application={application}
              onPress={() => handleApplicationPress(application)}
            />
          ))
        )}
      </View>

      {/* Recent Activity */}
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Recent Activity</Text>
        {recentActivity.length === 0 ? (
          <Text style={styles.emptyText}>No recent activity</Text>
        ) : (
          recentActivity.map((activity, index) => (
            <View key={index} style={styles.activityItem}>
              <Ionicons name="time" size={16} color="#6b7280" />
              <Text style={styles.activityText}>{activity.description}</Text>
              <Text style={styles.activityTime}>{activity.time}</Text>
            </View>
          ))
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
  },
  verificationText: {
    fontSize: 12,
    color: '#6b7280',
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    gap: 10,
  },
  quickActionsContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginVertical: 10,
    padding: 15,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 15,
  },
  actionsRow: {
    flexDirection: 'row',
    gap: 10,
  },
  actionButton: {
    flex: 1,
  },
  sectionContainer: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginVertical: 10,
    padding: 15,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  viewAllText: {
    color: '#4361ee',
    fontSize: 14,
    fontWeight: '500',
  },
  emptyText: {
    color: '#6b7280',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 20,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  activityText: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
  },
  activityTime: {
    fontSize: 12,
    color: '#9ca3af',
  },
});
