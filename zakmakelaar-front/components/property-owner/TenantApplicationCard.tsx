import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface TenantApplication {
  id: string;
  applicantName: string;
  applicantEmail: string;
  applicantPhone: string;
  applicantPhoto?: string;
  moveInDate: string;
  duration: string;
  budget: number;
  employmentStatus: string;
  monthlyIncome: number;
  message: string;
  status: 'pending' | 'approved' | 'rejected';
  submittedAt: string;
  documents: string[];
  propertyTitle: string;
}

interface TenantApplicationCardProps {
  application: TenantApplication;
  onPress: () => void;
  onApprove?: () => void;
  onReject?: () => void;
  compact?: boolean;
}

export const TenantApplicationCard: React.FC<TenantApplicationCardProps> = ({
  application,
  onPress,
  onApprove,
  onReject,
  compact = false,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return '#10b981';
      case 'rejected':
        return '#ef4444';
      case 'pending':
        return '#f59e0b';
      default:
        return '#6b7280';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('nl-NL', {
      style: 'currency',
      currency: 'EUR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('nl-NL', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.header}>
        <View style={styles.applicantInfo}>
          {application.applicantPhoto ? (
            <Image
              source={{ uri: application.applicantPhoto }}
              style={styles.applicantPhoto}
            />
          ) : (
            <View style={styles.photoPlaceholder}>
              <Ionicons name="person" size={24} color="#9ca3af" />
            </View>
          )}
          
          <View style={styles.nameContainer}>
            <Text style={styles.applicantName}>{application.applicantName}</Text>
            <Text style={styles.propertyTitle}>{application.propertyTitle}</Text>
          </View>
        </View>
        
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(application.status) }]}>
          <Text style={styles.statusText}>{application.status}</Text>
        </View>
      </View>

      {!compact && (
        <View style={styles.details}>
          <View style={styles.detailRow}>
            <Ionicons name="calendar" size={16} color="#6b7280" />
            <Text style={styles.detailText}>
              Move-in: {formatDate(application.moveInDate)}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="cash" size={16} color="#6b7280" />
            <Text style={styles.detailText}>
              Budget: {formatCurrency(application.budget)}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="briefcase" size={16} color="#6b7280" />
            <Text style={styles.detailText}>
              {application.employmentStatus}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Ionicons name="time" size={16} color="#6b7280" />
            <Text style={styles.detailText}>
              Duration: {application.duration}
            </Text>
          </View>
        </View>
      )}

      <View style={styles.footer}>
        <Text style={styles.submittedDate}>
          Applied: {formatDate(application.submittedAt)}
        </Text>
        
        {(onApprove || onReject) && application.status === 'pending' && (
          <View style={styles.actions}>
            <TouchableOpacity
              style={[styles.actionButton, styles.approveButton]}
              onPress={onApprove}
            >
              <Ionicons name="checkmark" size={16} color="#fff" />
              <Text style={styles.actionText}>Approve</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, styles.rejectButton]}
              onPress={onReject}
            >
              <Ionicons name="close" size={16} color="#fff" />
              <Text style={styles.actionText}>Reject</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {application.message && !compact && (
        <View style={styles.messageContainer}>
          <Text style={styles.messageLabel}>Message:</Text>
          <Text style={styles.messageText} numberOfLines={2}>
            {application.message}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    marginHorizontal: 20,
    marginVertical: 8,
    padding: 15,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  applicantInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  applicantPhoto: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  photoPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nameContainer: {
    flex: 1,
  },
  applicantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  propertyTitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  details: {
    gap: 8,
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#374151',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
  submittedDate: {
    fontSize: 12,
    color: '#9ca3af',
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  approveButton: {
    backgroundColor: '#10b981',
  },
  rejectButton: {
    backgroundColor: '#ef4444',
  },
  actionText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  messageContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  messageLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 4,
  },
  messageText: {
    fontSize: 14,
    color: '#6b7280',
  },
});
