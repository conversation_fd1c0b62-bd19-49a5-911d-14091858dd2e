import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { authService } from '../services/authService';

export default function SimpleConnectionTest() {
  const [results, setResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (result: string) => {
    setResults(prev => [...prev, result]);
    console.log(result);
  };

  const testConnection = async () => {
    setIsLoading(true);
    setResults([]);
    
    try {
      addResult('🚀 Starting connection test...');
      
      // Test registration
      const testEmail = `test${Date.now()}@example.com`;
      const testPassword = 'TestPass123';
      
      addResult(`📧 Testing with email: ${testEmail}`);
      
      const registerResult = await authService.register({
        email: testEmail,
        password: testPassword,
        firstName: 'Test',
        lastName: 'User'
      });
      
      if (registerResult.success) {
        addResult('✅ Registration: SUCCESS');
        addResult(`👤 User created: ${registerResult.data?.user?.email}`);
        addResult(`🔑 Token received: ${registerResult.data?.token ? 'YES' : 'NO'}`);
        
        // Test logout
        await authService.logout();
        addResult('✅ Logout: SUCCESS');
        
        // Test login
        const loginResult = await authService.login({
          email: testEmail,
          password: testPassword
        });
        
        if (loginResult.success) {
          addResult('✅ Login: SUCCESS');
          addResult(`🔑 Token received: ${loginResult.data?.token ? 'YES' : 'NO'}`);
          
          // Test get current user
          const userResult = await authService.getCurrentUser();
          if (userResult.success) {
            addResult('✅ Get User: SUCCESS');
            addResult(`👤 User email: ${userResult.data?.email}`);
          } else {
            addResult('❌ Get User: FAILED');
            addResult(`Error: ${userResult.message || userResult.error}`);
          }
        } else {
          addResult('❌ Login: FAILED');
          addResult(`Error: ${loginResult.message || loginResult.error}`);
        }
      } else {
        addResult('❌ Registration: FAILED');
        addResult(`Error: ${registerResult.message || registerResult.error}`);
      }
      
      addResult('🎉 Test completed!');
      
    } catch (error: any) {
      addResult('💥 Test failed with exception');
      addResult(`Error: ${error.message}`);
      console.error('Connection test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Simple Connection Test</Text>
      
      <TouchableOpacity 
        style={[styles.button, isLoading && styles.buttonDisabled]} 
        onPress={testConnection}
        disabled={isLoading}
      >
        <Text style={styles.buttonText}>
          {isLoading ? 'Testing...' : 'Test Connection'}
        </Text>
      </TouchableOpacity>
      
      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
        {results.length === 0 && (
          <Text style={styles.emptyText}>
            No results yet. Tap "Test Connection" to start.
          </Text>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f3f4f6',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 20,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#f72585',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonDisabled: {
    backgroundColor: '#9ca3af',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
  },
  resultText: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  emptyText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
});
