import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import Animated, { 
  useAnimatedStyle, 
  withTiming,
  FadeIn,
  interpolate
} from 'react-native-reanimated';

interface OnboardingProgressProps {
  currentStep: number;
  totalSteps: number;
  stepTitles?: string[];
}

export const OnboardingProgress: React.FC<OnboardingProgressProps> = ({
  currentStep,
  totalSteps,
  stepTitles = []
}) => {
  // Calculate progress percentage
  const progress = currentStep / totalSteps;
  
  // Animated style for progress bar
  const progressBarStyle = useAnimatedStyle(() => {
    return {
      width: `${progress * 100}%`,
    };
  });

  return (
    <Animated.View 
      style={styles.container}
      entering={FadeIn.duration(400)}
    >
      {/* Step indicator */}
      <View style={styles.stepsContainer}>
        <Text style={styles.stepText}>
          Step {currentStep} of {totalSteps}
        </Text>
        
        {stepTitles[currentStep - 1] && (
          <Text style={styles.stepTitle}>{stepTitles[currentStep - 1]}</Text>
        )}
      </View>
      
      {/* Progress bar */}
      <View style={styles.progressBarContainer}>
        <Animated.View 
          style={[styles.progressBar, progressBarStyle]} 
        />
      </View>
      
      {/* Step dots */}
      <View style={styles.dotsContainer}>
        {Array.from({ length: totalSteps }).map((_, index) => (
          <View 
            key={index}
            style={[
              styles.dot,
              index < currentStep ? styles.dotCompleted : null,
              index === currentStep - 1 ? styles.dotCurrent : null
            ]}
          />
        ))}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: 24,
  },
  stepsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stepText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  stepTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#f72585',
  },
  progressBarContainer: {
    height: 4,
    backgroundColor: '#e5e7eb',
    borderRadius: 2,
    overflow: 'hidden',
    marginBottom: 12,
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#f72585',
    borderRadius: 2,
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#e5e7eb',
    marginHorizontal: 4,
  },
  dotCompleted: {
    backgroundColor: '#f72585',
  },
  dotCurrent: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#f72585',
    borderWidth: 2,
    borderColor: 'rgba(247, 37, 133, 0.3)',
  },
});