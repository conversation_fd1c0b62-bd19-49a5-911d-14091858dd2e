import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  ViewStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
} from 'react-native-reanimated';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

interface AIMatchScoreIndicatorProps {
  score: number;
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
}

export const AIMatchScoreIndicator: React.FC<AIMatchScoreIndicatorProps> = ({
  score,
  size = 'medium',
  style,
}) => {
  // Animation value
  const scoreAnimation = useSharedValue(0);
  
  // Start animation when component mounts
  React.useEffect(() => {
    scoreAnimation.value = withTiming(score / 100, { 
      duration: 1000, 
      easing: Easing.bezier(0.25, 0.1, 0.25, 1) 
    });
  }, [score]);
  
  // Get score color
  const getScoreColor = (score: number) => {
    if (score >= 80) return '#10b981'; // Green
    if (score >= 60) return '#f59e0b'; // Yellow
    return '#ef4444'; // Red
  };
  
  // Get score label
  const getScoreLabel = (score: number) => {
    if (score >= 90) return 'Perfect Match';
    if (score >= 80) return 'Excellent Match';
    if (score >= 70) return 'Great Match';
    if (score >= 60) return 'Good Match';
    if (score >= 50) return 'Fair Match';
    return 'Basic Match';
  };
  
  // Get size-specific styles
  const getSizeStyles = (size: string) => {
    switch (size) {
      case 'small':
        return {
          container: {
            paddingVertical: 4,
            paddingHorizontal: 8,
            borderRadius: 8,
          },
          text: {
            fontSize: 12,
          },
          label: {
            fontSize: 8,
          },
        };
      case 'large':
        return {
          container: {
            paddingVertical: 8,
            paddingHorizontal: 16,
            borderRadius: 16,
          },
          text: {
            fontSize: 20,
          },
          label: {
            fontSize: 12,
          },
        };
      default: // medium
        return {
          container: {
            paddingVertical: 6,
            paddingHorizontal: 12,
            borderRadius: 12,
          },
          text: {
            fontSize: 16,
          },
          label: {
            fontSize: 10,
          },
        };
    }
  };
  
  const sizeStyles = getSizeStyles(size);
  const scoreColor = getScoreColor(score);
  const scoreLabel = getScoreLabel(score);
  
  return (
    <View 
      style={[
        styles.container, 
        sizeStyles.container,
        { backgroundColor: `${scoreColor}20` }, // 20% opacity
        style
      ]}
    >
      <Text style={[styles.scoreText, sizeStyles.text, { color: scoreColor }]}>
        {Math.round(score)}%
      </Text>
      {size !== 'small' && (
        <Text style={[styles.scoreLabel, sizeStyles.label, { color: scoreColor }]}>
          {scoreLabel}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  scoreText: {
    fontWeight: '700',
  },
  scoreLabel: {
    fontWeight: '500',
  },
});