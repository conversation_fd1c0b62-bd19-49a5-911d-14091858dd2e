import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
  Alert,
  Modal,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInRight,
  SlideOutLeft,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { useAuthStore } from '../../store/authStore';
import { useAIStore } from '../../store/aiStore';
import { useListingsStore } from '../../store/listingsStore';
import { useNotificationStore } from '../../store/notificationStore';
import { AutonomousStatusIndicator } from '../autonomous/AutonomousStatusIndicator';
import { PropertyMatchCard } from './PropertyMatchCard';
import { AIMatchScoreIndicator } from './AIMatchScoreIndicator';
import { PropertyMatchDetails } from './PropertyMatchDetails';
import { enhancedAIMatchingService, EnhancedPropertyMatch, MarketInsight } from '../../services/enhancedAIMatchingService';
import { realTimeMatchingService } from '../../services/realTimeMatchingService';
import { listingsService } from '../../services/listingsService';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
};

const { width, height } = Dimensions.get('window');

interface EnhancedAIDashboardProps {
  onUpdatePreferences?: () => void;
  onViewApplications?: () => void;
}

export const EnhancedAIDashboard: React.FC<EnhancedAIDashboardProps> = ({
  onUpdatePreferences,
  onViewApplications,
}) => {
  const router = useRouter();
  const { user } = useAuthStore();
  const { listings } = useListingsStore();
  const { addNotification } = useNotificationStore();
  
  // State
  const [enhancedMatches, setEnhancedMatches] = useState<EnhancedPropertyMatch[]>([]);
  const [marketInsights, setMarketInsights] = useState<MarketInsight[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<EnhancedPropertyMatch | null>(null);
  const [realTimeStatus, setRealTimeStatus] = useState({
    isRunning: false,
    lastUpdate: null as Date | null,
    notificationsSentToday: 0,
  });
  const [showNoMatchesModal, setShowNoMatchesModal] = useState(false);
  
  // Initialize enhanced matching
  useEffect(() => {
    initializeEnhancedMatching();
    startRealTimeUpdates();
    
    return () => {
      realTimeMatchingService.stopRealTimeUpdates();
    };
  }, []);

  const initializeEnhancedMatching = async () => {
    if (!user?.preferences) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Get latest listings
      const latestListings = await listingsService.getListings({
        limit: 50,
        sortBy: 'dateAdded',
        sortOrder: 'desc'
      });

      // Find enhanced matches
      const matches = await enhancedAIMatchingService.findMatches(
        user.preferences,
        user,
        latestListings.data?.listings || []
      );

      setEnhancedMatches(matches);

      // Generate market insights
      if (matches.length === 0) {
        const insights = await enhancedAIMatchingService.generateMarketInsights(
          user.preferences,
          matches
        );
        setMarketInsights(insights);
        setShowNoMatchesModal(true);
      } else {
        const insights = await enhancedAIMatchingService.generateMarketInsights(
          user.preferences,
          matches
        );
        setMarketInsights(insights);
      }

    } catch (error) {
      console.error('Enhanced matching initialization failed:', error);
      addNotification({
        category: 'system',
        priority: 'high',
        title: 'Matching Error',
        body: 'Failed to load property matches. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const startRealTimeUpdates = async () => {
    try {
      await realTimeMatchingService.startRealTimeUpdates({
        enabled: true,
        checkInterval: 30,
        notifyOnNewMatches: true,
        notifyOnPriceChanges: true,
        notifyOnStatusChanges: true,
        maxNotificationsPerDay: 10,
        quietHours: {
          start: '22:00',
          end: '08:00'
        }
      });

      // Update status
      const status = realTimeMatchingService.getStatus();
      setRealTimeStatus(status);

    } catch (error) {
      console.error('Failed to start real-time updates:', error);
    }
  };

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await initializeEnhancedMatching();
    
    // Update real-time status
    const status = realTimeMatchingService.getStatus();
    setRealTimeStatus(status);
    
    setRefreshing(false);
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleMatchInteraction = async (
    match: EnhancedPropertyMatch,
    type: 'like' | 'dislike' | 'skip' | 'view' | 'save' | 'apply'
  ) => {
    try {
      // Record interaction for learning
      await enhancedAIMatchingService.recordInteraction(
        user?.email || 'default',
        match.id,
        { type }
      );

      // Update match state
      const updatedMatches = enhancedMatches.map(m => {
        if (m.id === match.id) {
          return {
            ...m,
            viewed: type === 'view' ? true : m.viewed,
            saved: type === 'save' ? !m.saved : m.saved,
            applied: type === 'apply' ? true : m.applied,
          };
        }
        return m;
      });
      
      setEnhancedMatches(updatedMatches);

      // Provide haptic feedback
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    } catch (error) {
      console.error('Failed to record interaction:', error);
    }
  };

  const handleViewDetails = (match: EnhancedPropertyMatch) => {
    setSelectedMatch(match);
    handleMatchInteraction(match, 'view');
  };

  const handleQuickApply = (match: EnhancedPropertyMatch) => {
    Alert.alert(
      'Quick Apply',
      `Apply for ${match.listing.title}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Apply',
          onPress: () => {
            handleMatchInteraction(match, 'apply');
            // Navigate to application screen
            router.push('/application');
          }
        }
      ]
    );
  };

  const handleSaveMatch = (match: EnhancedPropertyMatch) => {
    handleMatchInteraction(match, 'save');
  };

  const handleUpdatePreferences = () => {
    setShowNoMatchesModal(false);
    onUpdatePreferences?.();
  };

  const renderMatchCard = (match: EnhancedPropertyMatch, index: number) => (
    <Animated.View
      key={match.id}
      entering={SlideInRight.delay(index * 100)}
      exiting={SlideOutLeft}
      style={styles.matchCardContainer}
    >
      <PropertyMatchCard
        match={match}
        onViewDetails={() => handleViewDetails(match)}
        onQuickApply={() => handleQuickApply(match)}
        onSave={() => handleSaveMatch(match)}
        onInteraction={(type) => handleMatchInteraction(match, type)}
      />
    </Animated.View>
  );

  const renderMarketInsight = (insight: MarketInsight, index: number) => (
    <Animated.View
      key={`insight-${index}`}
      entering={FadeIn.delay(index * 150)}
      style={[
        styles.insightCard,
        {
          borderLeftColor: 
            insight.impact === 'positive' ? THEME.success :
            insight.impact === 'negative' ? THEME.error : THEME.warning
        }
      ]}
    >
      <View style={styles.insightHeader}>
        <Ionicons
          name={
            insight.type === 'price_trend' ? 'trending-up' :
            insight.type === 'availability' ? 'home' :
            insight.type === 'competition' ? 'people' : 'bulb'
          }
          size={20}
          color={
            insight.impact === 'positive' ? THEME.success :
            insight.impact === 'negative' ? THEME.error : THEME.warning
          }
        />
        <Text style={styles.insightTitle}>{insight.title}</Text>
      </View>
      <Text style={styles.insightDescription}>{insight.description}</Text>
      {insight.actionable && insight.action && (
        <TouchableOpacity
          style={styles.insightAction}
          onPress={() => {
            if (insight.action === 'Adjust preferences') {
              handleUpdatePreferences();
            }
          }}
        >
          <Text style={styles.insightActionText}>{insight.action}</Text>
          <Ionicons name="arrow-forward" size={16} color={THEME.primary} />
        </TouchableOpacity>
      )}
    </Animated.View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <View>
          <Text style={styles.greeting}>
            Good {new Date().getHours() < 12 ? 'morning' : new Date().getHours() < 18 ? 'afternoon' : 'evening'}
          </Text>
          <Text style={styles.userName}>{user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : user?.email}</Text>
        </View>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={onUpdatePreferences}
        >
          <Ionicons name="settings-outline" size={24} color={THEME.gray} />
        </TouchableOpacity>
      </View>

      {/* Real-time Status */}
      <View style={styles.statusContainer}>
        <View style={[
          styles.statusIndicator,
          { backgroundColor: realTimeStatus.isRunning ? THEME.success : THEME.gray }
        ]} />
        <Text style={styles.statusText}>
          {realTimeStatus.isRunning ? 'Live updates active' : 'Updates paused'}
        </Text>
        {realTimeStatus.lastUpdate && (
          <Text style={styles.lastUpdateText}>
            Last check: {realTimeStatus.lastUpdate.toLocaleTimeString()}
          </Text>
        )}
      </View>
    </View>
  );

  const renderStats = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statCard}>
        <Text style={styles.statNumber}>{enhancedMatches.length}</Text>
        <Text style={styles.statLabel}>Active Matches</Text>
      </View>
      <View style={styles.statCard}>
        <Text style={styles.statNumber}>
          {enhancedMatches.filter(m => m.matchPercentage >= 80).length}
        </Text>
        <Text style={styles.statLabel}>High Matches</Text>
      </View>
      <View style={styles.statCard}>
        <Text style={styles.statNumber}>
          {enhancedMatches.filter(m => m.saved).length}
        </Text>
        <Text style={styles.statLabel}>Saved</Text>
      </View>
      <View style={styles.statCard}>
        <Text style={styles.statNumber}>{realTimeStatus.notificationsSentToday}</Text>
        <Text style={styles.statLabel}>Notifications</Text>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={THEME.primary} />
        <Text style={styles.loadingText}>Finding your perfect matches...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[THEME.primary]}
            tintColor={THEME.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderHeader()}
        {renderStats()}

        {/* Autonomous Status */}
        <View style={styles.section}>
          <AutonomousStatusIndicator 
            showControls={true}
            onPress={() => router.push('/autonomous-status')}
          />
        </View>

        {/* Market Insights */}
        {marketInsights.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Market Insights</Text>
            {marketInsights.map(renderMarketInsight)}
          </View>
        )}

        {/* Property Matches */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              Your Matches ({enhancedMatches.length})
            </Text>
            {enhancedMatches.length > 0 && (
              <TouchableOpacity onPress={handleRefresh}>
                <Ionicons name="refresh" size={20} color={THEME.primary} />
              </TouchableOpacity>
            )}
          </View>

          {enhancedMatches.length > 0 ? (
            enhancedMatches.map(renderMatchCard)
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="search" size={48} color={THEME.gray} />
              <Text style={styles.emptyStateTitle}>No matches found</Text>
              <Text style={styles.emptyStateText}>
                We couldn&apos;t find properties matching your current preferences.
              </Text>
              <TouchableOpacity
                style={styles.adjustPreferencesButton}
                onPress={handleUpdatePreferences}
              >
                <Text style={styles.adjustPreferencesText}>Adjust Preferences</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Property Details Modal */}
      <Modal
        visible={selectedMatch !== null}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        {selectedMatch && (
          <PropertyMatchDetails
            match={selectedMatch}
            onApply={() => handleQuickApply(selectedMatch)}
            onSave={() => handleSaveMatch(selectedMatch)}
            onClose={() => setSelectedMatch(null)}
          />
        )}
      </Modal>

      {/* No Matches Modal */}
      <Modal
        visible={showNoMatchesModal}
        transparent
        animationType="fade"
      >
        <View style={styles.modalOverlay}>
          <Animated.View
            entering={FadeIn}
            exiting={FadeOut}
            style={styles.noMatchesModal}
          >
            <Ionicons name="search" size={48} color={THEME.gray} />
            <Text style={styles.modalTitle}>No Perfect Matches Yet</Text>
            <Text style={styles.modalText}>
              We&apos;re continuously searching for properties that match your preferences.
              Consider adjusting your criteria to see more options.
            </Text>
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalButtonSecondary}
                onPress={() => setShowNoMatchesModal(false)}
              >
                <Text style={styles.modalButtonSecondaryText}>Keep Searching</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalButtonPrimary}
                onPress={handleUpdatePreferences}
              >
                <Text style={styles.modalButtonPrimaryText}>Adjust Preferences</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
  },
  header: {
    padding: 20,
    backgroundColor: THEME.light,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  greeting: {
    fontSize: 16,
    color: THEME.gray,
  },
  userName: {
    fontSize: 20,
    fontWeight: '600',
    color: THEME.dark,
    marginTop: 4,
  },
  settingsButton: {
    padding: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: THEME.gray,
    marginRight: 12,
  },
  lastUpdateText: {
    fontSize: 12,
    color: THEME.gray,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: THEME.light,
  },
  statCard: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: THEME.primary,
  },
  statLabel: {
    fontSize: 12,
    color: THEME.gray,
    marginTop: 4,
  },
  section: {
    padding: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
  },
  matchCardContainer: {
    marginBottom: 16,
  },
  insightCard: {
    backgroundColor: THEME.light,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.dark,
    marginLeft: 8,
  },
  insightDescription: {
    fontSize: 14,
    color: THEME.gray,
    lineHeight: 20,
  },
  insightAction: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  insightActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.primary,
    marginRight: 4,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
    marginTop: 16,
  },
  emptyStateText: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 20,
  },
  adjustPreferencesButton: {
    backgroundColor: THEME.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  adjustPreferencesText: {
    color: THEME.light,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  noMatchesModal: {
    backgroundColor: THEME.light,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    maxWidth: width - 40,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: THEME.dark,
    marginTop: 16,
    textAlign: 'center',
  },
  modalText: {
    fontSize: 14,
    color: THEME.gray,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    marginTop: 24,
    gap: 12,
  },
  modalButtonSecondary: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: THEME.gray,
    alignItems: 'center',
  },
  modalButtonSecondaryText: {
    color: THEME.gray,
    fontWeight: '500',
  },
  modalButtonPrimary: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: THEME.primary,
    alignItems: 'center',
  },
  modalButtonPrimaryText: {
    color: THEME.light,
    fontWeight: '600',
  },
});
