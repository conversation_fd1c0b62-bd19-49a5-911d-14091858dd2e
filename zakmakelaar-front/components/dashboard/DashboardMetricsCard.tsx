import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
  FadeIn,
} from 'react-native-reanimated';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
};

interface DashboardMetricsCardProps {
  matches: number;
  saved: number;
  viewed: number;
  applications: number;
  pending: number;
  onViewApplications?: () => void;
}

export const DashboardMetricsCard: React.FC<DashboardMetricsCardProps> = ({
  matches,
  saved,
  viewed,
  applications,
  pending,
  onViewApplications,
}) => {
  // Calculate progress percentages
  const viewedPercentage = matches > 0 ? Math.round((viewed / matches) * 100) : 0;
  const savedPercentage = matches > 0 ? Math.round((saved / matches) * 100) : 0;
  const applicationPercentage = matches > 0 ? Math.round((applications / matches) * 100) : 0;
  
  return (
    <Animated.View 
      style={styles.container}
      entering={FadeIn.duration(600).delay(300)}
    >
      <View style={styles.header}>
        <Text style={styles.title}>Your Progress</Text>
        {onViewApplications && (
          <TouchableOpacity 
            style={styles.viewButton}
            onPress={onViewApplications}
          >
            <Text style={styles.viewButtonText}>View Applications</Text>
            <Ionicons name="chevron-forward" size={16} color={THEME.primary} />
          </TouchableOpacity>
        )}
      </View>
      
      <View style={styles.metricsContainer}>
        {/* Matches Metric */}
        <View style={styles.metricItem}>
          <View style={styles.metricIconContainer}>
            <Ionicons name="home" size={20} color={THEME.accent} />
          </View>
          <View style={styles.metricContent}>
            <Text style={styles.metricValue}>{matches}</Text>
            <Text style={styles.metricLabel}>Matches</Text>
          </View>
        </View>
        
        {/* Viewed Metric */}
        <View style={styles.metricItem}>
          <View style={styles.metricIconContainer}>
            <Ionicons name="eye" size={20} color={THEME.primary} />
          </View>
          <View style={styles.metricContent}>
            <Text style={styles.metricValue}>{viewed}</Text>
            <Text style={styles.metricLabel}>Viewed</Text>
          </View>
        </View>
        
        {/* Saved Metric */}
        <View style={styles.metricItem}>
          <View style={styles.metricIconContainer}>
            <Ionicons name="heart" size={20} color="#ef4444" />
          </View>
          <View style={styles.metricContent}>
            <Text style={styles.metricValue}>{saved}</Text>
            <Text style={styles.metricLabel}>Saved</Text>
          </View>
        </View>
        
        {/* Applications Metric */}
        <View style={styles.metricItem}>
          <View style={styles.metricIconContainer}>
            <Ionicons name="paper-plane" size={20} color={THEME.secondary} />
          </View>
          <View style={styles.metricContent}>
            <Text style={styles.metricValue}>{applications}</Text>
            <Text style={styles.metricLabel}>Applications</Text>
          </View>
        </View>
      </View>
      
      {/* Progress Bars */}
      <View style={styles.progressContainer}>
        {/* Viewed Progress */}
        <View style={styles.progressItem}>
          <View style={styles.progressLabelContainer}>
            <Text style={styles.progressLabel}>Viewed</Text>
            <Text style={styles.progressPercentage}>{viewedPercentage}%</Text>
          </View>
          <View style={styles.progressBarContainer}>
            <View 
              style={[
                styles.progressBar, 
                { width: `${viewedPercentage}%`, backgroundColor: THEME.primary }
              ]} 
            />
          </View>
        </View>
        
        {/* Saved Progress */}
        <View style={styles.progressItem}>
          <View style={styles.progressLabelContainer}>
            <Text style={styles.progressLabel}>Saved</Text>
            <Text style={styles.progressPercentage}>{savedPercentage}%</Text>
          </View>
          <View style={styles.progressBarContainer}>
            <View 
              style={[
                styles.progressBar, 
                { width: `${savedPercentage}%`, backgroundColor: '#ef4444' }
              ]} 
            />
          </View>
        </View>
        
        {/* Applications Progress */}
        <View style={styles.progressItem}>
          <View style={styles.progressLabelContainer}>
            <Text style={styles.progressLabel}>Applied</Text>
            <Text style={styles.progressPercentage}>{applicationPercentage}%</Text>
          </View>
          <View style={styles.progressBarContainer}>
            <View 
              style={[
                styles.progressBar, 
                { width: `${applicationPercentage}%`, backgroundColor: THEME.secondary }
              ]} 
            />
          </View>
        </View>
      </View>
      
      {/* Pending Applications */}
      {pending > 0 && (
        <View style={styles.pendingContainer}>
          <Ionicons name="time-outline" size={20} color="#f59e0b" />
          <Text style={styles.pendingText}>
            {pending} pending application{pending !== 1 ? 's' : ''}
          </Text>
        </View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
  },
  viewButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewButtonText: {
    fontSize: 14,
    color: THEME.primary,
    fontWeight: '500',
    marginRight: 4,
  },
  metricsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricItem: {
    alignItems: 'center',
    flex: 1,
  },
  metricIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricContent: {
    alignItems: 'center',
  },
  metricValue: {
    fontSize: 18,
    fontWeight: '700',
    color: THEME.dark,
  },
  metricLabel: {
    fontSize: 12,
    color: THEME.gray,
  },
  progressContainer: {
    marginTop: 8,
  },
  progressItem: {
    marginBottom: 12,
  },
  progressLabelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  progressLabel: {
    fontSize: 14,
    color: THEME.dark,
  },
  progressPercentage: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.gray,
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: '#f3f4f6',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
  pendingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(245, 158, 11, 0.1)',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  pendingText: {
    fontSize: 14,
    color: '#f59e0b',
    fontWeight: '500',
    marginLeft: 8,
  },
});