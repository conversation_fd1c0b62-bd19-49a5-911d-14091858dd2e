import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet, Alert } from "react-native";
import { apiService } from "../services/api";
import { authService } from "../services/authService";

export default function ConnectionTest() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (result: string) => {
    setTestResults((prev) => [...prev, result]);
  };

  const runTests = async () => {
    setIsLoading(true);
    setTestResults([]);

    try {
      // Test 1: Health Check
      addResult("🔍 Testing API health...");
      try {
        await apiService.healthCheck();
        addResult("✅ Health check passed");
      } catch (error: any) {
        addResult(`❌ Health check failed: ${error.message}`);
      }

      // Test 2: Register a test user
      addResult("🔍 Testing user registration...");
      const testEmail = `test${Date.now()}@example.com`;
      const testPassword = "TestPass123";

      try {
        const registerResult = await authService.register({
          email: testEmail,
          password: testPassword,
          firstName: "Test",
          lastName: "User",
        });

        if (registerResult.success) {
          addResult("✅ Registration successful");

          // Test 3: Logout and Login
          addResult("🔍 Testing logout...");
          await authService.logout();
          addResult("✅ Logout successful");

          addResult("🔍 Testing login...");
          const loginResult = await authService.login({
            email: testEmail,
            password: testPassword,
          });

          if (loginResult.success) {
            addResult("✅ Login successful");

            // Test 4: Get current user
            addResult("🔍 Testing get current user...");
            const userResult = await authService.getCurrentUser();
            if (userResult.success) {
              addResult("✅ Get current user successful");
            } else {
              addResult(
                `❌ Get current user failed: ${
                  userResult.message || "Unknown error"
                }`
              );
            }
          } else {
            addResult(
              `❌ Login failed: ${loginResult.message || "Unknown error"}`
            );
          }
        } else {
          addResult(
            `❌ Registration failed: ${
              registerResult.message || registerResult.error || "Unknown error"
            }`
          );
          addResult(
            `📋 Registration response: ${JSON.stringify(registerResult)}`
          );
        }
      } catch (error: any) {
        addResult(`❌ Auth test failed: ${error.message || "Unknown error"}`);
        addResult(`📋 Error details: ${JSON.stringify(error)}`);
        console.error("Auth test error:", error);
      }

      addResult("🎉 Connection tests completed!");
    } catch (error: any) {
      addResult(`❌ Test suite failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Frontend-Backend Connection Test</Text>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.testButton]}
          onPress={runTests}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? "Running Tests..." : "Run Connection Tests"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.clearButton]}
          onPress={clearResults}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.resultsContainer}>
        <Text style={styles.resultsTitle}>Test Results:</Text>
        {testResults.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
        {testResults.length === 0 && (
          <Text style={styles.noResultsText}>
            No tests run yet. Tap "Run Connection Tests" to start.
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: "#f3f4f6",
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1f2937",
    marginBottom: 20,
    textAlign: "center",
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 10,
    marginBottom: 20,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  testButton: {
    backgroundColor: "#f72585",
  },
  clearButton: {
    backgroundColor: "#6b7280",
  },
  buttonText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "600",
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: "#ffffff",
    borderRadius: 8,
    padding: 16,
  },
  resultsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 12,
  },
  resultText: {
    fontSize: 14,
    color: "#374151",
    marginBottom: 4,
    fontFamily: "monospace",
  },
  noResultsText: {
    fontSize: 14,
    color: "#6b7280",
    fontStyle: "italic",
    textAlign: "center",
    marginTop: 20,
  },
});
