/**
 * LocationSelector Component
 * A dropdown/modal selector for choosing multiple locations from available cities
 */

import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Modal,
  ScrollView,
  ActivityIndicator,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, { FadeInUp, FadeInDown } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

import { listingsService } from '../services/listingsService';

// Theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

interface LocationSelectorProps {
  selectedLocations: string[];
  onSelectionChange: (locations: string[]) => void;
  placeholder?: string;
  maxSelections?: number;
  style?: any;
  displayMode?: 'chips' | 'text'; // New prop to control display mode
}

export const LocationSelector: React.FC<LocationSelectorProps> = ({
  selectedLocations,
  onSelectionChange,
  placeholder = "Select locations...",
  maxSelections = 10,
  style,
  displayMode = 'chips',
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [availableCities, setAvailableCities] = useState<string[]>([]);
  const [filteredCities, setFilteredCities] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Load available cities when component mounts
  useEffect(() => {
    loadAvailableCities();
  }, []);

  // Filter cities based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCities(availableCities);
    } else {
      const filtered = availableCities.filter(city =>
        city.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCities(filtered);
    }
  }, [searchQuery, availableCities]);

  const loadAvailableCities = async () => {
    try {
      setLoading(true);
      const response = await listingsService.getAvailableCities();
      
      if (response.success && response.data) {
        // Sort cities alphabetically and remove duplicates
        const uniqueCities = [...new Set(response.data)]
          .filter(city => city && city.trim() !== '')
          .sort((a, b) => a.localeCompare(b));
        
        setAvailableCities(uniqueCities);
        setFilteredCities(uniqueCities);
      } else {
        throw new Error(response.message || 'Failed to load cities');
      }
    } catch (error) {
      console.error('Error loading cities:', error);
      Alert.alert('Error', 'Failed to load available cities');
      
      // Fallback to common Dutch cities
      const fallbackCities = [
        'Amsterdam', 'Rotterdam', 'Den Haag', 'Utrecht', 'Eindhoven',
        'Tilburg', 'Groningen', 'Almere', 'Breda', 'Nijmegen',
        'Enschede', 'Haarlem', 'Arnhem', 'Zaanstad', 'Amersfoort'
      ];
      setAvailableCities(fallbackCities);
      setFilteredCities(fallbackCities);
    } finally {
      setLoading(false);
    }
  };

  const handleLocationToggle = (city: string) => {
    const isSelected = selectedLocations.includes(city);
    
    if (isSelected) {
      // Remove from selection
      const newSelection = selectedLocations.filter(loc => loc !== city);
      onSelectionChange(newSelection);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } else {
      // Add to selection (if under limit)
      if (selectedLocations.length >= maxSelections) {
        Alert.alert(
          'Selection Limit',
          `You can select up to ${maxSelections} locations.`,
          [{ text: 'OK', style: 'default' }]
        );
        return;
      }
      
      const newSelection = [...selectedLocations, city];
      onSelectionChange(newSelection);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleSelectAll = () => {
    if (filteredCities.length === 0) return;
    
    const citiesToAdd = filteredCities.filter(city => !selectedLocations.includes(city));
    const totalAfterAdd = selectedLocations.length + citiesToAdd.length;
    
    if (totalAfterAdd > maxSelections) {
      Alert.alert(
        'Selection Limit',
        `Adding all filtered cities would exceed the limit of ${maxSelections} locations.`,
        [{ text: 'OK', style: 'default' }]
      );
      return;
    }
    
    const newSelection = [...selectedLocations, ...citiesToAdd];
    onSelectionChange(newSelection);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  };

  const handleClearAll = () => {
    onSelectionChange([]);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
  };

  const handleRemoveLocation = (locationToRemove: string) => {
    const newSelection = selectedLocations.filter(loc => loc !== locationToRemove);
    onSelectionChange(newSelection);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getDisplayText = () => {
    if (selectedLocations.length === 0) {
      return placeholder;
    } else if (selectedLocations.length === 1) {
      return selectedLocations[0];
    } else if (selectedLocations.length <= 3) {
      return selectedLocations.join(', ');
    } else {
      return `${selectedLocations.slice(0, 2).join(', ')} +${selectedLocations.length - 2} more`;
    }
  };

  return (
    <>
      {/* Selector Display */}
      {displayMode === 'chips' ? (
        // Chip-based Selector
        <View style={[styles.chipContainer, style]}>
          {selectedLocations.length === 0 ? (
            // Empty state - show add button
            <TouchableOpacity
              style={styles.addButton}
              onPress={() => {
                setIsModalVisible(true);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
              activeOpacity={0.8}
            >
              <Ionicons name="add" size={16} color={THEME.primary} />
              <Text style={styles.addButtonText}>{placeholder}</Text>
            </TouchableOpacity>
          ) : (
            // Show selected locations as chips
            <View style={styles.chipsWrapper}>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                style={styles.chipsScrollView}
                contentContainerStyle={styles.chipsContent}
              >
                {selectedLocations.map((location, index) => (
                  <View key={location} style={styles.chip}>
                    <Text style={styles.chipText} numberOfLines={1}>
                      {location}
                    </Text>
                    <TouchableOpacity
                      style={styles.chipRemoveButton}
                      onPress={() => handleRemoveLocation(location)}
                      hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
                    >
                      <Ionicons name="close" size={14} color={THEME.primary} />
                    </TouchableOpacity>
                  </View>
                ))}
                
                {/* Add more button */}
                <TouchableOpacity
                  style={styles.addMoreButton}
                  onPress={() => {
                    setIsModalVisible(true);
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  }}
                  activeOpacity={0.8}
                >
                  <Ionicons name="add" size={14} color={THEME.primary} />
                  <Text style={styles.addMoreText}>Add</Text>
                </TouchableOpacity>
              </ScrollView>
              
              {/* Selection count badge */}
              <View style={styles.countBadge}>
                <Text style={styles.countBadgeText}>
                  {selectedLocations.length}
                </Text>
              </View>
            </View>
          )}
        </View>
      ) : (
        // Original Text-based Selector
        <TouchableOpacity
          style={[styles.selectorButton, style]}
          onPress={() => {
            setIsModalVisible(true);
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }}
          activeOpacity={0.8}
        >
          <Text 
            style={[
              styles.selectorText,
              selectedLocations.length === 0 && styles.placeholderText
            ]}
            numberOfLines={2}
          >
            {getDisplayText()}
          </Text>
          <View style={styles.selectorIcon}>
            <Ionicons 
              name="chevron-down" 
              size={16} 
              color={THEME.gray} 
            />
            {selectedLocations.length > 0 && (
              <View style={styles.selectionBadge}>
                <Text style={styles.selectionBadgeText}>
                  {selectedLocations.length}
                </Text>
              </View>
            )}
          </View>
        </TouchableOpacity>
      )}

      {/* Selection Modal */}
      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setIsModalVisible(false)}
            >
              <Ionicons name="close" size={24} color={THEME.gray} />
            </TouchableOpacity>
            
            <Text style={styles.modalTitle}>Select Locations</Text>
            
            <TouchableOpacity
              style={styles.modalDoneButton}
              onPress={() => setIsModalVisible(false)}
            >
              <Text style={styles.modalDoneText}>Done</Text>
            </TouchableOpacity>
          </View>

          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <Ionicons name="search" size={20} color={THEME.gray} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search cities..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoCapitalize="words"
              autoCorrect={false}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity
                onPress={() => setSearchQuery('')}
                style={styles.searchClearButton}
              >
                <Ionicons name="close-circle" size={20} color={THEME.gray} />
              </TouchableOpacity>
            )}
          </View>

          {/* Selection Summary */}
          <View style={styles.selectionSummary}>
            <Text style={styles.selectionSummaryText}>
              {selectedLocations.length} of {maxSelections} locations selected
            </Text>
            <View style={styles.selectionActions}>
              <TouchableOpacity
                style={styles.selectionActionButton}
                onPress={handleSelectAll}
                disabled={filteredCities.length === 0}
              >
                <Text style={[
                  styles.selectionActionText,
                  filteredCities.length === 0 && styles.disabledText
                ]}>
                  Select All
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.selectionActionButton}
                onPress={handleClearAll}
                disabled={selectedLocations.length === 0}
              >
                <Text style={[
                  styles.selectionActionText,
                  selectedLocations.length === 0 && styles.disabledText
                ]}>
                  Clear All
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Cities List */}
          {loading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={THEME.primary} />
              <Text style={styles.loadingText}>Loading cities...</Text>
            </View>
          ) : (
            <ScrollView 
              style={styles.citiesList}
              showsVerticalScrollIndicator={false}
            >
              {filteredCities.length === 0 ? (
                <View style={styles.emptyState}>
                  <Ionicons name="location-outline" size={48} color={THEME.gray} />
                  <Text style={styles.emptyStateText}>
                    {searchQuery ? 'No cities found' : 'No cities available'}
                  </Text>
                  {searchQuery && (
                    <Text style={styles.emptyStateSubtext}>
                      Try adjusting your search terms
                    </Text>
                  )}
                </View>
              ) : (
                filteredCities.map((city, index) => {
                  const isSelected = selectedLocations.includes(city);
                  
                  return (
                    <Animated.View
                      key={city}
                      entering={FadeInDown.duration(300).delay(index * 50)}
                    >
                      <TouchableOpacity
                        style={[
                          styles.cityItem,
                          isSelected && styles.cityItemSelected
                        ]}
                        onPress={() => handleLocationToggle(city)}
                        activeOpacity={0.8}
                      >
                        <View style={styles.cityItemContent}>
                          <Text style={[
                            styles.cityItemText,
                            isSelected && styles.cityItemTextSelected
                          ]}>
                            {city}
                          </Text>
                          {isSelected && (
                            <Ionicons 
                              name="checkmark-circle" 
                              size={20} 
                              color={THEME.primary} 
                            />
                          )}
                        </View>
                      </TouchableOpacity>
                    </Animated.View>
                  );
                })
              )}
            </ScrollView>
          )}
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  chipContainer: {
    backgroundColor: THEME.lightGray,
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 8,
    minHeight: 48,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
    paddingVertical: 8,
    minHeight: 32,
  },
  addButtonText: {
    fontSize: 14,
    color: THEME.gray,
    marginLeft: 6,
  },
  chipsWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  chipsScrollView: {
    flex: 1,
  },
  chipsContent: {
    alignItems: 'center',
    paddingRight: 8,
  },
  chip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    paddingLeft: 12,
    paddingRight: 4,
    paddingVertical: 6,
    marginRight: 6,
    marginVertical: 2,
    borderWidth: 1,
    borderColor: THEME.primary,
    maxWidth: 120,
  },
  chipText: {
    fontSize: 13,
    color: THEME.primary,
    fontWeight: '500',
    marginRight: 4,
    flex: 1,
  },
  chipRemoveButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  addMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    borderRadius: 14,
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderWidth: 1,
    borderColor: THEME.primary,
    borderStyle: 'dashed',
  },
  addMoreText: {
    fontSize: 12,
    color: THEME.primary,
    fontWeight: '500',
    marginLeft: 4,
  },
  countBadge: {
    backgroundColor: THEME.primary,
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  countBadgeText: {
    fontSize: 11,
    fontWeight: '600',
    color: '#ffffff',
  },
  // Original text-based selector styles
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: THEME.lightGray,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    minHeight: 48,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  selectorText: {
    flex: 1,
    fontSize: 14,
    color: THEME.dark,
    lineHeight: 18,
  },
  placeholderText: {
    color: THEME.gray,
  },
  selectorIcon: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
  },
  selectionBadge: {
    backgroundColor: THEME.primary,
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 6,
  },
  selectionBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: THEME.lightGray,
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
  },
  modalDoneButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  modalDoneText: {
    fontSize: 16,
    fontWeight: '600',
    color: THEME.primary,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.lightGray,
    borderRadius: 10,
    paddingHorizontal: 12,
    paddingVertical: 8,
    margin: 20,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: THEME.dark,
    marginLeft: 8,
    paddingVertical: 4,
  },
  searchClearButton: {
    padding: 4,
  },
  selectionSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  selectionSummaryText: {
    fontSize: 14,
    color: THEME.gray,
  },
  selectionActions: {
    flexDirection: 'row',
    gap: 16,
  },
  selectionActionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  selectionActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: THEME.primary,
  },
  disabledText: {
    color: THEME.gray,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
  },
  citiesList: {
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '500',
    color: THEME.gray,
    marginTop: 16,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: THEME.gray,
    marginTop: 8,
    textAlign: 'center',
  },
  cityItem: {
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: THEME.lightGray,
  },
  cityItemSelected: {
    backgroundColor: '#f0f9ff',
  },
  cityItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  cityItemText: {
    fontSize: 16,
    color: THEME.dark,
  },
  cityItemTextSelected: {
    fontWeight: '500',
    color: THEME.primary,
  },
});

export default LocationSelector;