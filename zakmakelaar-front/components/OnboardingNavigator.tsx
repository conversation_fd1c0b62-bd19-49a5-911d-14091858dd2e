import React, { useEffect, useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { Ionicons } from '@expo/vector-icons';
import Animated, { 
  FadeIn, 
  FadeOut,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing
} from 'react-native-reanimated';
import { OnboardingProgress } from './OnboardingProgress';
import { ContextualHelp } from './ContextualHelp';

const { width } = Dimensions.get('window');

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff'
};

export interface OnboardingStep {
  id: string;
  title: string;
  route: string;
  helpItems: {
    title: string;
    content: string;
    icon?: string;
  }[];
}

interface OnboardingNavigatorProps {
  steps: OnboardingStep[];
  currentStepId: string;
  allowSkip?: boolean;
  onComplete?: () => void;
  hideCompleteButton?: boolean;
}

export const OnboardingNavigator: React.FC<OnboardingNavigatorProps> = ({
  steps,
  currentStepId,
  allowSkip = false,
  onComplete,
  hideCompleteButton = false
}) => {
  const router = useRouter();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  
  // Animation values
  const progressOpacity = useSharedValue(0);
  const buttonScale = useSharedValue(1);
  
  // Find current step index
  useEffect(() => {
    const index = steps.findIndex(step => step.id === currentStepId);
    if (index !== -1) {
      setCurrentStepIndex(index);
    }
  }, [currentStepId, steps]);
  
  // Start animations
  useEffect(() => {
    progressOpacity.value = withTiming(1, { 
      duration: 600, 
      easing: Easing.out(Easing.ease) 
    });
    
    // Button pulse animation
    buttonScale.value = withTiming(1, { duration: 300 });
  }, [currentStepIndex]);
  
  // Animated styles
  const progressAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: progressOpacity.value
    };
  });
  
  const buttonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: buttonScale.value }]
    };
  });
  
  // Navigation handlers
  const handleNext = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    if (currentStepIndex < steps.length - 1) {
      // Navigate to next step
      const nextStep = steps[currentStepIndex + 1];
      router.push(nextStep.route);
    } else {
      // Complete onboarding
      if (onComplete) {
        onComplete();
      }
    }
  };
  
  const handleBack = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    if (currentStepIndex > 0) {
      // Navigate to previous step
      const prevStep = steps[currentStepIndex - 1];
      router.push(prevStep.route);
    } else {
      // Go back to welcome screen
      router.replace('/');
    }
  };
  
  const handleSkip = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    if (onComplete) {
      onComplete();
    } else {
      // Navigate to final step
      const finalStep = steps[steps.length - 1];
      router.push(finalStep.route);
    }
  };
  
  // Get current step
  const currentStep = steps[currentStepIndex];
  const isLastStep = currentStepIndex === steps.length - 1;
  
  // Get step titles for progress component
  const stepTitles = steps.map(step => step.title);
  
  return (
    <View style={styles.container}>
      {/* Top navigation bar */}
      <View style={styles.topBar}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={handleBack}
        >
          <Ionicons name="arrow-back" size={24} color="rgba(255, 255, 255, 0.9)" />
        </TouchableOpacity>
        
        <View style={styles.topBarRight}>
          {allowSkip && !isLastStep && (
            <TouchableOpacity 
              style={styles.skipButton}
              onPress={handleSkip}
            >
              <Text style={styles.skipText}>Skip</Text>
            </TouchableOpacity>
          )}
          
          <ContextualHelp 
            items={currentStep.helpItems}
            screenName={currentStep.title}
          />
        </View>
      </View>
      
      {/* Progress indicator */}
      <Animated.View 
        style={[styles.progressContainer, progressAnimatedStyle]}
      >
        <OnboardingProgress 
          currentStep={currentStepIndex + 1}
          totalSteps={steps.length}
          stepTitles={stepTitles}
        />
      </Animated.View>
      
      {/* Bottom navigation */}
      <View style={styles.bottomBar}>
        {/* Hide the Complete button on the last step if hideCompleteButton is true */}
        {!(isLastStep && hideCompleteButton) && (
          <Animated.View style={buttonAnimatedStyle}>
            <TouchableOpacity 
              style={[
                styles.nextButton,
                isLastStep ? styles.completeButton : null
              ]}
              onPress={handleNext}
              activeOpacity={0.8}
            >
              <Text style={styles.nextButtonText}>
                {isLastStep ? 'Complete' : 'Next'}
              </Text>
              <Ionicons 
                name={isLastStep ? "checkmark" : "arrow-forward"} 
                size={20} 
                color={THEME.light} 
                style={styles.nextButtonIcon}
              />
            </TouchableOpacity>
          </Animated.View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  topBarRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  skipButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginRight: 12,
  },
  skipText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontWeight: '500',
  },
  progressContainer: {
    marginBottom: 16,
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 24,
  },
  nextButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: THEME.accent,
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    minWidth: width * 0.5,
  },
  completeButton: {
    backgroundColor: THEME.primary,
  },
  nextButtonText: {
    color: THEME.light,
    fontSize: 16,
    fontWeight: '600',
  },
  nextButtonIcon: {
    marginLeft: 8,
  },
});