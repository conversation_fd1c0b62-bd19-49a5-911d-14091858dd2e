import React, { useEffect } from 'react';
import { StyleSheet, View, Dimensions } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withRepeat, 
  withTiming, 
  Easing,
  interpolate,
  withDelay,
  withSequence,
  FadeIn
} from 'react-native-reanimated';
import { LinearGradient } from 'expo-linear-gradient';
import { FuturisticGrid } from './FuturisticGrid';

const { width, height } = Dimensions.get('window');

interface WelcomeBackgroundProps {
  primaryColor?: string;
  secondaryColor?: string;
  accentColor?: string;
}

export const WelcomeBackground: React.FC<WelcomeBackgroundProps> = ({
  primaryColor = '#4361ee',
  secondaryColor = '#7209b7',
  accentColor = '#f72585'
}) => {
  // Animation values
  const rotation1 = useSharedValue(0);
  const rotation2 = useSharedValue(0);
  const rotation3 = useSharedValue(0);
  const rotation4 = useSharedValue(0);
  const scale1 = useSharedValue(1);
  const scale2 = useSharedValue(0.8);
  const scale3 = useSharedValue(0.6);
  const scale4 = useSharedValue(0.7);
  const translateX1 = useSharedValue(0);
  const translateY1 = useSharedValue(0);
  const translateX2 = useSharedValue(0);
  const translateY2 = useSharedValue(0);
  const translateX3 = useSharedValue(0);
  const translateY3 = useSharedValue(0);
  const translateX4 = useSharedValue(0);
  const translateY4 = useSharedValue(0);
  const opacity1 = useSharedValue(0.7);
  const opacity2 = useSharedValue(0.5);
  const opacity3 = useSharedValue(0.3);
  const opacity4 = useSharedValue(0.4);
  const gridOpacity = useSharedValue(0);
  const gridScale = useSharedValue(1.2);
  
  // Start animations
  useEffect(() => {
    // Grid animation
    gridOpacity.value = withTiming(1, { duration: 2000 });
    gridScale.value = withTiming(1, { duration: 2000, easing: Easing.out(Easing.exp) });
    
    // Rotation animations with different speeds
    rotation1.value = withRepeat(
      withTiming(360, { duration: 25000, easing: Easing.linear }), 
      -1, 
      false
    );
    
    rotation2.value = withRepeat(
      withTiming(-360, { duration: 30000, easing: Easing.linear }), 
      -1, 
      false
    );
    
    rotation3.value = withRepeat(
      withTiming(360, { duration: 35000, easing: Easing.linear }), 
      -1, 
      false
    );
    
    rotation4.value = withRepeat(
      withTiming(-360, { duration: 40000, easing: Easing.linear }), 
      -1, 
      false
    );
    
    // Scale animations with sequence for more dynamic effect
    scale1.value = withRepeat(
      withSequence(
        withTiming(1.3, { duration: 8000, easing: Easing.inOut(Easing.ease) }),
        withTiming(1.1, { duration: 8000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    scale2.value = withRepeat(
      withSequence(
        withTiming(1.1, { duration: 10000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.9, { duration: 10000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    scale3.value = withRepeat(
      withSequence(
        withTiming(0.8, { duration: 12000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.6, { duration: 12000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    scale4.value = withRepeat(
      withSequence(
        withTiming(0.9, { duration: 9000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.7, { duration: 9000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    // Translation animations for floating effect
    translateX1.value = withRepeat(
      withSequence(
        withTiming(width * 0.05, { duration: 15000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-width * 0.05, { duration: 15000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    translateY1.value = withRepeat(
      withSequence(
        withTiming(-height * 0.03, { duration: 18000, easing: Easing.inOut(Easing.ease) }),
        withTiming(height * 0.03, { duration: 18000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    translateX2.value = withRepeat(
      withSequence(
        withTiming(-width * 0.07, { duration: 20000, easing: Easing.inOut(Easing.ease) }),
        withTiming(width * 0.07, { duration: 20000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    translateY2.value = withRepeat(
      withSequence(
        withTiming(height * 0.04, { duration: 17000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-height * 0.04, { duration: 17000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    translateX3.value = withRepeat(
      withSequence(
        withTiming(width * 0.06, { duration: 22000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-width * 0.06, { duration: 22000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    translateY3.value = withRepeat(
      withSequence(
        withTiming(-height * 0.05, { duration: 19000, easing: Easing.inOut(Easing.ease) }),
        withTiming(height * 0.05, { duration: 19000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    translateX4.value = withRepeat(
      withSequence(
        withTiming(-width * 0.04, { duration: 23000, easing: Easing.inOut(Easing.ease) }),
        withTiming(width * 0.04, { duration: 23000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    translateY4.value = withRepeat(
      withSequence(
        withTiming(height * 0.06, { duration: 21000, easing: Easing.inOut(Easing.ease) }),
        withTiming(-height * 0.06, { duration: 21000, easing: Easing.inOut(Easing.ease) })
      ),
      -1,
      true
    );
    
    // Opacity animations
    opacity1.value = withRepeat(
      withSequence(
        withTiming(0.6, { duration: 7000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.8, { duration: 7000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    opacity2.value = withRepeat(
      withSequence(
        withTiming(0.4, { duration: 9000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.6, { duration: 9000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    opacity3.value = withRepeat(
      withSequence(
        withTiming(0.2, { duration: 11000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.4, { duration: 11000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
    
    opacity4.value = withRepeat(
      withSequence(
        withTiming(0.3, { duration: 8000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.5, { duration: 8000, easing: Easing.inOut(Easing.ease) })
      ), 
      -1, 
      true
    );
  }, []);
  
  // Animated styles
  const animatedStyle1 = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX1.value },
        { translateY: translateY1.value },
        { rotate: `${rotation1.value}deg` },
        { scale: scale1.value }
      ],
      opacity: opacity1.value,
    };
  });
  
  const animatedStyle2 = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX2.value },
        { translateY: translateY2.value },
        { rotate: `${rotation2.value}deg` },
        { scale: scale2.value }
      ],
      opacity: opacity2.value,
    };
  });
  
  const animatedStyle3 = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX3.value },
        { translateY: translateY3.value },
        { rotate: `${rotation3.value}deg` },
        { scale: scale3.value }
      ],
      opacity: opacity3.value,
    };
  });
  
  const animatedStyle4 = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX4.value },
        { translateY: translateY4.value },
        { rotate: `${rotation4.value}deg` },
        { scale: scale4.value }
      ],
      opacity: opacity4.value,
    };
  });
  
  const gridAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: gridOpacity.value,
      transform: [{ scale: gridScale.value }]
    };
  });

  return (
    <View style={styles.container}>
      {/* Main gradient background */}
      <LinearGradient
        colors={['#0a0a18', '#1a1a2e']}
        style={styles.gradientBackground}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      />
      
      {/* Animated blobs with gradients */}
      <Animated.View style={[styles.blob1, animatedStyle1]}>
        <LinearGradient
          colors={[primaryColor, secondaryColor]}
          style={styles.blobGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>
      
      <Animated.View style={[styles.blob2, animatedStyle2]}>
        <LinearGradient
          colors={[secondaryColor, accentColor]}
          style={styles.blobGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>
      
      <Animated.View style={[styles.blob3, animatedStyle3]}>
        <LinearGradient
          colors={[accentColor, primaryColor]}
          style={styles.blobGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>
      
      <Animated.View style={[styles.blob4, animatedStyle4]}>
        <LinearGradient
          colors={[primaryColor, accentColor, secondaryColor]}
          style={styles.blobGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>
      
      {/* Particle effect */}
      <View style={styles.particleContainer}>
        {Array.from({ length: 20 }).map((_, index) => (
          <Animated.View 
            key={index}
            style={[
              styles.particle,
              {
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                width: Math.random() * 3 + 1,
                height: Math.random() * 3 + 1,
                opacity: Math.random() * 0.5 + 0.2,
              }
            ]}
            entering={FadeIn.delay(index * 100).duration(1000)}
          />
        ))}
      </View>
      
      {/* Grid overlay for futuristic effect */}
      <Animated.View style={gridAnimatedStyle}>
        <FuturisticGrid cellSize={25} lineColor="rgba(73, 80, 170, 0.15)" />
      </Animated.View>
      
      {/* Horizontal lines for tech effect */}
      <View style={styles.horizontalLinesContainer}>
        {Array.from({ length: 5 }).map((_, index) => (
          <Animated.View 
            key={index}
            style={[
              styles.horizontalLine,
              {
                top: height * (0.2 + index * 0.15),
                opacity: 0.1 + index * 0.02,
                height: 1 + index * 0.5
              }
            ]}
            entering={FadeIn.delay(1000 + index * 200).duration(1000)}
          />
        ))}
      </View>
      
      {/* Frosted glass effect overlay */}
      <View style={styles.overlay} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    overflow: 'hidden',
  },
  gradientBackground: {
    ...StyleSheet.absoluteFillObject,
  },
  blob1: {
    position: 'absolute',
    width: width * 1.5,
    height: width * 1.5,
    borderRadius: width * 0.75,
    top: -width * 0.5,
    left: -width * 0.25,
    overflow: 'hidden',
  },
  blob2: {
    position: 'absolute',
    width: width * 1.2,
    height: width * 1.2,
    borderRadius: width * 0.6,
    bottom: -width * 0.3,
    right: -width * 0.3,
    overflow: 'hidden',
  },
  blob3: {
    position: 'absolute',
    width: width * 0.9,
    height: width * 0.9,
    borderRadius: width * 0.45,
    top: height * 0.4,
    left: -width * 0.2,
    overflow: 'hidden',
  },
  blob4: {
    position: 'absolute',
    width: width * 1.0,
    height: width * 1.0,
    borderRadius: width * 0.5,
    top: height * 0.15,
    right: -width * 0.4,
    overflow: 'hidden',
  },
  blobGradient: {
    width: '100%',
    height: '100%',
  },
  particleContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1,
  },
  particle: {
    position: 'absolute',
    backgroundColor: '#ffffff',
    borderRadius: 10,
  },
  horizontalLinesContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 2,
  },
  horizontalLine: {
    position: 'absolute',
    width: width,
    height: 1,
    backgroundColor: '#4361ee',
    opacity: 0.15,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(10, 10, 24, 0.65)',
    zIndex: 3,
  }
});