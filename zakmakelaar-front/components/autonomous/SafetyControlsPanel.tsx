import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as Haptics from "expo-haptics";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  FadeIn,
  FadeInUp,
} from "react-native-reanimated";
import { LinearGradient } from "expo-linear-gradient";
import { useAIStore } from "../../store/aiStore";
import { useAuthStore } from "../../store/authStore";

// Define theme colors
const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
  lightGray: "#f3f4f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
};

// Safety Status Card Component
const SafetyStatusCard = ({
  title,
  description,
  status,
  icon,
  onAction,
  actionLabel,
}: {
  title: string;
  description: string;
  status: "safe" | "warning" | "danger";
  icon: string;
  onAction?: () => void;
  actionLabel?: string;
}) => {
  const getStatusColor = () => {
    switch (status) {
      case "safe":
        return THEME.success;
      case "warning":
        return THEME.warning;
      case "danger":
        return THEME.danger;
      default:
        return THEME.gray;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case "safe":
        return "Safe";
      case "warning":
        return "Warning";
      case "danger":
        return "Critical";
      default:
        return "Unknown";
    }
  };

  return (
    <Animated.View
      style={[styles.statusCard, { borderLeftColor: getStatusColor() }]}
      entering={FadeInUp.duration(400)}
    >
      <View style={styles.statusHeader}>
        <View
          style={[styles.statusIcon, { backgroundColor: getStatusColor() }]}
        >
          <Ionicons name={icon as any} size={20} color={THEME.light} />
        </View>
        <View style={styles.statusInfo}>
          <Text style={styles.statusTitle}>{title}</Text>
          <Text style={styles.statusDescription}>{description}</Text>
        </View>
        <View style={styles.statusBadge}>
          <Text style={[styles.statusBadgeText, { color: getStatusColor() }]}>
            {getStatusText()}
          </Text>
        </View>
      </View>

      {onAction && actionLabel && (
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: getStatusColor() }]}
          onPress={onAction}
        >
          <Text style={styles.actionButtonText}>{actionLabel}</Text>
        </TouchableOpacity>
      )}
    </Animated.View>
  );
};

// Limits Overview Component
const LimitsOverview = ({
  dailyUsed,
  dailyLimit,
  weeklyUsed,
  weeklyLimit,
  onResetDaily,
  onResetWeekly,
}: {
  dailyUsed: number;
  dailyLimit: number;
  weeklyUsed: number;
  weeklyLimit: number;
  onResetDaily: () => void;
  onResetWeekly: () => void;
}) => {
  const dailyPercentage = Math.min((dailyUsed / dailyLimit) * 100, 100);
  const weeklyPercentage = Math.min((weeklyUsed / weeklyLimit) * 100, 100);

  const getDailyStatus = () => {
    if (dailyPercentage >= 100) return "danger";
    if (dailyPercentage >= 80) return "warning";
    return "safe";
  };

  const getWeeklyStatus = () => {
    if (weeklyPercentage >= 100) return "danger";
    if (weeklyPercentage >= 80) return "warning";
    return "safe";
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 100) return THEME.danger;
    if (percentage >= 80) return THEME.warning;
    return THEME.success;
  };

  return (
    <Animated.View
      style={styles.limitsCard}
      entering={FadeInUp.duration(400).delay(200)}
    >
      <Text style={styles.limitsTitle}>Application Limits</Text>

      {/* Daily Limit */}
      <View style={styles.limitItem}>
        <View style={styles.limitHeader}>
          <Text style={styles.limitLabel}>Daily Applications</Text>
          <Text style={styles.limitValue}>
            {dailyUsed} / {dailyLimit}
          </Text>
        </View>
        <View style={styles.progressContainer}>
          <View style={styles.progressTrack}>
            <View
              style={[
                styles.progressBar,
                {
                  width: `${dailyPercentage}%`,
                  backgroundColor: getProgressColor(dailyPercentage),
                },
              ]}
            />
          </View>
          <TouchableOpacity
            style={[styles.resetButton, { opacity: dailyUsed > 0 ? 1 : 0.5 }]}
            onPress={onResetDaily}
            disabled={dailyUsed === 0}
          >
            <Ionicons name="refresh" size={16} color={THEME.primary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Weekly Limit */}
      <View style={styles.limitItem}>
        <View style={styles.limitHeader}>
          <Text style={styles.limitLabel}>Weekly Applications</Text>
          <Text style={styles.limitValue}>
            {weeklyUsed} / {weeklyLimit}
          </Text>
        </View>
        <View style={styles.progressContainer}>
          <View style={styles.progressTrack}>
            <View
              style={[
                styles.progressBar,
                {
                  width: `${weeklyPercentage}%`,
                  backgroundColor: getProgressColor(weeklyPercentage),
                },
              ]}
            />
          </View>
          <TouchableOpacity
            style={[styles.resetButton, { opacity: weeklyUsed > 0 ? 1 : 0.5 }]}
            onPress={onResetWeekly}
            disabled={weeklyUsed === 0}
          >
            <Ionicons name="refresh" size={16} color={THEME.primary} />
          </TouchableOpacity>
        </View>
      </View>
    </Animated.View>
  );
};

// Emergency Controls Component
const EmergencyControls = ({
  onPauseAll,
  onResetCounters,
  onClearActivities,
}: {
  onPauseAll: () => void;
  onResetCounters: () => void;
  onClearActivities: () => void;
}) => {
  return (
    <Animated.View
      style={styles.emergencyCard}
      entering={FadeInUp.duration(400).delay(400)}
    >
      <Text style={styles.emergencyTitle}>Emergency Controls</Text>
      <Text style={styles.emergencyDescription}>
        Use these controls in case of issues with autonomous operations
      </Text>

      <View style={styles.emergencyButtons}>
        <TouchableOpacity
          style={[styles.emergencyButton, styles.pauseButton]}
          onPress={onPauseAll}
        >
          <Ionicons name="pause" size={20} color={THEME.light} />
          <Text style={styles.emergencyButtonText}>Pause All</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.emergencyButton, styles.resetButton]}
          onPress={onResetCounters}
        >
          <Ionicons name="refresh" size={20} color={THEME.light} />
          <Text style={styles.emergencyButtonText}>Reset Counters</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.emergencyButton, styles.clearButton]}
          onPress={onClearActivities}
        >
          <Ionicons name="trash" size={20} color={THEME.light} />
          <Text style={styles.emergencyButtonText}>Clear Log</Text>
        </TouchableOpacity>
      </View>
    </Animated.View>
  );
};

// Main Component
export const SafetyControlsPanel = () => {
  const { user } = useAuthStore();
  const {
    autonomousStatus,
    autonomousSettings,
    applications,
    checkDailyLimit,
    checkWeeklyLimit,
    checkRecentRejections,
    resetDailyCounters,
    resetWeeklyCounters,
    pauseAutonomousMode,
    clearAutonomousActivities,
  } = useAIStore();

  const [refreshing, setRefreshing] = useState(false);

  // Calculate safety status
  const isDailyLimitReached = checkDailyLimit();
  const isWeeklyLimitReached = checkWeeklyLimit();
  const hasRecentRejections = checkRecentRejections();

  // Get recent rejections count
  const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  const recentRejections = applications.filter((app) => {
    if (app.response?.status !== "rejected" || !app.response.receivedAt)
      return false;
    const receivedDate =
      typeof app.response.receivedAt === "string"
        ? new Date(app.response.receivedAt)
        : app.response.receivedAt;
    return receivedDate > oneDayAgo;
  });

  const handleResetDaily = () => {
    Alert.alert(
      "Reset Daily Counter",
      "Are you sure you want to reset the daily application counter? This will reset the counter in the backend database.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Reset",
          onPress: async () => {
            try {
              setRefreshing(true);
              await resetDailyCounters();
              Haptics.notificationAsync(
                Haptics.NotificationFeedbackType.Success
              );
            } catch (error: any) {
              console.error("Failed to reset daily counter:", error);
              Alert.alert(
                "Reset Failed",
                error.message ||
                  "Failed to reset daily counter. Please try again.",
                [{ text: "OK" }]
              );
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            } finally {
              setRefreshing(false);
            }
          },
        },
      ]
    );
  };

  const handleResetWeekly = () => {
    Alert.alert(
      "Reset Weekly Counter",
      "Are you sure you want to reset the weekly application counter?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Reset",
          onPress: () => {
            resetWeeklyCounters();
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          },
        },
      ]
    );
  };

  const handlePauseAll = () => {
    Alert.alert(
      "Emergency Pause",
      "This will immediately pause all autonomous operations. Continue?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Pause All",
          style: "destructive",
          onPress: async () => {
            try {
              await pauseAutonomousMode("Emergency pause activated");
              Haptics.notificationAsync(
                Haptics.NotificationFeedbackType.Success
              );
            } catch (error) {
              Alert.alert("Error", "Failed to pause autonomous mode");
            }
          },
        },
      ]
    );
  };

  const handleResetCounters = () => {
    Alert.alert(
      "Reset All Counters",
      "This will reset both daily and weekly application counters in the backend database. Continue?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Reset All",
          onPress: async () => {
            try {
              setRefreshing(true);
              await resetDailyCounters();
              resetWeeklyCounters(); // Weekly is still local only
              Haptics.notificationAsync(
                Haptics.NotificationFeedbackType.Success
              );
            } catch (error: any) {
              console.error("Failed to reset counters:", error);
              Alert.alert(
                "Reset Failed",
                error.message || "Failed to reset counters. Please try again.",
                [{ text: "OK" }]
              );
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            } finally {
              setRefreshing(false);
            }
          },
        },
      ]
    );
  };

  const handleClearActivities = () => {
    Alert.alert(
      "Clear Activity Log",
      "This will permanently delete all autonomous activity logs. Continue?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear",
          style: "destructive",
          onPress: () => {
            clearAutonomousActivities();
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          },
        },
      ]
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Safety Status Cards */}
      <SafetyStatusCard
        title="Daily Application Limit"
        description={`${autonomousStatus.applicationsToday} of ${autonomousSettings.maxApplicationsPerDay} applications used today`}
        status={
          isDailyLimitReached
            ? "danger"
            : autonomousStatus.applicationsToday >=
              autonomousSettings.maxApplicationsPerDay * 0.8
            ? "warning"
            : "safe"
        }
        icon="calendar"
        onAction={isDailyLimitReached ? handleResetDaily : undefined}
        actionLabel={isDailyLimitReached ? "Reset Daily" : undefined}
      />

      <SafetyStatusCard
        title="Weekly Application Limit"
        description={`${autonomousStatus.applicationsThisWeek} of ${autonomousSettings.maxApplicationsPerWeek} applications used this week`}
        status={
          isWeeklyLimitReached
            ? "danger"
            : autonomousStatus.applicationsThisWeek >=
              autonomousSettings.maxApplicationsPerWeek * 0.8
            ? "warning"
            : "safe"
        }
        icon="calendar-outline"
        onAction={isWeeklyLimitReached ? handleResetWeekly : undefined}
        actionLabel={isWeeklyLimitReached ? "Reset Weekly" : undefined}
      />

      <SafetyStatusCard
        title="Recent Rejections"
        description={`${recentRejections.length} rejections in the last 24 hours`}
        status={
          hasRecentRejections
            ? "danger"
            : recentRejections.length >= 2
            ? "warning"
            : "safe"
        }
        icon="close-circle"
      />

      {/* Limits Overview */}
      <LimitsOverview
        dailyUsed={autonomousStatus.applicationsToday}
        dailyLimit={autonomousSettings.maxApplicationsPerDay}
        weeklyUsed={autonomousStatus.applicationsThisWeek}
        weeklyLimit={autonomousSettings.maxApplicationsPerWeek}
        onResetDaily={handleResetDaily}
        onResetWeekly={handleResetWeekly}
      />

      {/* Emergency Controls */}
      <EmergencyControls
        onPauseAll={handlePauseAll}
        onResetCounters={handleResetCounters}
        onClearActivities={handleClearActivities}
      />

      <View style={styles.bottomSpacer} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  statusCard: {
    backgroundColor: THEME.light,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statusHeader: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  statusInfo: {
    flex: 1,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: THEME.dark,
    marginBottom: 4,
  },
  statusDescription: {
    fontSize: 14,
    color: THEME.gray,
    lineHeight: 20,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: THEME.lightGray,
  },
  statusBadgeText: {
    fontSize: 12,
    fontWeight: "600",
  },
  actionButton: {
    marginTop: 12,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: "center",
  },
  actionButtonText: {
    color: THEME.light,
    fontSize: 14,
    fontWeight: "600",
  },
  limitsCard: {
    backgroundColor: THEME.light,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  limitsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME.dark,
    marginBottom: 16,
  },
  limitItem: {
    marginBottom: 16,
  },
  limitHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  limitLabel: {
    fontSize: 14,
    color: THEME.dark,
    fontWeight: "500",
  },
  limitValue: {
    fontSize: 14,
    color: THEME.gray,
    fontWeight: "600",
  },
  progressContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  progressTrack: {
    flex: 1,
    height: 8,
    backgroundColor: THEME.lightGray,
    borderRadius: 4,
    overflow: "hidden",
    marginRight: 12,
  },
  progressBar: {
    height: "100%",
    borderRadius: 4,
  },
  resetButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: "rgba(67, 97, 238, 0.1)",
  },
  emergencyCard: {
    backgroundColor: THEME.light,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: THEME.danger,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  emergencyTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: THEME.danger,
    marginBottom: 8,
  },
  emergencyDescription: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 16,
    lineHeight: 20,
  },
  emergencyButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  emergencyButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  pauseButton: {
    backgroundColor: THEME.warning,
  },
  clearButton: {
    backgroundColor: THEME.danger,
  },
  emergencyButtonText: {
    color: THEME.light,
    fontSize: 12,
    fontWeight: "600",
    marginLeft: 4,
  },
  bottomSpacer: {
    height: 32,
  },
});
