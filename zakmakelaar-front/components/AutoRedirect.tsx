import React, { useEffect } from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuthStore } from '../store/authStore';
import { navigationService } from '../services/navigationService';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface AutoRedirectProps {
  children: React.ReactNode;
  redirectIfAuthenticated?: boolean;
  redirectIfUnauthenticated?: boolean;
  redirectIfNoPreferences?: boolean;
  redirectTo?: string;
}

/**
 * Component that automatically redirects based on user authentication state
 */
export const AutoRedirect: React.FC<AutoRedirectProps> = ({
  children,
  redirectIfAuthenticated = false,
  redirectIfUnauthenticated = false,
  redirectIfNoPreferences = false,
  redirectTo
}) => {
  const router = useRouter();
  const { isAuthenticated, isLoading, user } = useAuthStore();
  
  useEffect(() => {
    const checkRedirection = async () => {
      // Skip redirection if still loading
      if (isLoading) return;
      
      // Redirect if authenticated and redirectIfAuthenticated is true
      if (isAuthenticated && redirectIfAuthenticated) {
        if (redirectTo) {
          router.replace(redirectTo);
        } else {
          await navigationService.navigateBasedOnAuthState();
        }
        return;
      }
      
      // Redirect if unauthenticated and redirectIfUnauthenticated is true
      if (!isAuthenticated && redirectIfUnauthenticated) {
        if (redirectTo) {
          router.replace(redirectTo);
        } else {
          router.replace('/');
        }
        return;
      }
      
      // Check redirect blocker before redirecting
      if (isAuthenticated && redirectIfNoPreferences && (!user || !user.preferences)) {
        // Check redirect blocker service
        try {
          const { redirectBlockerService } = await import('../services/redirectBlockerService');
          
          if (redirectBlockerService.areRedirectsBlocked()) {
            console.log('AutoRedirect - Redirects are BLOCKED, staying on current screen');
            return;
          }
        } catch (error) {
          console.error('Error checking redirect blocker:', error);
        }
        
        if (redirectTo) {
          router.replace(redirectTo);
        } else {
          router.replace('/preferences');
        }
        return;
      }
    };
    
    checkRedirection();
  }, [isAuthenticated, isLoading, user, redirectIfAuthenticated, redirectIfUnauthenticated, redirectIfNoPreferences, redirectTo]);
  
  // Show loading indicator while checking authentication
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f72585" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }
  
  // Render children if no redirection is needed
  return <>{children}</>;
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#0a0a18',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#ffffff',
  }
});