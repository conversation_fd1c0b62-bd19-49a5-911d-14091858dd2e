import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useAuthStore } from '../store/authStore';
import { authService } from '../services/authService';
import * as SecureStore from 'expo-secure-store';

export default function AuthLogicTest() {
  const [results, setResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user, isAuthenticated, checkAuthStatus, logout } = useAuthStore();

  const addResult = (result: string) => {
    setResults(prev => [...prev, result]);
    console.log(result);
  };

  const testAuthLogic = async () => {
    setIsLoading(true);
    setResults([]);
    
    try {
      addResult('🧪 Testing Auth Logic...');
      
      // Test 1: Check current auth state
      addResult(`📊 Current State: ${isAuthenticated ? 'Authenticated' : 'Not Authenticated'}`);
      addResult(`👤 Current User: ${user?.email || 'None'}`);
      
      // Test 2: Check stored token
      const storedToken = await authService.getAuthToken();
      addResult(`🔑 Stored Token: ${storedToken ? 'Present' : 'None'}`);
      
      // Test 3: Test auth status check
      addResult('🔍 Testing checkAuthStatus...');
      await checkAuthStatus();
      const newState = useAuthStore.getState();
      addResult(`📊 After Check: ${newState.isAuthenticated ? 'Authenticated' : 'Not Authenticated'}`);
      
      // Test 4: If authenticated, test token validation
      if (newState.isAuthenticated) {
        addResult('🔍 Testing token validation...');
        try {
          const userResult = await authService.getCurrentUser();
          if (userResult.success) {
            addResult('✅ Token is valid');
            addResult(`👤 User: ${userResult.data?.email}`);
          } else {
            addResult('❌ Token validation failed');
          }
        } catch (error: any) {
          addResult(`❌ Token validation error: ${error.message}`);
        }
      }
      
      // Test 5: Test logout if authenticated
      if (newState.isAuthenticated) {
        addResult('🔍 Testing logout...');
        await logout();
        const logoutState = useAuthStore.getState();
        addResult(`📊 After Logout: ${logoutState.isAuthenticated ? 'Still Authenticated' : 'Logged Out'}`);
        
        // Check if token was cleared
        const tokenAfterLogout = await authService.getAuthToken();
        addResult(`🔑 Token After Logout: ${tokenAfterLogout ? 'Still Present' : 'Cleared'}`);
      }
      
      addResult('🎉 Auth logic test completed!');
      
    } catch (error: any) {
      addResult('💥 Test failed with exception');
      addResult(`Error: ${error.message}`);
      console.error('Auth logic test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testTokenManipulation = async () => {
    setIsLoading(true);
    setResults([]);
    
    try {
      addResult('🔧 Testing Token Manipulation...');
      
      // Test 1: Set invalid token
      addResult('🔍 Setting invalid token...');
      await SecureStore.setItemAsync('access_token', 'invalid-token-123');
      
      // Test 2: Check auth status with invalid token
      addResult('🔍 Checking auth status with invalid token...');
      await checkAuthStatus();
      const state = useAuthStore.getState();
      addResult(`📊 Result: ${state.isAuthenticated ? 'Still Authenticated (BAD)' : 'Logged Out (GOOD)'}`);
      
      // Test 3: Check if invalid token was cleared
      const tokenAfterCheck = await authService.getAuthToken();
      addResult(`🔑 Token After Check: ${tokenAfterCheck ? 'Still Present' : 'Cleared'}`);
      
      addResult('🎉 Token manipulation test completed!');
      
    } catch (error: any) {
      addResult('💥 Test failed with exception');
      addResult(`Error: ${error.message}`);
      console.error('Token manipulation test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Auth Logic Test</Text>
      
      <View style={styles.statusContainer}>
        <Text style={styles.statusText}>
          Status: {isAuthenticated ? '🟢 Authenticated' : '🔴 Not Authenticated'}
        </Text>
        <Text style={styles.statusText}>
          User: {user?.email || 'None'}
        </Text>
      </View>
      
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={[styles.button, styles.primaryButton, isLoading && styles.buttonDisabled]} 
          onPress={testAuthLogic}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Testing...' : 'Test Auth Logic'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.secondaryButton, isLoading && styles.buttonDisabled]} 
          onPress={testTokenManipulation}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            Test Token Handling
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.button, styles.clearButton]} 
          onPress={clearResults}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>Clear Results</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView style={styles.resultsContainer}>
        {results.map((result, index) => (
          <Text key={index} style={styles.resultText}>
            {result}
          </Text>
        ))}
        {results.length === 0 && (
          <Text style={styles.emptyText}>
            No results yet. Tap a test button to start.
          </Text>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f3f4f6',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  statusContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  statusText: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  button: {
    flex: 1,
    minWidth: 100,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#f72585',
  },
  secondaryButton: {
    backgroundColor: '#3b82f6',
  },
  clearButton: {
    backgroundColor: '#6b7280',
  },
  buttonDisabled: {
    backgroundColor: '#9ca3af',
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  resultsContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
  },
  resultText: {
    fontSize: 13,
    color: '#374151',
    marginBottom: 6,
    fontFamily: 'monospace',
  },
  emptyText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
});
