import { ApiResponse, apiService, PaginatedResponse } from "./api";

// Extended API response for preference-based listings with fallback info
interface PreferenceBasedListingsResponse
  extends ApiResponse<{ listings: Listing[] }> {
  fallbackUsed?: boolean;
}

// Listing types
export interface Listing {
  id: any;
  _id: string;
  title: string;
  description?: string;
  price: number | string; // Backend sends string prices
  location:
    | string
    | {
        address: string;
        city: string;
        postalCode: string;
        coordinates?: {
          lat: number;
          lng: number;
        };
      };
  propertyType?: string;
  rooms?: number | string; // Backend sends string rooms
  bedrooms?: number | string;
  bathrooms?: number | string;
  area?: number; // in square meters
  size?: string; // Backend field for size
  year?: string; // Backend field for year built
  interior?: string; // Backend field for interior type
  furnished?: boolean;
  pets?: boolean;
  smoking?: boolean;
  garden?: boolean;
  balcony?: boolean;
  parking?: boolean;
  images?: string[];
  url: string;
  source: string;
  dateAdded: string;
  dateAvailable?: string;
  isActive?: boolean;
  features?: string[];
  energyLabel?: string;
  deposit?: number;
  utilities?: number;
  contactInfo?: {
    name?: string;
    phone?: string;
    email?: string;
  };
}

export interface ListingFilters {
  minPrice?: number;
  maxPrice?: number;
  minRooms?: number;
  maxRooms?: number;
  minArea?: number;
  maxArea?: number;
  propertyTypes?: string[];
  cities?: string[];
  furnished?: boolean;
  pets?: boolean;
  smoking?: boolean;
  garden?: boolean;
  balcony?: boolean;
  parking?: boolean;
  dateAddedAfter?: string;
  source?: string[];
  search?: string; // General search term
}

export interface ListingQuery extends ListingFilters {
  page?: number;
  limit?: number;
  sortBy?: "dateAdded" | "price" | "area" | "rooms";
  sortOrder?: "asc" | "desc";
}

export interface ListingStats {
  total?: number;
  totalListings?: number;
  bySource?: Record<string, number>;
  byPropertyType?: Record<string, number>;
  byCity?: Record<string, number>;
  averagePrice?: number;
  avgPrice?: number | null;
  priceRange?: {
    min: number;
    max: number;
  };
  minPrice?: string;
  maxPrice?: string;
  uniqueLocations?: number;
  uniquePropertyTypes?: number;
  locations?: string[];
  propertyTypes?: string[];
  stats?: {
    totalListings: number;
    avgPrice: number | null;
    minPrice: string;
    maxPrice: string;
    uniqueLocations: number;
    uniquePropertyTypes: number;
    locations: string[];
    propertyTypes: string[];
  };
}

class ListingsService {
  /**
   * Get listings with optional filters and pagination
   */
  async getListings(
    query: ListingQuery = {}
  ): Promise<PaginatedResponse<{ listings: Listing[] }>> {
    try {
      const params = this.buildQueryParams(query);
      return await apiService.get<{ listings: Listing[] }>("/listings", params);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get a single listing by ID
   */
  async getListing(id: string): Promise<ApiResponse<Listing>> {
    try {
      return await apiService.get<Listing>(`/listings/${id}`);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Search listings with text query
   */
  async searchListings(
    searchTerm: string,
    filters: ListingFilters = {},
    page = 1,
    limit = 20
  ): Promise<PaginatedResponse<{ listings: Listing[] }>> {
    try {
      const query: ListingQuery = {
        ...filters,
        search: searchTerm,
        page,
        limit,
      };
      return await this.getListings(query);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get listings by location
   */
  async getListingsByLocation(
    city: string,
    filters: ListingFilters = {},
    page = 1,
    limit = 20
  ): Promise<PaginatedResponse<{ listings: Listing[] }>> {
    try {
      const query: ListingQuery = {
        ...filters,
        cities: [city],
        page,
        limit,
      };
      return await this.getListings(query);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get recent listings
   */
  async getRecentListings(
    limit = 10,
    filters: ListingFilters = {}
  ): Promise<ApiResponse<{ listings: Listing[] }>> {
    try {
      const query: ListingQuery = {
        ...filters,
        limit,
        sortBy: "dateAdded",
        sortOrder: "desc",
      };
      const response = await this.getListings(query);
      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get listings based on user preferences
   */
  async getPreferenceBasedListings(
    limit = 10
  ): Promise<PreferenceBasedListingsResponse> {
    try {
      // Import auth service to get user preferences
      const { authService } = await import("./authService");
      const userData = await authService.getCachedUser();

      if (!userData?.preferences) {
        console.log("⚠️ No preferences found, falling back to recent listings");
        // If no preferences, fall back to recent listings
        return this.getRecentListings(limit);
      }

      const preferences = userData.preferences;

      // Build filters from user preferences
      const filters: ListingFilters = {};

      if (preferences.minPrice !== undefined) {
        filters.minPrice = preferences.minPrice;
      }

      if (preferences.maxPrice !== undefined) {
        filters.maxPrice = preferences.maxPrice;
      }

      if (preferences.minRooms !== undefined) {
        filters.minRooms = preferences.minRooms;
      }

      if (preferences.maxRooms !== undefined) {
        filters.maxRooms = preferences.maxRooms;
      }

      if (
        preferences.preferredLocations &&
        preferences.preferredLocations.length > 0
      ) {
        // Make location matching more flexible by including variations
        const expandedLocations = preferences.preferredLocations.flatMap(
          (location) => {
            const variations = [location];

            // Add common variations for major cities
            if (location.toLowerCase().includes("amsterdam")) {
              variations.push(
                "Amsterdam",
                "amsterdam",
                "Amsterdam Centrum",
                "Amsterdam Noord",
                "Amsterdam Zuid",
                "Amsterdam West",
                "Amsterdam Oost"
              );
            }
            if (location.toLowerCase().includes("rotterdam")) {
              variations.push(
                "Rotterdam",
                "rotterdam",
                "Rotterdam Centrum",
                "Rotterdam Noord",
                "Rotterdam Zuid"
              );
            }
            if (
              location.toLowerCase().includes("den haag") ||
              location.toLowerCase().includes("the hague")
            ) {
              variations.push(
                "Den Haag",
                "The Hague",
                "den haag",
                "the hague",
                "Haag"
              );
            }
            if (location.toLowerCase().includes("utrecht")) {
              variations.push("Utrecht", "utrecht", "Utrecht Centrum");
            }

            return [...new Set(variations)]; // Remove duplicates
          }
        );

        filters.cities = expandedLocations;

        // Dev log removed for production cleanliness
      }

      if (preferences.propertyTypes && preferences.propertyTypes.length > 0) {
        // Map English property types to Dutch equivalents that exist in the database
        const propertyTypeMapping: Record<string, string[]> = {
          apartment: ["woning", "appartement"],
          house: ["woning", "huis"],
          studio: ["woning", "studio"],
          room: ["woning", "kamer"],
          other: ["woning"],
        };

        // Expand property types to include Dutch equivalents
        const expandedPropertyTypes = preferences.propertyTypes.flatMap(
          (type) => {
            const mappedTypes = propertyTypeMapping[type.toLowerCase()];
            return mappedTypes || [type];
          }
        );

        // Remove duplicates
        filters.propertyTypes = [...new Set(expandedPropertyTypes)];

        // Dev log removed for production cleanliness
      }

      const query: ListingQuery = {
        ...filters,
        limit,
        sortBy: "dateAdded",
        sortOrder: "desc",
      };

      const response = await this.getListings(query);

      // If no results found with preferences, fall back to recent listings
      if (
        response.success &&
        response.data &&
        response.data.listings.length === 0
      ) {
        console.log(
          "⚠️ No listings found with preferences, falling back to recent listings"
        );
        const fallbackResponse = await this.getRecentListings(limit);
        return {
          ...fallbackResponse,
          message:
            "No listings found matching your preferences. Showing recent listings instead.",
          fallbackUsed: true,
        } as PreferenceBasedListingsResponse;
      }

      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      // If preference-based filtering fails, fall back to recent listings
      console.warn(
        "Preference-based filtering failed, falling back to recent listings:",
        error
      );
      const fallbackResponse = await this.getRecentListings(limit);
      return {
        ...fallbackResponse,
        message:
          "Unable to load preference-based listings. Showing recent listings instead.",
        fallbackUsed: true,
      } as PreferenceBasedListingsResponse;
    }
  }

  /**
   * Get listings statistics
   */
  async getListingsStats(
    filters: ListingFilters = {}
  ): Promise<ApiResponse<ListingStats>> {
    try {
      const params = this.buildQueryParams(filters);
      return await apiService.get<ListingStats>("/search/stats", params);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get quick dashboard statistics from backend
   */
  async getQuickStats(): Promise<
    ApiResponse<{
      totalListings: number;
      averagePrice: number;
      newToday: number;
    }>
  > {
    try {
      const response = await apiService.get<{
        stats: {
          totalListings: number;
          averagePrice: number;
          newToday: number;
        };
      }>("/listings/quick-stats");

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data.stats,
          message: response.message,
        };
      }

      throw new Error(response.message || "Failed to fetch quick stats");
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get available cities
   */
  async getAvailableCities(): Promise<ApiResponse<string[]>> {
    try {
      return await apiService.get<string[]>("/listings/cities");
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get available property types
   */
  async getPropertyTypes(): Promise<ApiResponse<string[]>> {
    try {
      return await apiService.get<string[]>("/listings/property-types");
    } catch (error) {
      throw error;
    }
  }

  /**
   * Save/favorite a listing
   */
  async saveListing(listingId: string): Promise<ApiResponse> {
    try {
      return await apiService.post(`/listings/${listingId}/save`);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Remove listing from saved/favorites
   */
  async unsaveListing(listingId: string): Promise<ApiResponse> {
    try {
      return await apiService.delete(`/listings/${listingId}/save`);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get user's saved listings
   */
  async getSavedListings(
    page = 1,
    limit = 20
  ): Promise<PaginatedResponse<{ listings: Listing[] }>> {
    try {
      return await apiService.get<{ listings: Listing[] }>("/listings/saved", {
        page,
        limit,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Report a listing (for inappropriate content, etc.)
   */
  async reportListing(
    listingId: string,
    reason: string,
    details?: string
  ): Promise<ApiResponse> {
    try {
      return await apiService.post(`/listings/${listingId}/report`, {
        reason,
        details,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get similar listings
   */
  async getSimilarListings(
    listingId: string,
    limit = 5
  ): Promise<ApiResponse<{ listings: Listing[] }>> {
    try {
      return await apiService.get<{ listings: Listing[] }>(
        `/listings/${listingId}/similar`,
        {
          limit,
        }
      );
    } catch (error) {
      throw error;
    }
  }

  /**
   * Build query parameters for API requests
   */
  private buildQueryParams(query: ListingQuery): Record<string, any> {
    const params: Record<string, any> = {};

    // Pagination
    if (query.page) params.page = query.page;
    if (query.limit) params.limit = query.limit;

    // Sorting
    if (query.sortBy) params.sortBy = query.sortBy;
    if (query.sortOrder) params.sortOrder = query.sortOrder;

    // Price filters
    if (query.minPrice) params.minPrice = query.minPrice;
    if (query.maxPrice) params.maxPrice = query.maxPrice;

    // Room filters
    if (query.minRooms) params.minRooms = query.minRooms;
    if (query.maxRooms) params.maxRooms = query.maxRooms;

    // Area filters
    if (query.minArea) params.minArea = query.minArea;
    if (query.maxArea) params.maxArea = query.maxArea;

    // Array filters
    if (query.propertyTypes?.length)
      params.propertyTypes = query.propertyTypes.join(",");
    if (query.cities?.length) params.cities = query.cities.join(",");
    if (query.source?.length) params.source = query.source.join(",");

    // Boolean filters
    if (query.furnished !== undefined) params.furnished = query.furnished;
    if (query.pets !== undefined) params.pets = query.pets;
    if (query.smoking !== undefined) params.smoking = query.smoking;
    if (query.garden !== undefined) params.garden = query.garden;
    if (query.balcony !== undefined) params.balcony = query.balcony;
    if (query.parking !== undefined) params.parking = query.parking;

    // Date filters
    if (query.dateAddedAfter) params.dateAddedAfter = query.dateAddedAfter;

    // Search
    if (query.search) params.search = query.search;

    return params;
  }

  /**
   * Format price for display
   */
  formatPrice(price: number | string | undefined | null): string {
    if (price === undefined || price === null || price === "") {
      return "€ --";
    }

    let numPrice: number;

    if (typeof price === "string") {
      // Remove all non-numeric characters except dots and commas
      const cleanPrice = price.replace(/[^\d.,]/g, "");

      // Handle different number formats:
      // "1,500" (thousands separator) -> 1500
      // "1.500" (European thousands separator) -> 1500
      // "1,500.50" (US format with decimal) -> 1500.50
      // "1.500,50" (European format with decimal) -> 1500.50

      let normalizedPrice: string;

      // Check if it's European format (dot as thousands, comma as decimal)
      if (
        cleanPrice.includes(",") &&
        cleanPrice.includes(".") &&
        cleanPrice.lastIndexOf(".") < cleanPrice.lastIndexOf(",")
      ) {
        // European format: "1.500,50" -> "1500.50"
        normalizedPrice = cleanPrice.replace(/\./g, "").replace(",", ".");
      }
      // Check if it's US format (comma as thousands, dot as decimal)
      else if (
        cleanPrice.includes(",") &&
        cleanPrice.includes(".") &&
        cleanPrice.lastIndexOf(",") < cleanPrice.lastIndexOf(".")
      ) {
        // US format: "1,500.50" -> "1500.50"
        normalizedPrice = cleanPrice.replace(/,/g, "");
      }
      // Only comma (could be thousands separator or decimal)
      else if (cleanPrice.includes(",") && !cleanPrice.includes(".")) {
        // If comma is in thousands position (e.g., "1,500"), remove it
        // If comma is in decimal position (e.g., "15,50"), replace with dot
        const commaIndex = cleanPrice.indexOf(",");
        const digitsAfterComma = cleanPrice.length - commaIndex - 1;

        if (digitsAfterComma === 3 && commaIndex > 0) {
          // Thousands separator: "1,500" -> "1500"
          normalizedPrice = cleanPrice.replace(/,/g, "");
        } else {
          // Decimal separator: "15,50" -> "15.50"
          normalizedPrice = cleanPrice.replace(",", ".");
        }
      }
      // Only dot (could be thousands separator or decimal)
      else if (cleanPrice.includes(".") && !cleanPrice.includes(",")) {
        const dotIndex = cleanPrice.indexOf(".");
        const digitsAfterDot = cleanPrice.length - dotIndex - 1;

        if (digitsAfterDot === 3 && dotIndex > 0) {
          // Thousands separator: "1.500" -> "1500"
          normalizedPrice = cleanPrice.replace(/\./g, "");
        } else {
          // Decimal separator: "15.50" -> "15.50"
          normalizedPrice = cleanPrice;
        }
      }
      // No separators
      else {
        normalizedPrice = cleanPrice;
      }

      numPrice = parseFloat(normalizedPrice);
    } else {
      numPrice = price;
    }

    if (isNaN(numPrice) || numPrice <= 0) {
      return "€ --";
    }

    return new Intl.NumberFormat("nl-NL", {
      style: "currency",
      currency: "EUR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(numPrice);
  }

  /**
   * Format area for display
   */
  formatArea(area: number): string {
    return `${area} m²`;
  }

  /**
   * Get property type display name
   */
  getPropertyTypeDisplayName(type: string): string {
    const displayNames: Record<string, string> = {
      apartment: "Apartment",
      house: "House",
      woning: "Woning",
      studio: "Studio",
      room: "Room",
      other: "Other",
    };
    return displayNames[type] || type;
  }
}

// Export singleton instance
export const listingsService = new ListingsService();
export default listingsService;
