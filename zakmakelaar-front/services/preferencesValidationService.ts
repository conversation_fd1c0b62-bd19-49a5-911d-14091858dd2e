import { UserPreferences } from './authService';

/**
 * Service for validating user preferences
 */
export class PreferencesValidationService {
  /**
   * Validates if user preferences are complete and valid
   * @param preferences User preferences to validate
   * @returns Object with validation result and any missing fields
   */
  static validatePreferences(preferences: Partial<UserPreferences> | null | undefined): {
    isValid: boolean;
    missingFields: string[];
    message: string;
  } {
    const result = {
      isValid: false,
      missingFields: [] as string[],
      message: ''
    };

    // Check if preferences exist
    if (!preferences) {
      result.message = 'Preferences are missing';
      result.missingFields = ['all'];
      return result;
    }

    // Required fields and their validation rules
    const validationRules: Partial<Record<keyof UserPreferences, (value: any) => boolean>> = {
      minPrice: (value) => typeof value === 'number' && value >= 0,
      maxPrice: (value) => typeof value === 'number' && value > 0,
      maxRent: (value) => value === undefined || value === null || (typeof value === 'number' && value > 0),
      preferredLocations: (value) => Array.isArray(value) && value.length > 0,
      propertyTypes: (value) => Array.isArray(value) && value.length > 0,
      minRooms: (value) => value === undefined || value === null || (typeof value === 'number' && value >= 0),
      maxRooms: (value) => value === undefined || value === null || (typeof value === 'number' && value > 0),
      amenities: (value) => value === undefined || value === null || Array.isArray(value),
      notifications: (value) => {
        return value === undefined || value === null || (
               value && 
               typeof value === 'object' && 
               'email' in value && 
               'push' in value && 
               'sms' in value
        );
      }
    };

    // Define which fields are required (not optional)
    const requiredFields: (keyof UserPreferences)[] = ['minPrice', 'maxPrice', 'preferredLocations', 'propertyTypes'];

    // Check each required field
    for (const field of requiredFields) {
      const validator = validationRules[field];
      const value = preferences[field];
      
      if (validator && (value === undefined || value === null || !validator(value))) {
        result.missingFields.push(field);
      }
    }

    // Check optional fields if they exist
    for (const [field, validator] of Object.entries(validationRules)) {
      const fieldName = field as keyof UserPreferences;
      if (!requiredFields.includes(fieldName)) {
        const value = preferences[fieldName];
        if (value !== undefined && value !== null && !validator(value)) {
          result.missingFields.push(field);
        }
      }
    }

    // Additional validation for min/max values
    if (
      preferences.minPrice !== undefined && 
      preferences.maxPrice !== undefined && 
      preferences.minPrice > preferences.maxPrice
    ) {
      result.missingFields.push('price_range_invalid');
    }

    if (
      preferences.minRooms !== undefined && 
      preferences.maxRooms !== undefined && 
      preferences.minRooms > preferences.maxRooms
    ) {
      result.missingFields.push('rooms_range_invalid');
    }

    // Set validation result
    result.isValid = result.missingFields.length === 0;
    
    // Set appropriate message
    if (result.isValid) {
      result.message = 'Preferences are valid';
    } else {
      result.message = `Preferences are incomplete or invalid. Missing or invalid fields: ${result.missingFields.join(', ')}`;
    }

    return result;
  }

  /**
   * Checks if the user has completed the minimum required preferences
   * This is a less strict validation used for navigation purposes
   */
  static hasMinimumRequiredPreferences(preferences: Partial<UserPreferences> | null | undefined): boolean {
    if (!preferences) return false;

    // Check minimum required fields for navigation
    const hasLocation = Array.isArray(preferences.preferredLocations) && 
                        preferences.preferredLocations.length > 0;
    
    const hasPropertyType = Array.isArray(preferences.propertyTypes) && 
                           preferences.propertyTypes.length > 0;
    
    const hasBudget = typeof preferences.minPrice === 'number' && 
                      typeof preferences.maxPrice === 'number' &&
                      preferences.minPrice >= 0 &&
                      preferences.maxPrice > 0 &&
                      preferences.minPrice <= preferences.maxPrice;
    
    const hasRooms = typeof preferences.minRooms === 'number' && 
                     typeof preferences.maxRooms === 'number' &&
                     preferences.minRooms >= 0 &&
                     preferences.maxRooms > 0 &&
                     preferences.minRooms <= preferences.maxRooms;

    return hasLocation && hasPropertyType && hasBudget && hasRooms;
  }
}

// Export singleton instance
export const preferencesValidationService = new PreferencesValidationService();
export default preferencesValidationService;

// Export the static method as a named export for convenience
export const validatePreferences = PreferencesValidationService.validatePreferences;