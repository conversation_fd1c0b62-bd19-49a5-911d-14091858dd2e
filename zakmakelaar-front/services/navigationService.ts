import { router } from 'expo-router';
import { authService } from './authService';
import { PreferencesValidationService } from './preferencesValidationService';
import { LogService } from './logService';
import { welcomeService } from './welcomeService';

/**
 * Onboarding step definition
 */
export interface OnboardingStep {
  id: string;
  title: string;
  route: string;
  isCompleted?: boolean;
}

/**
 * Navigation service to handle app navigation flows
 */
class NavigationService {
  // Onboarding steps configuration
  private onboardingSteps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome',
      route: '/',
      isCompleted: false
    },
    {
      id: 'auth',
      title: 'Authentication',
      route: '/login',
      isCompleted: false
    },
    {
      id: 'preferences',
      title: 'Preferences',
      route: '/preferences',
      isCompleted: false
    }
  ];
  
  /**
   * Get onboarding steps
   */
  getOnboardingSteps(): OnboardingStep[] {
    return [...this.onboardingSteps];
  }
  
  /**
   * Get current onboarding step
   */
  getCurrentOnboardingStep(route: string): OnboardingStep | undefined {
    return this.onboardingSteps.find(step => step.route === route);
  }
  
  /**
   * Update onboarding step completion status
   */
  updateOnboardingStepStatus(stepId: string, isCompleted: boolean): void {
    const stepIndex = this.onboardingSteps.findIndex(step => step.id === stepId);
    if (stepIndex !== -1) {
      this.onboardingSteps[stepIndex].isCompleted = isCompleted;
    }
  }
  
  /**
   * Navigate to next onboarding step
   */
  navigateToNextOnboardingStep(currentStepId: string): void {
    const currentIndex = this.onboardingSteps.findIndex(step => step.id === currentStepId);
    
    if (currentIndex !== -1 && currentIndex < this.onboardingSteps.length - 1) {
      const nextStep = this.onboardingSteps[currentIndex + 1];
      this.updateOnboardingStepStatus(currentStepId, true);
      
      // Handle route navigation based on the route path
      if (nextStep.route === '/') {
        router.replace('/');
      } else if (nextStep.route === '/login') {
        router.push('/login');
      } else if (nextStep.route === '/preferences') {
        router.push('/preferences');
      } else {
        // For any other routes, try to navigate safely
        try {
          router.push(nextStep.route as any);
        } catch (error) {
          console.error('Navigation error:', error);
          // Fallback to replace if push fails
          router.replace(nextStep.route as any);
        }
      }
    } else {
      // If last step or step not found, go to dashboard
      router.replace('/dashboard');
    }
  }
  
  /**
   * Navigate based on user authentication state
   * - If user is a property owner, go to property owner dashboard
   * - If user is a tenant with preferences, go to tenant dashboard
   * - If user is a tenant without preferences, go to preferences
   * - If user is not authenticated, check if they've seen welcome screen before
   */
  async navigateBasedOnAuthState(): Promise<void> {
    try {
      // Check redirect blocker first
      const { redirectBlockerService } = await import('./redirectBlockerService');
      
      if (redirectBlockerService.areRedirectsBlocked()) {
        console.log('🚫 NavigationService.navigateBasedOnAuthState() - Redirects are BLOCKED');
        return;
      }

      const isAuthenticated = await authService.isAuthenticated();
      
      if (isAuthenticated) {
        const user = await authService.getCachedUser();
        
        // Check if user is a property owner - prioritize role first, then check propertyOwner object
        if (user && (user.role === 'owner' || (user.propertyOwner && user.propertyOwner.isPropertyOwner))) {
          LogService.info('Navigation', `Property owner (role: ${user.role}) authenticated, navigating to property owner dashboard`);
          router.replace('/property-owner/dashboard');
          return;
        }
        
        // For regular users (tenants), check preferences
        const validationResult = PreferencesValidationService.validatePreferences(user?.preferences);
        
        if (user && PreferencesValidationService.hasMinimumRequiredPreferences(user.preferences)) {
          LogService.info('Navigation', 'User has valid preferences, navigating to dashboard');
          router.replace('/dashboard');
        } else {
          LogService.warn('Navigation', 'User has invalid or incomplete preferences, redirecting to preferences setup');
          router.replace('/preferences');
        }
      } else {
        // Not authenticated - check if user has seen welcome screen before
        const hasSeenWelcome = await welcomeService.hasSeenWelcome();
        
        if (hasSeenWelcome) {
          router.replace('/login');
        } else {
          router.replace('/');
        }
      }
    } catch (error) {
      console.error('Navigation error:', error);
      router.replace('/');
    }
  }
  
  /**
   * Navigate after successful authentication
   * - If user is a property owner, go to property owner dashboard
   * - If user has preferences, go to tenant dashboard
   * - If user doesn't have preferences, go to preferences
   */
  async navigateAfterAuth(): Promise<void> {
    try {
      // Check redirect blocker first
      const { redirectBlockerService } = await import('./redirectBlockerService');
      
      if (redirectBlockerService.areRedirectsBlocked()) {
        console.log('🚫 NavigationService.navigateAfterAuth() - Redirects are BLOCKED');
        return;
      }

      const user = await authService.getCachedUser();
      
      // Check if user is a property owner
      const isOwnerRole = user?.role === 'owner';
      const hasPropertyOwnerFlag = user?.propertyOwner && user.propertyOwner.isPropertyOwner;
      
      if (user && (isOwnerRole || hasPropertyOwnerFlag)) {
        console.log('Property owner detected, navigating to property owner dashboard');
        router.replace('/property-owner/dashboard');
        return;
      }
      
      // For regular users, check preferences
      const validationResult = PreferencesValidationService.validatePreferences(user?.preferences);
      
      if (user && PreferencesValidationService.hasMinimumRequiredPreferences(user.preferences)) {
        LogService.info('Navigation', 'User has valid preferences after auth, navigating to dashboard');
        router.replace('/dashboard');
      } else {
        LogService.warn('Navigation', 'User has invalid or incomplete preferences after auth, redirecting to preferences setup');
        router.replace('/preferences');
      }
    } catch (error) {
      console.error('Error in navigateAfterAuth:', error);
      router.replace('/preferences');
    }
  }
  
  /**
   * Navigate after successful preferences setup - DISABLED TO PREVENT REDIRECT LOOPS
   * @param preferencesComplete Optional flag to indicate if preferences are complete
   */
  async navigateAfterPreferences(preferencesComplete: boolean = false): Promise<void> {
    console.log('🚫 NavigationService.navigateAfterPreferences() DISABLED to prevent redirect loops');
    // DO NOTHING - navigation after preferences is handled directly by the preferences screen
    return;
  }
  
  /**
   * Navigate to login screen
   */
  navigateToLogin(): void {
    // Mark welcome step as completed
    this.updateOnboardingStepStatus('welcome', true);
    
    router.push('/login');
  }
  
  /**
   * Navigate to welcome screen
   */
  navigateToWelcome(): void {
    router.replace('/');
  }
  
  /**
   * Skip onboarding and go to dashboard
   */
  skipOnboarding(): void {
    // Mark all onboarding steps as completed
    this.onboardingSteps.forEach(step => {
      this.updateOnboardingStepStatus(step.id, true);
    });
    
    // Navigate to dashboard
    router.replace('/dashboard');
  }
  
  /**
   * Check if onboarding is complete
   */
  isOnboardingComplete(): boolean {
    return this.onboardingSteps.every(step => step.isCompleted);
  }
}

// Export singleton instance
export const navigationService = new NavigationService();
export default navigationService;