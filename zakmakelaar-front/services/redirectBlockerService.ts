/**
 * Simple service to block all redirects during preferences completion
 */

class RedirectBlockerService {
  private isBlocked = false;
  private blockStartTime = 0;
  private readonly BLOCK_DURATION = 30000; // 30 seconds

  /**
   * Block all redirects for a specified duration
   */
  blockRedirects(durationMs: number = this.BLOCK_DURATION): void {
    console.log('🚫 BLOCKING ALL REDIRECTS for', durationMs, 'ms');
    this.isBlocked = true;
    this.blockStartTime = Date.now();
    
    // Auto-unblock after duration
    setTimeout(() => {
      this.unblockRedirects();
    }, durationMs);
  }

  /**
   * Unblock redirects
   */
  unblockRedirects(): void {
    console.log('✅ UNBLOCKING redirects');
    this.isBlocked = false;
    this.blockStartTime = 0;
  }

  /**
   * Check if redirects are currently blocked
   */
  areRedirectsBlocked(): boolean {
    // Auto-expire if block duration has passed
    if (this.isBlocked && Date.now() - this.blockStartTime > this.BLOCK_DURATION) {
      this.unblockRedirects();
    }
    
    return this.isBlocked;
  }

  /**
   * Get remaining block time in milliseconds
   */
  getRemainingBlockTime(): number {
    if (!this.isBlocked) return 0;
    const elapsed = Date.now() - this.blockStartTime;
    return Math.max(0, this.BLOCK_DURATION - elapsed);
  }
}

// Export singleton instance
export const redirectBlockerService = new RedirectBlockerService();
export default redirectBlockerService;