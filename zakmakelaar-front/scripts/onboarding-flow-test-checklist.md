# Onboarding Flow Test Checklist

## Test Environment Setup
- [ ] Ensure the app is running in development mode
- [ ] Clear any stored authentication tokens and preferences
- [ ] Have multiple test accounts ready (new user, user with incomplete preferences, user with complete preferences)

## Scenario 1: New User Flow
- [ ] Register a new user account
- [ ] Verify user is redirected to preferences wizard after successful registration
- [ ] Complete each step of the preferences wizard:
  - [ ] Profile type selection
  - [ ] Location selection
  - [ ] Budget setting
  - [ ] Property type selection
  - [ ] Amenities selection
  - [ ] Notification preferences
  - [ ] Summary review
- [ ] Click "Complete & Save" on the final step
- [ ] Verify user is redirected to dashboard
- [ ] Check console logs for successful navigation and preference validation

## Scenario 2: Incomplete Preferences Flow
- [ ] Log in with a user that has incomplete preferences
- [ ] Verify user is redirected to preferences wizard
- [ ] Try to navigate away from preferences wizard before completion
- [ ] Verify exit confirmation dialog appears
- [ ] Cancel exit and continue with preferences setup
- [ ] Complete preferences and verify redirection to dashboard
- [ ] Check console logs for validation events

## Scenario 3: Complete Preferences Flow
- [ ] Log in with a user that has complete preferences
- [ ] Verify user is redirected directly to dashboard without seeing preferences wizard
- [ ] Navigate to preferences from dashboard menu
- [ ] Make changes to preferences
- [ ] Save changes and verify they are persisted
- [ ] Check console logs for preference updates

## Scenario 4: Direct Dashboard Access
- [ ] Clear authentication
- [ ] Log in with a user that has incomplete preferences
- [ ] Try to navigate directly to dashboard URL
- [ ] Verify redirection to preferences wizard
- [ ] Complete preferences
- [ ] Try to navigate directly to dashboard URL again
- [ ] Verify successful access to dashboard
- [ ] Check console logs for navigation guards

## Edge Cases
- [ ] Test with partially completed preferences
- [ ] Test with invalid preference values
- [ ] Test with network errors during preference saving
- [ ] Test back button behavior on different steps
- [ ] Test app restart during preferences setup

## Results
- [ ] All scenarios pass without issues
- [ ] Users cannot access dashboard without completing preferences
- [ ] Navigation flow is smooth and intuitive
- [ ] Error handling is robust and user-friendly
- [ ] Console logs provide clear information about the flow