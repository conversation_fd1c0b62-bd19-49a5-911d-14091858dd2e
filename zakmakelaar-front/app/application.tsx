import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
  Modal,
  Switch,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { useAIStore } from "../store/aiStore";
import { useAuthStore } from "../store/authStore";
import { Listing, listingsService } from "../services/listingsService";
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  FadeInUp,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Enhanced Header Component
const Header = ({
  showBackButton = false,
  onBack,
}: {
  showBackButton?: boolean;
  onBack?: () => void;
}) => {
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        {showBackButton && (
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              onBack?.();
            }}
            style={styles.backButton}
            activeOpacity={0.8}
          >
            <View style={styles.backButtonInner}>
              <Ionicons name="chevron-back" size={24} color={THEME.primary} />
            </View>
          </TouchableOpacity>
        )}

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <Text style={styles.headerTitle}>Application Generator</Text>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );
};

// Application Style Selection Component
const StyleSelector = ({
  selectedStyle,
  onStyleChange,
}: {
  selectedStyle: 'professional' | 'personal' | 'creative' | 'student' | 'expat';
  onStyleChange: (style: 'professional' | 'personal' | 'creative' | 'student' | 'expat') => void;
}) => {
  const styles_local = {
    professional: {
      title: 'Professional',
      description: 'Formal, business-like tone with emphasis on credentials and work experience',
      icon: 'briefcase-outline' as const,
      color: '#2563eb',
    },
    personal: {
      title: 'Personal',
      description: 'Warm, friendly approach highlighting personal qualities and lifestyle',
      icon: 'heart-outline' as const,
      color: '#dc2626',
    },
    creative: {
      title: 'Creative',
      description: 'Unique, engaging style that stands out from the crowd',
      icon: 'bulb-outline' as const,
      color: '#7c3aed',
    },
    student: {
      title: 'Student',
      description: 'Tailored for students with focus on studies, budget, and reliability',
      icon: 'school-outline' as const,
      color: '#059669',
    },
    expat: {
      title: 'Expat',
      description: 'Perfect for international professionals new to the Netherlands',
      icon: 'globe-outline' as const,
      color: '#ea580c',
    },
  };

  return (
    <View style={styles.styleSelectorContainer}>
      <Text style={styles.sectionTitle}>Application Style</Text>
      <Text style={styles.sectionSubtitle}>Choose the tone that best represents you</Text>

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.styleOptionsContainer}
      >
        {Object.entries(styles_local).map(([key, style]) => (
          <TouchableOpacity
            key={key}
            style={[
              styles.styleOption,
              selectedStyle === key && { borderColor: style.color, backgroundColor: `${style.color}15` },
            ]}
            onPress={() => onStyleChange(key as 'professional' | 'personal' | 'creative' | 'student' | 'expat')}
          >
            <View style={[styles.styleIcon, { backgroundColor: style.color }]}>
              <Ionicons name={style.icon} size={20} color="white" />
            </View>
            <Text style={[styles.styleTitle, selectedStyle === key && { color: style.color }]}>
              {style.title}
            </Text>
            <Text style={styles.styleDescription}>{style.description}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

// Enhanced Progress Indicator Component with estimated time
const ProgressIndicator = ({
  progress,
  estimatedTimeRemaining
}: {
  progress: number;
  estimatedTimeRemaining?: number;
}) => {
  const getProgressStage = (progress: number) => {
    if (progress < 20) return "Analyzing property details...";
    if (progress < 40) return "Understanding your preferences...";
    if (progress < 60) return "Crafting personalized content...";
    if (progress < 80) return "Optimizing application style...";
    if (progress < 95) return "Final review and polish...";
    return "Almost ready!";
  };

  return (
    <View style={styles.progressContainer}>
      <View style={styles.progressHeader}>
        <Text style={styles.progressStage}>{getProgressStage(progress)}</Text>
        {estimatedTimeRemaining && (
          <Text style={styles.estimatedTime}>
            ~{estimatedTimeRemaining}s remaining
          </Text>
        )}
      </View>
      <View style={styles.progressBar}>
        <View style={[styles.progressFill, { width: `${progress}%` }]} />
      </View>
      <Text style={styles.progressText}>{Math.round(progress)}% Complete</Text>
    </View>
  );
};
// Enhanced Autonomous Settings Modal Component
const AutonomousSettingsModal = ({
  visible,
  onClose,
  settings,
  onSettingsChange,
}: {
  visible: boolean;
  onClose: () => void;
  settings: any;
  onSettingsChange: (settings: any) => void;
}) => {
  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Autonomous Mode Configuration</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#666" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          {/* Autonomous Mode Explanation */}
          <View style={styles.explanationSection}>
            <View style={styles.explanationHeader}>
              <Ionicons name="information-circle" size={24} color="#3b82f6" />
              <Text style={styles.explanationTitle}>How Autonomous Mode Works</Text>
            </View>
            <Text style={styles.explanationText}>
              When enabled, our AI will automatically submit applications on your behalf when optimal conditions are met.
              You maintain full control with safety limits and can pause anytime.
            </Text>
          </View>

          {/* Main Settings */}
          <View style={styles.settingItem}>
            <View style={styles.settingHeader}>
              <Text style={styles.settingLabel}>Enable Autonomous Submission</Text>
              <Switch
                value={settings.enabled}
                onValueChange={(value) => onSettingsChange({ ...settings, enabled: value })}
                trackColor={{ false: '#e5e7eb', true: '#f72585' }}
                thumbColor={settings.enabled ? '#ffffff' : '#f4f3f4'}
              />
            </View>
            <Text style={styles.settingDescription}>
              Automatically submit applications that meet your criteria and safety limits
            </Text>
          </View>

          {/* Criteria Settings */}
          <View style={styles.settingsGroup}>
            <Text style={styles.settingsGroupTitle}>Application Criteria</Text>

            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Minimum Match Score</Text>
              <Text style={styles.settingDescription}>
                Only auto-apply to properties with AI match score above: {settings.autoApplyMinMatchScore}%
              </Text>
              <View style={styles.sliderContainer}>
                <Text style={styles.sliderValue}>{settings.autoApplyMinMatchScore}%</Text>
                <Text style={styles.sliderNote}>Higher scores = better matches</Text>
              </View>
            </View>

            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Maximum Budget Override</Text>
              <Text style={styles.settingDescription}>
                Allow applications up to {settings.maxBudgetOverride}% above your preferred budget
              </Text>
              <View style={styles.sliderContainer}>
                <Text style={styles.sliderValue}>{settings.maxBudgetOverride}%</Text>
              </View>
            </View>
          </View>

          {/* Safety Limits */}
          <View style={styles.settingsGroup}>
            <Text style={styles.settingsGroupTitle}>Safety Limits</Text>

            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Daily Application Limit</Text>
              <Text style={styles.settingDescription}>
                Maximum applications per day: {settings.maxApplicationsPerDay}
              </Text>
              <View style={styles.sliderContainer}>
                <Text style={styles.sliderValue}>{settings.maxApplicationsPerDay}</Text>
                <Text style={styles.sliderNote}>Prevents spam and maintains quality</Text>
              </View>
            </View>

            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Weekly Application Limit</Text>
              <Text style={styles.settingDescription}>
                Maximum applications per week: {settings.maxApplicationsPerWeek}
              </Text>
              <View style={styles.sliderContainer}>
                <Text style={styles.sliderValue}>{settings.maxApplicationsPerWeek}</Text>
              </View>
            </View>
          </View>

          {/* Confirmation Settings */}
          <View style={styles.settingsGroup}>
            <Text style={styles.settingsGroupTitle}>Confirmation Requirements</Text>

            <View style={styles.settingItem}>
              <View style={styles.settingHeader}>
                <Text style={styles.settingLabel}>Confirm Expensive Properties</Text>
                <Switch
                  value={settings.requireConfirmationForExpensive}
                  onValueChange={(value) => onSettingsChange({ ...settings, requireConfirmationForExpensive: value })}
                  trackColor={{ false: '#e5e7eb', true: '#f72585' }}
                  thumbColor={settings.requireConfirmationForExpensive ? '#ffffff' : '#f4f3f4'}
                />
              </View>
              <Text style={styles.settingDescription}>
                Ask for approval before applying to properties significantly above your budget
              </Text>
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingHeader}>
                <Text style={styles.settingLabel}>Pause on Multiple Rejections</Text>
                <Switch
                  value={settings.pauseOnMultipleRejections}
                  onValueChange={(value) => onSettingsChange({ ...settings, pauseOnMultipleRejections: value })}
                  trackColor={{ false: '#e5e7eb', true: '#f72585' }}
                  thumbColor={settings.pauseOnMultipleRejections ? '#ffffff' : '#f4f3f4'}
                />
              </View>
              <Text style={styles.settingDescription}>
                Automatically pause autonomous mode after 3 consecutive rejections to review strategy
              </Text>
            </View>
          </View>

          {/* Notification Settings */}
          <View style={styles.settingsGroup}>
            <Text style={styles.settingsGroupTitle}>Notifications</Text>

            <View style={styles.settingItem}>
              <View style={styles.settingHeader}>
                <Text style={styles.settingLabel}>Notify on Application</Text>
                <Switch
                  value={settings.notifyOnApplication}
                  onValueChange={(value) => onSettingsChange({ ...settings, notifyOnApplication: value })}
                  trackColor={{ false: '#e5e7eb', true: '#f72585' }}
                  thumbColor={settings.notifyOnApplication ? '#ffffff' : '#f4f3f4'}
                />
              </View>
              <Text style={styles.settingDescription}>
                Get immediate notifications when applications are submitted
              </Text>
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingHeader}>
                <Text style={styles.settingLabel}>Daily Summary</Text>
                <Switch
                  value={settings.dailySummary}
                  onValueChange={(value) => onSettingsChange({ ...settings, dailySummary: value })}
                  trackColor={{ false: '#e5e7eb', true: '#f72585' }}
                  thumbColor={settings.dailySummary ? '#ffffff' : '#f4f3f4'}
                />
              </View>
              <Text style={styles.settingDescription}>
                Receive daily summaries of autonomous activity and results
              </Text>
            </View>
          </View>
        </ScrollView>

        <View style={styles.modalFooter}>
          <TouchableOpacity style={styles.saveButton} onPress={onClose}>
            <Ionicons name="checkmark-circle" size={20} color="white" />
            <Text style={styles.saveButtonText}>Save Configuration</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};
// Helper function to format price
const formatPrice = (price: number | string | undefined): string => {
  if (!price) return '0';
  const numPrice = typeof price === 'string' ? parseInt(price.replace(/[^\d]/g, '')) : price;
  return numPrice.toLocaleString();
};

// Helper function to format number fields
const formatNumber = (value: number | string | undefined): number => {
  if (!value) return 0;
  return typeof value === 'string' ? parseInt(value) || 0 : value;
};

export default function ApplicationScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const { user } = useAuthStore();
  const {
    generateApplication,
    approveApplication,
    submitApplication,
    applicationInProgress,
    applicationError,
    autonomousSettings,
    autonomousStatus,
    updateAutonomousSettings,
  } = useAIStore();

  // State management
  const [selectedStyle, setSelectedStyle] = useState<'professional' | 'personal' | 'creative' | 'student' | 'expat'>('professional');
  const [customMessage, setCustomMessage] = useState('');
  const [currentApplication, setCurrentApplication] = useState<any>(null);
  const [editedContent, setEditedContent] = useState('');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [showAutonomousSettings, setShowAutonomousSettings] = useState(false);
  const [submissionMode, setSubmissionMode] = useState<'manual' | 'autonomous'>('manual');
  const [generationProgress, setGenerationProgress] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState<number | undefined>(undefined);
  const [propertyContext, setPropertyContext] = useState<any>(null);

  // Real listing data state
  const [currentListing, setCurrentListing] = useState<Listing | null>(null);
  const [listingLoading, setListingLoading] = useState(true);
  const [listingError, setListingError] = useState<string | null>(null);

  // Fetch real listing data from backend
  useEffect(() => {
    const fetchListing = async () => {
      const listingId = params.listingId as string;

      if (!listingId) {
        setListingError('No listing ID provided');
        setListingLoading(false);
        return;
      }

      try {
        setListingLoading(true);
        setListingError(null);

        console.log('📡 Fetching listing with ID:', listingId);
        const response = await listingsService.getListing(listingId);

        if (response.success && response.data) {
          console.log('✅ Listing fetched successfully:', response.data.title);
          setCurrentListing(response.data);
        } else {
          console.log('❌ Failed to fetch listing:', response.message);
          setListingError(response.message || 'Failed to fetch listing');
        }
      } catch (error: any) {
        console.error('❌ Error fetching listing:', error);
        setListingError(error.message || 'Failed to fetch listing');
      } finally {
        setListingLoading(false);
      }
    };

    fetchListing();
  }, [params.listingId]);

  useEffect(() => {
    // Enhanced progress simulation with realistic timing and estimated completion
    if (applicationInProgress) {
      const totalEstimatedTime = 8; // 8 seconds total
      const startTime = Date.now();

      const interval = setInterval(() => {
        const elapsed = (Date.now() - startTime) / 1000;
        const newProgress = Math.min((elapsed / totalEstimatedTime) * 100, 95);
        const remaining = Math.max(totalEstimatedTime - elapsed, 0);

        setGenerationProgress(newProgress);
        setEstimatedTimeRemaining(Math.ceil(remaining));

        if (newProgress >= 95) {
          clearInterval(interval);
          setEstimatedTimeRemaining(0);
        }
      }, 100);

      return () => clearInterval(interval);
    } else {
      setGenerationProgress(0);
      setEstimatedTimeRemaining(undefined);
    }
  }, [applicationInProgress]);

  // Load property context when listing is available
  useEffect(() => {
    const loadPropertyContext = async () => {
      if (!currentListing) return;

      const context = {
        listing: currentListing,
        marketData: {
          averagePrice: 1650,
          competitionLevel: 'high',
          responseRate: '78%',
          averageResponseTime: '2.3 days'
        },
        userMatchScore: 87,
        similarApplications: 12
      };
      setPropertyContext(context);
    };

    loadPropertyContext();
  }, [currentListing]);

  const handleGenerateApplication = async () => {
    if (!user) {
      Alert.alert('Error', 'Please log in to generate applications');
      return;
    }

    if (!currentListing) {
      Alert.alert('Error', 'Listing data not available. Please try again.');
      return;
    }

    try {
      setGenerationProgress(10);
      const application = await generateApplication(
        currentListing,
        user,
        selectedStyle,
        customMessage
      );
      setCurrentApplication(application);
      setEditedContent(application.content.message);
      setIsPreviewMode(true);
      setGenerationProgress(100);
    } catch (error) {
      console.error('Error generating application:', error);
      Alert.alert('Error', 'Failed to generate application. Please try again.');
    }
  };

  const handleApproveApplication = async () => {
    if (!currentApplication) return;

    try {
      await approveApplication(currentApplication.id);
      Alert.alert('Success', 'Application approved and ready for submission!');
    } catch (error) {
      console.error('Error approving application:', error);
      Alert.alert('Error', 'Failed to approve application.');
    }
  };

  const handleSubmitApplication = async () => {
    if (!currentApplication) return;

    try {
      const success = await submitApplication(currentApplication.id, submissionMode);

      if (success) {
        if (submissionMode === 'autonomous') {
          Alert.alert(
            'Autonomous Submission Activated',
            `Your application is now queued for automatic submission. Our AI will submit it when optimal conditions are detected.\n\n• Expected submission: Within 2-6 hours\n• You'll receive immediate notification\n• You can pause anytime from the dashboard`,
            [
              { text: 'View Dashboard', onPress: () => router.replace('/dashboard') },
              { text: 'OK', onPress: () => router.back() }
            ]
          );
        } else {
          Alert.alert(
            'Application Submitted Successfully!',
            `Your application has been sent to the landlord.\n\n• Submission time: ${new Date().toLocaleTimeString()}\n• Expected response: 1-3 business days\n• We'll notify you of any updates`,
            [
              { text: 'Track Status', onPress: () => router.replace('/dashboard') },
              { text: 'OK', onPress: () => router.back() }
            ]
          );
        }
      } else {
        throw new Error('Submission failed');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      Alert.alert(
        'Submission Failed',
        'There was an issue submitting your application. Please check your connection and try again.',
        [
          { text: 'Retry', onPress: handleSubmitApplication },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    }
  };
  const renderGenerationInterface = () => (
    <Animated.View
      style={styles.generationContainer}
      entering={FadeInUp.duration(600)}
    >
      <Animated.Text
        style={styles.applicationTitle}
        entering={FadeInUp.duration(800).delay(200)}
      >
        AI Application Generator
      </Animated.Text>
      <Animated.Text
        style={styles.applicationSubtitle}
        entering={FadeInUp.duration(800).delay(400)}
      >
        Create a personalized application letter for this property
      </Animated.Text>

      <Animated.View
        style={styles.propertyInfo}
        entering={FadeInUp.duration(800).delay(600)}
      >
        <View style={styles.propertyHeader}>
          <Text style={styles.propertyTitle}>{currentListing?.title || 'Loading...'}</Text>
          {propertyContext?.userMatchScore && (
            <View style={styles.matchScoreBadge}>
              <Text style={styles.matchScoreText}>{propertyContext.userMatchScore}% Match</Text>
            </View>
          )}
        </View>
        <Text style={styles.propertyDetails}>
          €{currentListing ? formatPrice(currentListing.price) : '0'}/month • {formatNumber(currentListing?.bedrooms)} bed • {formatNumber(currentListing?.area)}m²
        </Text>
        <Text style={styles.propertyLocation}>
          {currentListing ? (
            typeof currentListing.location === 'string'
              ? currentListing.location
              : `${currentListing.location?.city || ''}, ${currentListing.location?.postalCode || ''}`.trim().replace(/,$/, '')
          ) : 'Loading...'}
        </Text>

        {propertyContext?.marketData && (
          <View style={styles.marketContext}>
            <Text style={styles.marketContextTitle}>Market Context</Text>
            <View style={styles.marketStats}>
              <View style={styles.marketStat}>
                <Text style={styles.marketStatLabel}>Avg. Price</Text>
                <Text style={styles.marketStatValue}>€{propertyContext.marketData.averagePrice}</Text>
              </View>
              <View style={styles.marketStat}>
                <Text style={styles.marketStatLabel}>Competition</Text>
                <Text style={[
                  styles.marketStatValue,
                  { color: propertyContext.marketData.competitionLevel === 'high' ? '#ef4444' : '#10b981' }
                ]}>
                  {propertyContext.marketData.competitionLevel}
                </Text>
              </View>
              <View style={styles.marketStat}>
                <Text style={styles.marketStatLabel}>Response Rate</Text>
                <Text style={styles.marketStatValue}>{propertyContext.marketData.responseRate}</Text>
              </View>
            </View>
          </View>
        )}
      </Animated.View>

      <Animated.View entering={FadeInUp.duration(800).delay(800)}>
        <StyleSelector
          selectedStyle={selectedStyle}
          onStyleChange={setSelectedStyle}
        />
      </Animated.View>

      <Animated.View
        style={styles.customMessageContainer}
        entering={FadeInUp.duration(800).delay(1000)}
      >
        <Text style={styles.sectionTitle}>Personal Message (Optional)</Text>
        <Text style={styles.sectionSubtitle}>
          Add any specific details you'd like to highlight
        </Text>
        <TextInput
          style={styles.customMessageInput}
          placeholder="e.g., I have a stable job at a tech company, I am looking for a long-term rental..."
          value={customMessage}
          onChangeText={setCustomMessage}
          multiline
          numberOfLines={3}
          textAlignVertical="top"
        />
      </Animated.View>

      {applicationInProgress && (
        <Animated.View
          style={styles.progressSection}
          entering={FadeInUp.duration(600)}
        >
          <Text style={styles.progressTitle}>Generating Your Application...</Text>
          <ProgressIndicator
            progress={generationProgress}
            estimatedTimeRemaining={estimatedTimeRemaining}
          />
          <Text style={styles.progressDescription}>
            Our AI is analyzing the property, your profile, and market conditions to craft the perfect application
          </Text>

          {propertyContext?.similarApplications && (
            <View style={styles.aiInsights}>
              <Ionicons name="bulb-outline" size={16} color="#f59e0b" />
              <Text style={styles.aiInsightsText}>
                Analyzing {propertyContext.similarApplications} similar applications to optimize your chances
              </Text>
            </View>
          )}
        </Animated.View>
      )}

      <Animated.View entering={FadeInUp.duration(800).delay(1200)}>
        <TouchableOpacity
          style={[
            styles.generateButton,
            (applicationInProgress || !currentListing) && styles.disabledButton,
          ]}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            handleGenerateApplication();
          }}
          disabled={applicationInProgress || !currentListing}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={[THEME.accent, THEME.secondary]}
            style={[styles.generateButton, { marginBottom: 0 }]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {applicationInProgress ? (
              <Animated.View
                style={styles.loadingContainer}
                entering={FadeInUp.duration(300)}
              >
                <ActivityIndicator size="small" color="#ffffff" />
                <Text style={[styles.generateButtonText, { marginLeft: 12 }]}>Generating...</Text>
              </Animated.View>
            ) : !currentListing ? (
              <Animated.View
                style={styles.loadingContainer}
                entering={FadeInUp.duration(300)}
              >
                <Ionicons name="time-outline" size={22} color="white" />
                <Text style={[styles.generateButtonText, { marginLeft: 12 }]}>Loading Property...</Text>
              </Animated.View>
            ) : (
              <Animated.View
                style={styles.loadingContainer}
                entering={FadeInUp.duration(300)}
              >
                <Ionicons name="sparkles" size={22} color="white" />
                <Text style={[styles.generateButtonText, { marginLeft: 12 }]}>Generate Application</Text>
              </Animated.View>
            )}
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>

      {applicationError && (
        <Animated.Text
          style={styles.errorText}
          entering={FadeInUp.duration(400)}
        >
          {applicationError}
        </Animated.Text>
      )}
    </Animated.View>
  );
  const renderPreviewInterface = () => (
    <Animated.View
      style={styles.previewContainer}
      entering={FadeInUp.duration(600)}
    >
      <Animated.View
        style={styles.previewHeader}
        entering={FadeInUp.duration(800).delay(200)}
      >
        <TouchableOpacity
          style={styles.backToGenerateButton}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            setIsPreviewMode(false);
          }}
          activeOpacity={0.8}
        >
          <Ionicons name="arrow-back" size={20} color={THEME.accent} />
          <Text style={styles.backToGenerateText}>Back to Generate</Text>
        </TouchableOpacity>

        <Text style={styles.previewTitle}>Application Preview & Edit</Text>
      </Animated.View>

      <Animated.View
        style={styles.applicationPreview}
        entering={FadeInUp.duration(800).delay(400)}
      >
        <View style={styles.previewMeta}>
          <View style={styles.metaRow}>
            <Text style={styles.metaLabel}>Subject:</Text>
            <TextInput
              style={styles.editableSubject}
              value={currentApplication?.content.subject || ''}
              onChangeText={(text) => {
                if (currentApplication) {
                  setCurrentApplication({
                    ...currentApplication,
                    content: { ...currentApplication.content, subject: text }
                  });
                }
              }}
              placeholder="Email subject line"
            />
          </View>
          <View style={styles.metaRow}>
            <Text style={styles.metaLabel}>Template:</Text>
            <Text style={styles.previewTemplate}>
              {currentApplication?.content.template}
            </Text>
            <TouchableOpacity
              style={styles.changeTemplateButton}
              onPress={() => setIsPreviewMode(false)}
            >
              <Text style={styles.changeTemplateText}>Change</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.editingSection}>
          <View style={styles.editingHeader}>
            <Text style={styles.editingSectionTitle}>Application Content</Text>
            <View style={styles.editingControls}>
              <TouchableOpacity style={styles.editingControl}>
                <Ionicons name="text-outline" size={16} color="#6b7280" />
                <Text style={styles.editingControlText}>Format</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.editingControl}>
                <Ionicons name="refresh-outline" size={16} color="#6b7280" />
                <Text style={styles.editingControlText}>Regenerate</Text>
              </TouchableOpacity>
            </View>
          </View>

          <TextInput
            style={styles.editableContent}
            value={editedContent}
            onChangeText={setEditedContent}
            multiline
            textAlignVertical="top"
            placeholder="Application content will appear here..."
          />

          <View style={styles.personalizationOptions}>
            <Text style={styles.personalizationTitle}>Quick Personalizations</Text>
            <View style={styles.personalizationTags}>
              {['Add urgency', 'Mention flexibility', 'Highlight stability', 'Add personal touch'].map((tag, index) => (
                <TouchableOpacity key={index} style={styles.personalizationTag}>
                  <Text style={styles.personalizationTagText}>{tag}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {currentApplication?.content.tips && (
          <View style={styles.tipsContainer}>
            <Text style={styles.tipsTitle}>💡 AI Tips for Success:</Text>
            {currentApplication.content.tips.map((tip: string, index: number) => (
              <Text key={index} style={styles.tipItem}>• {tip}</Text>
            ))}
          </View>
        )}

        <View style={styles.approvalWorkflow}>
          <Text style={styles.approvalTitle}>Application Review Checklist</Text>
          <View style={styles.checklistItems}>
            {[
              'Personal information is accurate',
              'Property details are mentioned correctly',
              'Tone matches your selected style',
              'Contact information is included',
              'Application highlights your strengths'
            ].map((item, index) => (
              <View key={index} style={styles.checklistItem}>
                <Ionicons name="checkmark-circle" size={20} color="#10b981" />
                <Text style={styles.checklistItemText}>{item}</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.submissionControls}>
          <Text style={styles.sectionTitle}>Submission Method</Text>

          <View style={styles.submissionOptions}>
            <TouchableOpacity
              style={[
                styles.submissionOption,
                submissionMode === 'manual' && styles.selectedSubmissionOption,
              ]}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setSubmissionMode('manual');
              }}
              activeOpacity={0.8}
            >
              <Ionicons
                name="hand-left-outline"
                size={28}
                color={submissionMode === 'manual' ? THEME.accent : '#64748b'}
              />
              <Text style={[
                styles.submissionOptionTitle,
                submissionMode === 'manual' && styles.selectedSubmissionOptionTitle,
              ]}>Manual Submission</Text>
              <Text style={styles.submissionOptionDescription}>
                Review and submit immediately
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.submissionOption,
                submissionMode === 'autonomous' && styles.selectedSubmissionOption,
              ]}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setSubmissionMode('autonomous');
              }}
              activeOpacity={0.8}
            >
              <Ionicons
                name="flash-outline"
                size={28}
                color={submissionMode === 'autonomous' ? THEME.accent : '#64748b'}
              />
              <Text style={[
                styles.submissionOptionTitle,
                submissionMode === 'autonomous' && styles.selectedSubmissionOptionTitle,
              ]}>Autonomous Submission</Text>
              <Text style={styles.submissionOptionDescription}>
                AI submits at optimal timing
              </Text>
            </TouchableOpacity>
          </View>

          {submissionMode === 'autonomous' && (
            <View style={styles.autonomousInfo}>
              <View style={styles.autonomousHeader}>
                <View style={styles.autonomousStatus}>
                  <Ionicons
                    name={autonomousSettings.enabled ? "flash" : "flash-off"}
                    size={16}
                    color={autonomousSettings.enabled ? "#10b981" : "#ef4444"}
                  />
                  <Text style={[
                    styles.autonomousStatusText,
                    { color: autonomousSettings.enabled ? "#10b981" : "#ef4444" }
                  ]}>
                    Autonomous mode: {autonomousSettings.enabled ? 'Active' : 'Inactive'}
                  </Text>
                </View>

                <TouchableOpacity
                  style={styles.settingsButton}
                  onPress={() => setShowAutonomousSettings(true)}
                >
                  <Ionicons name="settings-outline" size={16} color="white" />
                  <Text style={styles.settingsButtonText}>Configure</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.autonomousExplanation}>
                <Text style={styles.autonomousTitle}>How Autonomous Submission Works:</Text>
                <View style={styles.autonomousSteps}>
                  <View style={styles.autonomousStep}>
                    <View style={styles.stepNumber}>
                      <Text style={styles.stepNumberText}>1</Text>
                    </View>
                    <Text style={styles.stepText}>AI monitors market conditions and competition levels</Text>
                  </View>
                  <View style={styles.autonomousStep}>
                    <View style={styles.stepNumber}>
                      <Text style={styles.stepNumberText}>2</Text>
                    </View>
                    <Text style={styles.stepText}>Submits when optimal timing is detected (usually within 2-6 hours)</Text>
                  </View>
                  <View style={styles.autonomousStep}>
                    <View style={styles.stepNumber}>
                      <Text style={styles.stepNumberText}>3</Text>
                    </View>
                    <Text style={styles.stepText}>Sends immediate notification with submission details</Text>
                  </View>
                </View>
              </View>

              <View style={styles.autonomousStats}>
                <Text style={styles.autonomousStatsTitle}>Current Status</Text>
                <View style={styles.statsRow}>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{autonomousSettings.maxApplicationsPerDay - (autonomousStatus?.applicationsToday || 0)}</Text>
                    <Text style={styles.statLabel}>Applications left today</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>{autonomousSettings.autoApplyMinMatchScore}%</Text>
                    <Text style={styles.statLabel}>Min. match score</Text>
                  </View>
                  <View style={styles.statItem}>
                    <Text style={styles.statValue}>
                      {autonomousStatus?.isActive ? 'Active' : 'Paused'}
                    </Text>
                    <Text style={styles.statLabel}>Current status</Text>
                  </View>
                </View>
              </View>

              {!autonomousSettings.enabled && (
                <View style={styles.warningBox}>
                  <Ionicons name="warning-outline" size={16} color="#f59e0b" />
                  <Text style={styles.warningText}>
                    Autonomous mode is disabled. Enable it in settings to use automatic submission.
                  </Text>
                </View>
              )}
            </View>
          )}
        </View>
      </Animated.View>

      <Animated.View
        style={styles.actionButtons}
        entering={FadeInUp.duration(800).delay(600)}
      >
        <TouchableOpacity
          style={styles.approveButton}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            handleApproveApplication();
          }}
          activeOpacity={0.8}
        >
          <Ionicons name="checkmark-circle-outline" size={22} color="white" />
          <Text style={styles.approveButtonText}>Approve Application</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.submitButton}
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
            handleSubmitApplication();
          }}
          activeOpacity={0.8}
        >
          <Ionicons
            name={submissionMode === 'autonomous' ? 'flash' : 'send'}
            size={22}
            color="white"
          />
          <Text style={styles.submitButtonText}>
            {submissionMode === 'autonomous' ? 'Enable Auto-Submit' : 'Submit Now'}
          </Text>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
  // Show loading state while fetching listing
  if (listingLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          showBackButton={true}
          onBack={() => router.back()}
        />
        <View style={styles.listingLoadingContainer}>
          <ActivityIndicator size="large" color={THEME.primary} />
          <Text style={styles.listingLoadingText}>Loading property details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state if listing failed to load
  if (listingError || !currentListing) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          showBackButton={true}
          onBack={() => router.back()}
        />
        <View style={styles.listingErrorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color={THEME.danger} />
          <Text style={styles.listingErrorTitle}>Property not found</Text>
          <Text style={styles.errorText}>
            {listingError || "The property you're looking for could not be found."}
          </Text>
          <TouchableOpacity
            style={styles.listingErrorButton}
            onPress={() => router.back()}
          >
            <Text style={styles.listingErrorButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header
        showBackButton={true}
        onBack={() => router.back()}
      />

      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {!isPreviewMode ? renderGenerationInterface() : renderPreviewInterface()}
      </ScrollView>

      <AutonomousSettingsModal
        visible={showAutonomousSettings}
        onClose={() => setShowAutonomousSettings(false)}
        settings={autonomousSettings}
        onSettingsChange={updateAutonomousSettings}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 4,
  },
  backButtonInner: {
    width: 44,
    height: 44,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  logoContainer: {
    width: 44,
    height: 44,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  logoText: {
    fontSize: 18,
    fontWeight: '900',
    color: THEME.primary,
    letterSpacing: -0.5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '800',
    color: THEME.light,
    letterSpacing: -0.5,
  },
  headerSpacer: {
    width: 44,
  },
  scrollContainer: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },

  // Generation Interface Styles
  generationContainer: {
    flex: 1,
  },
  applicationTitle: {
    fontSize: 28,
    fontWeight: "800",
    color: "#0f172a",
    marginBottom: 8,
    marginTop: 16,
    letterSpacing: -0.5,
  },
  applicationSubtitle: {
    fontSize: 17,
    color: "#64748b",
    marginBottom: 32,
    lineHeight: 24,
    fontWeight: '500',
  },

  // Property Info Styles
  propertyInfo: {
    backgroundColor: "#ffffff",
    padding: 24,
    borderRadius: 20,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: "rgba(148, 163, 184, 0.1)",
  },
  propertyHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 12,
  },
  propertyTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#0f172a",
    flex: 1,
    marginRight: 12,
    lineHeight: 26,
    letterSpacing: -0.3,
  },
  matchScoreBadge: {
    backgroundColor: THEME.success,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    shadowColor: THEME.success,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  matchScoreText: {
    fontSize: 13,
    fontWeight: "700",
    color: "#ffffff",
    letterSpacing: 0.2,
  },
  propertyDetails: {
    fontSize: 16,
    color: "#64748b",
    marginBottom: 6,
    fontWeight: '500',
  },
  propertyLocation: {
    fontSize: 16,
    color: THEME.accent,
    fontWeight: "600",
    marginBottom: 16,
  },
  marketContext: {
    borderTopWidth: 1,
    borderTopColor: "rgba(148, 163, 184, 0.2)",
    paddingTop: 16,
    marginTop: 4,
  },
  marketContextTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "#0f172a",
    marginBottom: 12,
    letterSpacing: -0.2,
  },
  marketStats: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  marketStat: {
    alignItems: "center",
    flex: 1,
  },
  marketStatLabel: {
    fontSize: 13,
    color: "#64748b",
    marginBottom: 4,
    fontWeight: '500',
  },
  marketStatValue: {
    fontSize: 16,
    fontWeight: "700",
    color: "#0f172a",
    letterSpacing: -0.2,
  },

  // Style Selector Styles
  styleSelectorContainer: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "700",
    color: "#0f172a",
    marginBottom: 6,
    letterSpacing: -0.3,
  },
  sectionSubtitle: {
    fontSize: 16,
    color: "#64748b",
    marginBottom: 20,
    fontWeight: '500',
  },
  styleOptionsContainer: {
    paddingHorizontal: 4,
    gap: 16,
  },
  styleOption: {
    width: 150,
    backgroundColor: "#ffffff",
    padding: 20,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: "rgba(148, 163, 184, 0.2)",
    alignItems: "center",
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  styleIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  styleTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "#0f172a",
    marginBottom: 6,
    textAlign: "center",
    letterSpacing: -0.2,
  },
  styleDescription: {
    fontSize: 13,
    color: "#64748b",
    textAlign: "center",
    lineHeight: 18,
    fontWeight: '500',
  },

  // Custom Message Styles
  customMessageContainer: {
    marginBottom: 32,
  },
  customMessageInput: {
    borderWidth: 1,
    borderColor: "rgba(148, 163, 184, 0.3)",
    borderRadius: 16,
    padding: 20,
    fontSize: 16,
    backgroundColor: "#ffffff",
    minHeight: 100,
    textAlignVertical: "top",
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
    fontWeight: '500',
    color: '#0f172a',
  },

  // Progress Styles
  progressSection: {
    marginBottom: 32,
    backgroundColor: "#ffffff",
    padding: 24,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: "rgba(148, 163, 184, 0.1)",
  },
  progressTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#0f172a",
    textAlign: "center",
    marginBottom: 16,
    letterSpacing: -0.3,
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  progressStage: {
    fontSize: 16,
    fontWeight: "600",
    color: THEME.accent,
  },
  estimatedTime: {
    fontSize: 14,
    color: "#64748b",
    fontWeight: '500',
  },
  progressBar: {
    height: 10,
    backgroundColor: "rgba(148, 163, 184, 0.2)",
    borderRadius: 5,
    overflow: "hidden",
    marginBottom: 12,
  },
  progressFill: {
    height: "100%",
    backgroundColor: THEME.accent,
    borderRadius: 5,
  },
  progressText: {
    fontSize: 16,
    color: "#64748b",
    textAlign: "center",
    fontWeight: '600',
  },
  progressDescription: {
    fontSize: 16,
    color: "#64748b",
    textAlign: "center",
    lineHeight: 22,
    marginBottom: 16,
    fontWeight: '500',
  },
  aiInsights: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(251, 191, 36, 0.1)",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "rgba(251, 191, 36, 0.3)",
  },
  aiInsightsText: {
    fontSize: 14,
    color: "#92400e",
    marginLeft: 12,
    flex: 1,
    lineHeight: 20,
    fontWeight: '500',
  },

  generateButton: {
    width: "100%",
    paddingVertical: 18,
    paddingHorizontal: 32,
    borderRadius: 16,
    alignItems: "center",
    marginBottom: 32,
    flexDirection: "row",
    justifyContent: "center",
    shadowColor: THEME.accent,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  generateButtonText: {
    color: "#ffffff",
    fontSize: 18,
    fontWeight: "700",
    letterSpacing: 0.3,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  disabledButton: {
    opacity: 0.6,
    shadowOpacity: 0.1,
  },

  errorText: {
    color: THEME.danger,
    fontSize: 16,
    textAlign: "center",
    marginTop: 12,
    fontWeight: '600',
  },

  // Loading and Error States
  listingLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  listingLoadingText: {
    fontSize: 18,
    color: THEME.gray,
    marginTop: 16,
    fontWeight: '600',
  },
  listingErrorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  listingErrorTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: THEME.danger,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  listingErrorButton: {
    backgroundColor: THEME.primary,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
    marginTop: 24,
  },
  listingErrorButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },

  // Preview Interface Styles
  previewContainer: {
    flex: 1,
  },
  previewHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 32,
  },
  backToGenerateButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: "#ffffff",
    borderRadius: 12,
    borderWidth: 2,
    borderColor: THEME.accent,
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  backToGenerateText: {
    color: THEME.accent,
    fontSize: 15,
    fontWeight: "600",
    marginLeft: 6,
  },
  previewTitle: {
    fontSize: 24,
    fontWeight: "700",
    color: "#0f172a",
    flex: 1,
    letterSpacing: -0.3,
  },

  // Application Preview Styles
  applicationPreview: {
    backgroundColor: "#ffffff",
    borderRadius: 20,
    padding: 24,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
    borderWidth: 1,
    borderColor: "rgba(148, 163, 184, 0.1)",
  },
  previewMeta: {
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(148, 163, 184, 0.2)",
  },
  metaRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  metaLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#0f172a",
    width: 80,
    letterSpacing: -0.2,
  },
  editableSubject: {
    flex: 1,
    borderWidth: 1,
    borderColor: "rgba(148, 163, 184, 0.3)",
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    backgroundColor: "#f8fafc",
    fontWeight: '500',
    color: '#0f172a',
  },
  previewTemplate: {
    fontSize: 16,
    color: "#64748b",
    flex: 1,
    fontWeight: '500',
  },
  changeTemplateButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: "rgba(247, 37, 133, 0.1)",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "rgba(247, 37, 133, 0.3)",
  },
  changeTemplateText: {
    fontSize: 14,
    color: THEME.accent,
    fontWeight: "600",
  },
  editingSection: {
    marginBottom: 20,
  },
  editingHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  editingSectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#0f172a",
    letterSpacing: -0.2,
  },
  editingControls: {
    flexDirection: "row",
    gap: 12,
  },
  editingControl: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: "#f1f5f9",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "rgba(148, 163, 184, 0.2)",
  },
  editingControlText: {
    fontSize: 13,
    color: "#64748b",
    marginLeft: 6,
    fontWeight: '500',
  },
  editableContent: {
    borderWidth: 1,
    borderColor: "rgba(148, 163, 184, 0.3)",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: "#f8fafc",
    minHeight: 220,
    textAlignVertical: "top",
    marginBottom: 20,
    fontWeight: '500',
    color: '#0f172a',
    lineHeight: 24,
  },
  personalizationOptions: {
    marginBottom: 16,
  },
  personalizationTitle: {
    fontSize: 14,
    fontWeight: "500",
    color: "#374151",
    marginBottom: 8,
  },
  personalizationTags: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  personalizationTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: "#ede9fe",
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "#c4b5fd",
  },
  personalizationTagText: {
    fontSize: 12,
    color: "#7c3aed",
    fontWeight: "500",
  },
  approvalWorkflow: {
    backgroundColor: "#f0f9ff",
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#bae6fd",
    marginTop: 16,
  },
  approvalTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#0369a1",
    marginBottom: 12,
  },
  checklistItems: {
    gap: 8,
  },
  checklistItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  checklistItemText: {
    fontSize: 14,
    color: "#0369a1",
    marginLeft: 8,
    flex: 1,
  },

  // Tips Styles
  tipsContainer: {
    backgroundColor: "#f0f9ff",
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#bae6fd",
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#0369a1",
    marginBottom: 8,
  },
  tipItem: {
    fontSize: 14,
    color: "#0369a1",
    marginBottom: 4,
    lineHeight: 20,
  },

  // Submission Controls Styles
  submissionControls: {
    marginBottom: 32,
  },
  submissionOptions: {
    flexDirection: "row",
    gap: 16,
    marginBottom: 20,
  },
  submissionOption: {
    flex: 1,
    backgroundColor: "#ffffff",
    padding: 20,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: "rgba(148, 163, 184, 0.2)",
    alignItems: "center",
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  selectedSubmissionOption: {
    borderColor: THEME.accent,
    backgroundColor: "rgba(247, 37, 133, 0.05)",
    shadowColor: THEME.accent,
    shadowOpacity: 0.15,
  },
  submissionOptionTitle: {
    fontSize: 16,
    fontWeight: "700",
    color: "#0f172a",
    marginTop: 12,
    marginBottom: 6,
    textAlign: "center",
    letterSpacing: -0.2,
  },
  selectedSubmissionOptionTitle: {
    color: THEME.accent,
  },
  submissionOptionDescription: {
    fontSize: 14,
    color: "#64748b",
    textAlign: "center",
    fontWeight: '500',
  },

  // Autonomous Info Styles
  autonomousInfo: {
    backgroundColor: "rgba(59, 130, 246, 0.05)",
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: "rgba(59, 130, 246, 0.2)",
    marginTop: 16,
  },
  autonomousHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  autonomousStatus: {
    flexDirection: "row",
    alignItems: "center",
  },
  autonomousStatusText: {
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 10,
  },
  settingsButton: {
    backgroundColor: THEME.primary,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 12,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: THEME.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  settingsButtonText: {
    color: "#ffffff",
    fontSize: 15,
    fontWeight: "600",
    marginLeft: 6,
  },
  autonomousExplanation: {
    marginBottom: 16,
  },
  autonomousTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#0369a1",
    marginBottom: 8,
  },
  autonomousSteps: {
    gap: 8,
  },
  autonomousStep: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  stepNumber: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "#0369a1",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    marginTop: 2,
  },
  stepNumberText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#ffffff",
  },
  stepText: {
    fontSize: 13,
    color: "#0369a1",
    flex: 1,
    lineHeight: 18,
  },
  autonomousStats: {
    backgroundColor: "#ffffff",
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
  },
  autonomousStatsTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: 8,
  },
  statsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statItem: {
    alignItems: "center",
  },
  statValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#0369a1",
  },
  statLabel: {
    fontSize: 11,
    color: "#6b7280",
    textAlign: "center",
    marginTop: 2,
  },
  warningBox: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#fef3c7",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#fbbf24",
  },
  warningText: {
    fontSize: 13,
    color: "#92400e",
    marginLeft: 8,
    flex: 1,
    lineHeight: 18,
  },

  // Action Buttons Styles
  actionButtons: {
    flexDirection: "row",
    gap: 16,
  },
  approveButton: {
    flex: 1,
    backgroundColor: THEME.success,
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 16,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    shadowColor: THEME.success,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  approveButtonText: {
    color: "#ffffff",
    fontSize: 17,
    fontWeight: "700",
    marginLeft: 8,
    letterSpacing: 0.2,
  },
  submitButton: {
    flex: 1,
    backgroundColor: THEME.accent,
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 16,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    shadowColor: THEME.accent,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  submitButtonText: {
    color: "#ffffff",
    fontSize: 17,
    fontWeight: "700",
    marginLeft: 8,
    letterSpacing: 0.2,
  },

  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    backgroundColor: "#ffffff",
    borderBottomWidth: 1,
    borderBottomColor: "rgba(148, 163, 184, 0.2)",
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#0f172a",
    letterSpacing: -0.3,
  },
  closeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: "#f1f5f9",
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  modalFooter: {
    padding: 20,
    backgroundColor: "#ffffff",
    borderTopWidth: 1,
    borderTopColor: "rgba(148, 163, 184, 0.2)",
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  saveButton: {
    backgroundColor: THEME.accent,
    paddingVertical: 18,
    paddingHorizontal: 24,
    borderRadius: 16,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    shadowColor: THEME.accent,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  saveButtonText: {
    color: "#ffffff",
    fontSize: 17,
    fontWeight: "700",
    marginLeft: 8,
    letterSpacing: 0.2,
  },

  // Settings Styles
  explanationSection: {
    backgroundColor: "rgba(59, 130, 246, 0.05)",
    padding: 20,
    borderRadius: 16,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: "rgba(59, 130, 246, 0.2)",
  },
  explanationHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  explanationTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#1e40af",
    marginLeft: 12,
    letterSpacing: -0.2,
  },
  explanationText: {
    fontSize: 16,
    color: "#1e40af",
    lineHeight: 22,
    fontWeight: '500',
  },
  settingsGroup: {
    marginBottom: 32,
  },
  settingsGroupTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#0f172a",
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: "rgba(148, 163, 184, 0.2)",
    letterSpacing: -0.3,
  },
  settingItem: {
    backgroundColor: "#ffffff",
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
    borderWidth: 1,
    borderColor: "rgba(148, 163, 184, 0.1)",
  },
  settingHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  settingLabel: {
    fontSize: 17,
    fontWeight: "700",
    color: "#0f172a",
    flex: 1,
    letterSpacing: -0.2,
  },
  settingDescription: {
    fontSize: 15,
    color: "#64748b",
    lineHeight: 22,
    fontWeight: '500',
  },
  sliderContainer: {
    alignItems: "center",
    marginTop: 16,
  },
  sliderValue: {
    fontSize: 20,
    fontWeight: "700",
    color: THEME.accent,
    marginBottom: 6,
    letterSpacing: -0.2,
  },
  sliderNote: {
    fontSize: 13,
    color: "#94a3b8",
    fontStyle: "italic",
    fontWeight: '500',
  },
});