import React, { useEffect, useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Image,
  ActivityIndicator,
  Alert,
  Dimensions,
  Linking,
  FlatList,
} from "react-native";
import { useRouter, useLocalSearchParams } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useListingsStore } from "../store/listingsStore";
import { listingsService } from "../services/listingsService";
import { useAIStore } from "../store/aiStore";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  FadeIn,
  FadeInUp,
  SlideInRight,
  SlideInLeft,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

const { width } = Dimensions.get("window");

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
};

// Enhanced Header Component
const Header = ({
  showBackButton = false,
  onBack,
  onShare,
  onFavorite,
  isFavorite = false,
}: {
  showBackButton?: boolean;
  onBack?: () => void;
  onShare?: () => void;
  onFavorite?: () => void;
  isFavorite?: boolean;
}) => {
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        {showBackButton && (
          <TouchableOpacity
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              onBack?.();
            }}
            style={styles.backButton}
            activeOpacity={0.8}
          >
            <View style={styles.backButtonInner}>
              <Ionicons name="chevron-back" size={24} color={THEME.primary} />
            </View>
          </TouchableOpacity>
        )}

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <Text style={styles.headerTitle}>Property Details</Text>
        </View>

        <View style={styles.headerActions}>
          {onShare && (
            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                onShare();
              }}
              style={styles.headerActionButton}
              activeOpacity={0.8}
            >
              <View style={styles.actionButtonInner}>
                <Ionicons name="share-outline" size={20} color={THEME.primary} />
              </View>
            </TouchableOpacity>
          )}
          {onFavorite && (
            <TouchableOpacity
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
                onFavorite();
              }}
              style={styles.headerActionButton}
              activeOpacity={0.8}
            >
              <View style={styles.actionButtonInner}>
                <Ionicons
                  name={isFavorite ? "heart" : "heart-outline"}
                  size={20}
                  color={isFavorite ? THEME.accent : THEME.primary}
                />
              </View>
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>
    </LinearGradient>
  );
};

export default function ListingDetailsScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { currentListing, fetchListing, isLoading, error } = useListingsStore();
  const { translateText } = useAIStore();

  const [isFavorite, setIsFavorite] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [translatedDescription, setTranslatedDescription] = useState<string>("");
  const [isTranslating, setIsTranslating] = useState(false);
  const [showTranslation, setShowTranslation] = useState(false);
  const flatListRef = React.useRef<FlatList>(null);

  useEffect(() => {
    console.log("🔍 Listing Details - URL params:", { id, type: typeof id });
    if (id && typeof id === "string") {
      console.log("📡 Fetching listing with ID:", id);
      fetchListing(id);
    } else {
      console.log("❌ No valid listing ID found");
    }
  }, [id]);

  const formatPrice = (price: number | string | undefined) => {
    // Use the service's formatPrice function directly - it has the proper logic
    return listingsService.formatPrice(price);
  };

  const handleShare = async () => {
    if (currentListing) {
      try {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        await Linking.openURL(
          `mailto:?subject=Check out this property&body=I found this great property: ${currentListing.title
          } - ${formatPrice(currentListing.price)}. Check it out: ${currentListing.url
          }`
        );
      } catch (error) {
        Alert.alert("Share", "Unable to open email app");
      }
    }
  };

  const handleFavorite = () => {
    setIsFavorite(!isFavorite);
    Haptics.notificationAsync(
      isFavorite
        ? Haptics.NotificationFeedbackType.Warning
        : Haptics.NotificationFeedbackType.Success
    );
    Alert.alert(
      isFavorite ? "Removed from favorites" : "Added to favorites",
      isFavorite
        ? "Property removed from your favorites"
        : "Property added to your favorites"
    );
  };

  const handleApply = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.push(`/application?listingId=${id}`);
  };

  const handleContact = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    Alert.alert(
      "Contact Agent",
      "Would you like to contact the agent about this property?",
      [
        { text: "Cancel", style: "cancel" },
        { text: "Call", onPress: () => Alert.alert("Calling agent...") },
        { text: "Email", onPress: () => Alert.alert("Opening email...") },
      ]
    );
  };

  const handleTranslate = async () => {
    if (!currentListing?.description) return;

    try {
      setIsTranslating(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Detect if description is likely Dutch and translate to English, or vice versa
      const isDutch = /[ëïöüáéíóúàèìòù]|ij|oe|ui|aa|ee|oo|uu/.test(currentListing.description.toLowerCase());
      const targetLanguage = isDutch ? 'en' : 'nl';

      const result = await translateText(currentListing.description, targetLanguage);
      setTranslatedDescription(result.translatedText);
      setShowTranslation(true);
      
      // Show success message for long translations
      if (currentListing.description.length > 500) {
        Alert.alert(
          "Translation Complete",
          "Description successfully translated!",
          [{ text: "Great!" }]
        );
      }
    } catch (error: any) {
      console.error('Translation error:', error);

      let errorMessage = "Unable to translate the description. Please try again later.";

      // Provide more specific error messages based on error type
      if (error?.status === 500) {
        errorMessage = "Translation service is temporarily unavailable. Please try again later.";
      } else if (error?.status === 401) {
        errorMessage = "Authentication required. Please log in and try again.";
      } else if (error?.status === 429) {
        errorMessage = "Too many translation requests. Please wait a moment and try again.";
      } else if (error?.message?.includes('Network Error') || error?.code === 'NETWORK_ERROR') {
        errorMessage = "Network connection issue. Please check your internet connection and try again.";
      }

      // Show specific guidance for API key configuration
      if (error?.message?.includes('configure API keys') || error?.message?.includes('authentication')) {
        Alert.alert(
          "AI Translation Setup Required",
          "To use AI translation, please configure API keys in the backend. Check the setup guide for instructions.",
          [{ text: "OK" }]
        );
      } else {
        Alert.alert("Translation Error", errorMessage);
      }
    } finally {
      setIsTranslating(false);
    }
  };

  // Helper function to get the best available value for bedrooms/rooms
  const getBedrooms = (listing: any) => {
    if (listing.bedrooms && listing.bedrooms !== "N/A") {
      return listing.bedrooms;
    }
    if (listing.rooms && listing.rooms !== "N/A") {
      return listing.rooms;
    }
    return "N/A";
  };

  // Helper function to get size information
  const getSize = (listing: any) => {
    if (listing.size) {
      return listing.size;
    }
    if (listing.area) {
      return `${listing.area} m²`;
    }
    return "N/A";
  };

  // Carousel functions
  const handleScroll = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset;
    const viewSize = event.nativeEvent.layoutMeasurement;
    const pageNum = Math.floor(contentOffset.x / viewSize.width);
    setCurrentImageIndex(pageNum);
  };

  const scrollToIndex = (index: number) => {
    if (flatListRef.current && listing.images && index < listing.images.length) {
      flatListRef.current.scrollToIndex({ index, animated: true });
      setCurrentImageIndex(index);
    }
  };

  const isValidImageUrl = (url: string) => {
    if (!url || typeof url !== 'string') return false;
    const lower = url.toLowerCase();
    if (lower.endsWith('.svg')) return false;
    if (lower.includes('funda-logo')) return false;
    if (lower.includes('/maps/map_')) return false;
    return lower.startsWith('http');
  };

  const renderImageItem = ({ item }: { item: string }) => (
    <View style={styles.imageItemContainer}>
      <Image
        source={{ uri: item }}
        style={styles.carouselImage}
        resizeMode="cover"
      />
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.3)']}
        style={styles.imageGradient}
      />
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          showBackButton={true}
          onBack={() => router.back()}
        />
        <Animated.View
          style={styles.loadingContainer}
          entering={FadeIn.duration(600)}
        >
          <View style={styles.loadingContent}>
            <ActivityIndicator size="large" color={THEME.primary} />
            <Text style={styles.loadingText}>Loading property details...</Text>
          </View>
        </Animated.View>
      </SafeAreaView>
    );
  }

  if (error || !currentListing) {
    return (
      <SafeAreaView style={styles.container}>
        <Header
          showBackButton={true}
          onBack={() => router.back()}
        />
        <Animated.View
          style={styles.errorContainer}
          entering={FadeIn.duration(600)}
        >
          <View style={styles.errorContent}>
            <View style={styles.errorIconContainer}>
              <Ionicons name="alert-circle-outline" size={64} color={THEME.danger} />
            </View>
            <Text style={styles.errorTitle}>Property not found</Text>
            <Text style={styles.errorText}>
              {error || "The property you're looking for could not be found."}
            </Text>
            <TouchableOpacity
              style={styles.errorButton}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                router.back();
              }}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={[THEME.accent, THEME.secondary]}
                style={styles.errorButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Text style={styles.errorButtonText}>Go Back</Text>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </SafeAreaView>
    );
  }

  const listing = currentListing;

  return (
    <SafeAreaView style={styles.container}>
      <Header
        showBackButton={true}
        onBack={() => router.back()}
        onShare={handleShare}
        onFavorite={handleFavorite}
        isFavorite={isFavorite}
      />
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.detailsContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Image Gallery */}
        <Animated.View
          style={styles.imageContainer}
          entering={FadeIn.duration(800)}
        >
          {listing.images && listing.images.length > 0 ? (
            <>
              <FlatList
                ref={flatListRef}
                data={listing.images.filter(isValidImageUrl)}
                renderItem={renderImageItem}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                onScroll={handleScroll}
                scrollEventThrottle={16}
                keyExtractor={(item, index) => index.toString()}
                getItemLayout={(data, index) => ({
                  length: width,
                  offset: width * index,
                  index,
                })}
              />
              {listing.images.length > 1 && (
                <View style={styles.imageIndicators}>
                  {listing.images.map((_, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.imageIndicator,
                        currentImageIndex === index && styles.imageIndicatorActive,
                      ]}
                      onPress={() => {
                        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                        scrollToIndex(index);
                      }}
                      activeOpacity={0.8}
                    />
                  ))}
                </View>
              )}
            </>
          ) : (
            <View style={styles.imageItemContainer}>
              <Image
                source={{
                  uri: "https://placehold.co/600x400/e0e0e0/333333?text=No+Image",
                }}
                style={styles.listingImage}
              />
              <LinearGradient
                colors={['transparent', 'rgba(0,0,0,0.3)']}
                style={styles.imageGradient}
              />
            </View>
          )}
        </Animated.View>

        {/* Property Header */}
        <Animated.View
          style={styles.propertyHeader}
          entering={FadeInUp.duration(600).delay(200)}
        >
          <Text style={styles.listingTitle}>{listing.title}</Text>
          <View style={styles.priceContainer}>
            <LinearGradient
              colors={[THEME.accent, THEME.secondary]}
              style={styles.priceGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Text style={styles.listingPrice}>{formatPrice(listing.price)}</Text>
            </LinearGradient>
          </View>
          <View style={styles.locationContainer}>
            <View style={styles.locationIcon}>
              <Ionicons name="location" size={16} color={THEME.accent} />
            </View>
            <Text style={styles.locationText}>
              {typeof listing.location === "string"
                ? listing.location
                : listing.location?.city || "Unknown location"}
            </Text>
          </View>
        </Animated.View>

        {/* Property Details Grid */}
        <Animated.View
          style={styles.detailsGrid}
          entering={FadeInUp.duration(600).delay(400)}
        >
          <Animated.View
            style={styles.detailItem}
            entering={SlideInLeft.duration(600).delay(400)}
          >
            <LinearGradient
              colors={['rgba(247, 37, 133, 0.1)', 'rgba(247, 37, 133, 0.05)']}
              style={styles.detailItemGradient}
            >
              <View style={styles.detailIconContainer}>
                <Ionicons name="bed" size={24} color={THEME.accent} />
              </View>
              <Text style={styles.detailLabel}>Bedrooms</Text>
              <Text style={styles.detailValue}>{getBedrooms(listing)}</Text>
            </LinearGradient>
          </Animated.View>

          <Animated.View
            style={styles.detailItem}
            entering={SlideInRight.duration(600).delay(500)}
          >
            <LinearGradient
              colors={['rgba(67, 97, 238, 0.1)', 'rgba(67, 97, 238, 0.05)']}
              style={styles.detailItemGradient}
            >
              <View style={styles.detailIconContainer}>
                <Ionicons name="water" size={24} color={THEME.primary} />
              </View>
              <Text style={styles.detailLabel}>Bathrooms</Text>
              <Text style={styles.detailValue}>{listing.bathrooms || "1"}</Text>
            </LinearGradient>
          </Animated.View>

          <Animated.View
            style={styles.detailItem}
            entering={SlideInLeft.duration(600).delay(600)}
          >
            <LinearGradient
              colors={['rgba(114, 9, 183, 0.1)', 'rgba(114, 9, 183, 0.05)']}
              style={styles.detailItemGradient}
            >
              <View style={styles.detailIconContainer}>
                <Ionicons name="resize" size={24} color={THEME.secondary} />
              </View>
              <Text style={styles.detailLabel}>Size</Text>
              <Text style={styles.detailValue}>{getSize(listing)}</Text>
            </LinearGradient>
          </Animated.View>

          <Animated.View
            style={styles.detailItem}
            entering={SlideInRight.duration(600).delay(700)}
          >
            <LinearGradient
              colors={['rgba(16, 185, 129, 0.1)', 'rgba(16, 185, 129, 0.05)']}
              style={styles.detailItemGradient}
            >
              <View style={styles.detailIconContainer}>
                <Ionicons name="calendar" size={24} color={THEME.success} />
              </View>
              <Text style={styles.detailLabel}>Year Built</Text>
              <Text style={styles.detailValue}>{listing.year || "N/A"}</Text>
            </LinearGradient>
          </Animated.View>
        </Animated.View>

        {/* Property Type & Interior */}
        <Animated.View
          style={styles.propertyInfo}
          entering={FadeInUp.duration(600).delay(800)}
        >
          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Ionicons name="home" size={20} color={THEME.primary} />
            </View>
            <Text style={styles.infoLabel}>Property Type</Text>
            <Text style={styles.infoValue}>
              {listing.propertyType
                ? listing.propertyType.charAt(0).toUpperCase() +
                listing.propertyType.slice(1)
                : "Apartment"}
            </Text>
          </View>
          <View style={styles.infoItem}>
            <View style={styles.infoIconContainer}>
              <Ionicons name="bed" size={20} color={THEME.secondary} />
            </View>
            <Text style={styles.infoLabel}>Interior</Text>
            <Text style={styles.infoValue}>
              {listing.interior ||
                (listing.furnished ? "Furnished" : "Unfurnished")}
            </Text>
          </View>
        </Animated.View>

        {/* Description */}
        <Animated.View
          style={styles.section}
          entering={FadeInUp.duration(600).delay(900)}
        >
          <View style={styles.sectionHeader}>
            <View style={styles.sectionTitleContainer}>
              <View style={styles.sectionIconContainer}>
                <Ionicons name="document-text" size={24} color={THEME.accent} />
              </View>
              <Text style={styles.sectionTitle}>Description</Text>
            </View>
            {listing.description && (
              <TouchableOpacity
                style={styles.translateButton}
                onPress={handleTranslate}
                disabled={isTranslating}
                activeOpacity={0.8}
              >
                <View style={styles.translateButtonContent}>
                  {isTranslating ? (
                    <ActivityIndicator size="small" color={THEME.primary} />
                  ) : (
                    <Ionicons name="language" size={18} color={THEME.primary} />
                  )}
                  <Text style={styles.translateButtonText}>
                    {isTranslating ? 
                      (currentListing?.description && currentListing.description.length > 500 ? 
                        "Translating long text..." : "Translating...") 
                      : "Translate"}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
          <Text style={styles.descriptionText}>
            {showTranslation && translatedDescription
              ? translatedDescription
              : listing.description || "No description available for this property."}
          </Text>
          {showTranslation && translatedDescription && (
            <TouchableOpacity
              style={styles.showOriginalButton}
              onPress={() => {
                setShowTranslation(false);
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }}
              activeOpacity={0.8}
            >
              <Text style={styles.showOriginalText}>Show Original</Text>
            </TouchableOpacity>
          )}
        </Animated.View>

        {/* Action Buttons */}
        <Animated.View
          style={styles.actionButtons}
          entering={FadeInUp.duration(600).delay(1000)}
        >
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleContact}
            activeOpacity={0.8}
          >
            <View style={styles.secondaryButtonContent}>
              <Ionicons name="call" size={20} color={THEME.accent} />
              <Text style={styles.secondaryButtonText}>Contact Agent</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.primaryButton}
            onPress={handleApply}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={[THEME.accent, THEME.secondary]}
              style={styles.primaryButtonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <Ionicons name="document-text" size={20} color={THEME.light} />
              <Text style={styles.primaryButtonText}>Apply Now</Text>
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>

        {/* Source Info */}
        <Animated.View
          style={styles.sourceInfo}
          entering={FadeInUp.duration(600).delay(1100)}
        >
          <View style={styles.sourceContainer}>
            <Ionicons name="information-circle" size={16} color={THEME.gray} />
            <Text style={styles.sourceText}>
              Listed on {listing.source || "Unknown"} • Added{" "}
              {new Date(listing.dateAdded).toLocaleDateString()}
            </Text>
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    padding: 4,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerCenter: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  logoContainer: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 16,
    fontWeight: '800',
    color: THEME.primary,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: THEME.light,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerActionButton: {
    padding: 4,
  },
  actionButtonInner: {
    width: 36,
    height: 36,
    backgroundColor: THEME.light,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scrollContainer: {
    flex: 1,
  },
  detailsContainer: {
    paddingBottom: 100,
  },
  // Loading and Error States
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingContent: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 40,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  loadingText: {
    fontSize: 16,
    color: THEME.gray,
    marginTop: 16,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorContent: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 40,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    maxWidth: width - 40,
  },
  errorIconContainer: {
    marginBottom: 16,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: THEME.danger,
    marginBottom: 12,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: THEME.gray,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 24,
  },
  errorButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  errorButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
  },
  errorButtonText: {
    color: THEME.light,
    fontSize: 16,
    fontWeight: '700',
  },
  // Image Gallery
  imageContainer: {
    position: 'relative',
    marginBottom: 20,
    marginTop: 20,
    borderRadius: 20,
    overflow: 'hidden',
    marginHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  imageItemContainer: {
    position: 'relative',
  },
  listingImage: {
    width: '100%',
    height: 300,
  },
  carouselImage: {
    width: width - 40,
    height: 300,
  },
  imageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
  },
  imageIndicators: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  imageIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  imageIndicatorActive: {
    backgroundColor: THEME.light,
    borderColor: THEME.light,
  },
  // Property Header
  propertyHeader: {
    backgroundColor: THEME.light,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  listingTitle: {
    fontSize: 28,
    fontWeight: '800',
    color: THEME.dark,
    marginBottom: 16,
    lineHeight: 36,
  },
  priceContainer: {
    alignSelf: 'flex-start',
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  priceGradient: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  listingPrice: {
    fontSize: 24,
    fontWeight: '800',
    color: THEME.light,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  locationIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 16,
    color: THEME.gray,
    fontWeight: '500',
  },
  // Property Details Grid
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: THEME.light,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 20,
    padding: 20,
    gap: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  detailItem: {
    width: (width - 112) / 2,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  detailItemGradient: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: THEME.light,
  },
  detailIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 12,
    color: THEME.gray,
    marginBottom: 4,
    fontWeight: '600',
  },
  detailValue: {
    fontSize: 18,
    fontWeight: '800',
    color: THEME.dark,
  },
  // Property Info
  propertyInfo: {
    backgroundColor: THEME.light,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 20,
    padding: 24,
    flexDirection: 'row',
    gap: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  infoItem: {
    flex: 1,
  },
  infoIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: THEME.gray,
    marginBottom: 6,
    fontWeight: '600',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '700',
    color: THEME.dark,
  },
  // Section
  section: {
    backgroundColor: THEME.light,
    marginHorizontal: 20,
    marginBottom: 20,
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  sectionIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '800',
    color: THEME.dark,
  },
  descriptionText: {
    fontSize: 16,
    color: THEME.gray,
    lineHeight: 26,
    fontWeight: '500',
  },
  // Action Buttons
  actionButtons: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginBottom: 20,
    gap: 16,
  },
  primaryButton: {
    flex: 1,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 8,
  },
  primaryButtonGradient: {
    paddingVertical: 18,
    paddingHorizontal: 24,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
  },
  primaryButtonText: {
    color: THEME.light,
    fontSize: 16,
    fontWeight: '700',
  },
  secondaryButton: {
    flex: 1,
    backgroundColor: THEME.light,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: THEME.accent,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  secondaryButtonContent: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
  },
  secondaryButtonText: {
    color: THEME.accent,
    fontSize: 16,
    fontWeight: '700',
  },
  // Source Info
  sourceInfo: {
    marginHorizontal: 20,
    marginBottom: 20,
    alignItems: 'center',
  },
  sourceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: THEME.light,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sourceText: {
    fontSize: 14,
    color: THEME.gray,
    fontWeight: '500',
  },
  // Translate Button Styles
  translateButton: {
    backgroundColor: 'rgba(67, 97, 238, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: 'rgba(67, 97, 238, 0.2)',
  },
  translateButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  translateButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: THEME.primary,
  },
  showOriginalButton: {
    alignSelf: 'flex-start',
    marginTop: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: 'rgba(247, 37, 133, 0.1)',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(247, 37, 133, 0.2)',
  },
  showOriginalText: {
    fontSize: 12,
    fontWeight: '600',
    color: THEME.accent,
  },
});
