import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { useRouter } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import Animated, { FadeInUp, FadeInDown } from "react-native-reanimated";
import * as Haptics from "expo-haptics";
import { Picker } from "@react-native-picker/picker";

import { useAuthStore } from "../store/authStore";
import { userProfileService } from "../services/userProfileService";
import {
  autoApplicationService,
  AutoApplicationSettings,
} from "../services/autoApplicationService";

// Hide the default navigation header
export const options = {
  headerShown: false,
};

// Helper functions for European date format (DD/MM/YYYY)
const formatDateToEuropean = (date: Date | string): string => {
  if (!date) return "";
  const d = new Date(date);
  if (isNaN(d.getTime())) return "";

  const day = d.getDate().toString().padStart(2, "0");
  const month = (d.getMonth() + 1).toString().padStart(2, "0");
  const year = d.getFullYear();

  return `${day}/${month}/${year}`;
};

const parseEuropeanDate = (dateString: string): Date | null => {
  if (!dateString) return null;

  // Handle both DD/MM/YYYY and DD-MM-YYYY formats
  const cleanDate = dateString.replace(/[-]/g, "/");
  const parts = cleanDate.split("/");

  if (parts.length !== 3) return null;

  const day = parseInt(parts[0], 10);
  const month = parseInt(parts[1], 10);
  const year = parseInt(parts[2], 10);

  // Basic validation
  if (
    day < 1 ||
    day > 31 ||
    month < 1 ||
    month > 12 ||
    year < 1900 ||
    year > 2100
  ) {
    return null;
  }

  // Create date (month is 0-indexed in JavaScript Date)
  const date = new Date(year, month - 1, day);

  // Verify the date is valid (handles cases like 31/02/2023)
  if (
    date.getDate() !== day ||
    date.getMonth() !== month - 1 ||
    date.getFullYear() !== year
  ) {
    return null;
  }

  return date;
};

const validateEuropeanDate = (
  dateString: string
): { isValid: boolean; error?: string } => {
  if (!dateString.trim()) {
    return { isValid: true }; // Empty date is valid (optional field)
  }

  const date = parseEuropeanDate(dateString);
  if (!date) {
    return {
      isValid: false,
      error: "Please enter a valid date in DD/MM/YYYY format",
    };
  }

  // Check if date is not in the future (for birth date)
  if (date > new Date()) {
    return {
      isValid: false,
      error: "Date of birth cannot be in the future",
    };
  }

  // Check if date is reasonable (not too old)
  const minDate = new Date();
  minDate.setFullYear(minDate.getFullYear() - 120);
  if (date < minDate) {
    return {
      isValid: false,
      error: "Please enter a valid birth date",
    };
  }

  return { isValid: true };
};

// Form validation functions
const validateProfile = (
  profile: any,
  preferences: any
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Required fields validation
  if (!profile.firstName?.trim()) {
    errors.push("First name is required");
  }

  if (!profile.lastName?.trim()) {
    errors.push("Last name is required");
  }

  // Phone number validation (if provided)
  if (profile.phoneNumber?.trim()) {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    if (!phoneRegex.test(profile.phoneNumber.replace(/\s/g, ""))) {
      errors.push("Please enter a valid phone number");
    }
  }

  // Date of birth validation
  const dateValidation = validateEuropeanDate(profile.dateOfBirth);
  if (!dateValidation.isValid) {
    errors.push(dateValidation.error || "Invalid date of birth");
  }

  // Employment type validation (if provided)
  if (profile.employment?.employmentType?.trim()) {
    const validEmploymentTypes = [
      "full-time",
      "part-time",
      "student",
      "freelancer",
      "unemployed",
    ];
    if (!validEmploymentTypes.includes(profile.employment.employmentType)) {
      errors.push("Please select a valid employment type");
    }
  }

  // Contract type validation (if provided)
  if (profile.employment?.contractType?.trim()) {
    const validContractTypes = [
      "permanent",
      "temporary",
      "student",
      "freelancer",
    ];
    if (!validContractTypes.includes(profile.employment.contractType)) {
      errors.push("Please select a valid contract type");
    }
  }

  // Monthly income validation (if provided)
  if (profile.employment?.monthlyIncome?.trim()) {
    const income = parseFloat(profile.employment.monthlyIncome);
    if (isNaN(income) || income < 0) {
      errors.push("Monthly income must be a valid positive number");
    }
  }

  // Preferences validation (if provided)
  if (preferences.minPrice?.trim()) {
    const minPrice = parseFloat(preferences.minPrice);
    if (isNaN(minPrice) || minPrice < 0) {
      errors.push("Minimum price must be a valid positive number");
    }
  }

  if (preferences.maxPrice?.trim()) {
    const maxPrice = parseFloat(preferences.maxPrice);
    if (isNaN(maxPrice) || maxPrice < 0) {
      errors.push("Maximum price must be a valid positive number");
    }
  }

  if (preferences.minPrice?.trim() && preferences.maxPrice?.trim()) {
    const minPrice = parseFloat(preferences.minPrice);
    const maxPrice = parseFloat(preferences.maxPrice);
    if (!isNaN(minPrice) && !isNaN(maxPrice) && minPrice > maxPrice) {
      errors.push("Minimum price cannot be greater than maximum price");
    }
  }

  if (preferences.minRooms?.trim()) {
    const minRooms = parseInt(preferences.minRooms);
    if (isNaN(minRooms) || minRooms < 1) {
      errors.push("Minimum rooms must be at least 1");
    }
  }

  if (preferences.maxRooms?.trim()) {
    const maxRooms = parseInt(preferences.maxRooms);
    if (isNaN(maxRooms) || maxRooms < 1) {
      errors.push("Maximum rooms must be at least 1");
    }
  }

  if (preferences.minRooms?.trim() && preferences.maxRooms?.trim()) {
    const minRooms = parseInt(preferences.minRooms);
    const maxRooms = parseInt(preferences.maxRooms);
    if (!isNaN(minRooms) && !isNaN(maxRooms) && minRooms > maxRooms) {
      errors.push("Minimum rooms cannot be greater than maximum rooms");
    }
  }

  return { isValid: errors.length === 0, errors };
};

// Clean data for API submission (remove empty strings for enum fields)
const cleanProfileData = (profile: any, preferences: any) => {
  const cleanedProfile = { ...profile };

  // Clean employment data - convert empty strings to undefined for enum fields
  if (cleanedProfile.employment) {
    cleanedProfile.employment = { ...cleanedProfile.employment };

    if (!cleanedProfile.employment.employmentType?.trim()) {
      delete cleanedProfile.employment.employmentType;
    }

    if (!cleanedProfile.employment.contractType?.trim()) {
      delete cleanedProfile.employment.contractType;
    }

    if (!cleanedProfile.employment.occupation?.trim()) {
      delete cleanedProfile.employment.occupation;
    }

    if (!cleanedProfile.employment.employer?.trim()) {
      delete cleanedProfile.employment.employer;
    }

    if (!cleanedProfile.employment.workLocation?.trim()) {
      delete cleanedProfile.employment.workLocation;
    }

    if (!cleanedProfile.employment.monthlyIncome?.trim()) {
      delete cleanedProfile.employment.monthlyIncome;
    } else {
      cleanedProfile.employment.monthlyIncome = parseFloat(
        cleanedProfile.employment.monthlyIncome
      );
    }
  }

  // Clean other optional fields
  if (!cleanedProfile.phoneNumber?.trim()) {
    delete cleanedProfile.phoneNumber;
  }

  if (!cleanedProfile.nationality?.trim()) {
    delete cleanedProfile.nationality;
  }

  // Clean preferences
  const cleanedPreferences = { ...preferences };

  if (!cleanedPreferences.minPrice?.trim()) {
    delete cleanedPreferences.minPrice;
  } else {
    cleanedPreferences.minPrice = parseFloat(cleanedPreferences.minPrice);
  }

  if (!cleanedPreferences.maxPrice?.trim()) {
    delete cleanedPreferences.maxPrice;
  } else {
    cleanedPreferences.maxPrice = parseFloat(cleanedPreferences.maxPrice);
  }

  if (!cleanedPreferences.minRooms?.trim()) {
    delete cleanedPreferences.minRooms;
  } else {
    cleanedPreferences.minRooms = parseInt(cleanedPreferences.minRooms);
  }

  if (!cleanedPreferences.maxRooms?.trim()) {
    delete cleanedPreferences.maxRooms;
  } else {
    cleanedPreferences.maxRooms = parseInt(cleanedPreferences.maxRooms);
  }

  return { profile: cleanedProfile, preferences: cleanedPreferences };
};

// Parse API error messages for better UX
const parseApiError = (error: any): string => {
  if (!error?.message) {
    return "An unexpected error occurred. Please try again.";
  }

  const message = error.message;

  // Handle validation errors
  if (message.includes("validation failed")) {
    const errors: string[] = [];

    if (message.includes("employmentType")) {
      errors.push("Please select a valid employment type");
    }

    if (message.includes("contractType")) {
      errors.push("Please select a valid contract type");
    }

    if (message.includes("firstName")) {
      errors.push("First name is required");
    }

    if (message.includes("lastName")) {
      errors.push("Last name is required");
    }

    if (message.includes("email")) {
      errors.push("Please enter a valid email address");
    }

    if (message.includes("phoneNumber")) {
      errors.push("Please enter a valid phone number");
    }

    if (message.includes("dateOfBirth")) {
      errors.push("Please enter a valid date of birth");
    }

    if (errors.length > 0) {
      return errors.join("\n\n");
    }
  }

  // Handle other common errors
  if (message.includes("duplicate") || message.includes("already exists")) {
    return "This information is already in use. Please check your details.";
  }

  if (message.includes("network") || message.includes("connection")) {
    return "Network error. Please check your connection and try again.";
  }

  if (message.includes("unauthorized") || message.includes("authentication")) {
    return "Session expired. Please log in again.";
  }

  return message;
};

// Theme colors
const THEME = {
  primary: "#4361ee",
  secondary: "#7209b7",
  accent: "#f72585",
  dark: "#0a0a18",
  light: "#ffffff",
  gray: "#6b7280",
  lightGray: "#f3f4f6",
  success: "#10b981",
  warning: "#f59e0b",
  danger: "#ef4444",
};

// Header Component
const Header = () => {
  const router = useRouter();
  const insets = useSafeAreaInsets();

  return (
    <LinearGradient
      colors={[THEME.primary, THEME.secondary]}
      style={[styles.header, { paddingTop: Math.max(insets.top, 16) }]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <Animated.View
        style={styles.headerContent}
        entering={FadeInUp.duration(600)}
      >
        <TouchableOpacity
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            router.back();
          }}
          style={styles.backButton}
          activeOpacity={0.8}
        >
          <View style={styles.backButtonInner}>
            <Ionicons name="chevron-back" size={24} color={THEME.primary} />
          </View>
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>ZM</Text>
          </View>
          <Text style={styles.headerTitle}>Edit Profile</Text>
        </View>

        <View style={styles.headerSpacer} />
      </Animated.View>
    </LinearGradient>
  );
};

// Input Field Component
const InputField = ({
  label,
  value,
  onChangeText,
  placeholder,
  keyboardType = "default",
  multiline = false,
  numberOfLines = 1,
  editable = true,
  error,
}: {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  keyboardType?: any;
  multiline?: boolean;
  numberOfLines?: number;
  editable?: boolean;
  error?: string;
}) => (
  <View style={styles.inputContainer}>
    <Text style={styles.inputLabel}>{label}</Text>
    <TextInput
      style={[
        styles.textInput,
        multiline && styles.multilineInput,
        !editable && styles.disabledInput,
        error && styles.textInputError,
      ]}
      value={value}
      onChangeText={onChangeText}
      placeholder={placeholder}
      placeholderTextColor={THEME.gray}
      keyboardType={keyboardType}
      multiline={multiline}
      numberOfLines={numberOfLines}
      editable={editable}
    />
    {error && <Text style={styles.errorText}>{error}</Text>}
  </View>
);

// Picker Field Component
const PickerField = ({
  label,
  value,
  onValueChange,
  items,
}: {
  label: string;
  value: string;
  onValueChange: (value: string) => void;
  items: { label: string; value: string }[];
}) => (
  <View style={styles.inputContainer}>
    <Text style={styles.inputLabel}>{label}</Text>
    <View style={styles.pickerContainer}>
      <Picker
        selectedValue={value}
        onValueChange={onValueChange}
        style={styles.picker}
      >
        <Picker.Item label="Select..." value="" />
        {items.map((item) => (
          <Picker.Item key={item.value} label={item.label} value={item.value} />
        ))}
      </Picker>
    </View>
  </View>
);

export default function EditProfileScreen() {
  const router = useRouter();
  const { user, updateUser } = useAuthStore();

  // Form state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profile, setProfile] = useState({
    firstName: "",
    lastName: "",
    phoneNumber: "",
    dateOfBirth: "",
    nationality: "",
    userType: [] as string[],
    employment: {
      occupation: "",
      employmentType: "",
      contractType: "",
      employer: "",
      workLocation: "",
      monthlyIncome: "",
    },
  });

  const [preferences, setPreferences] = useState({
    minPrice: "",
    maxPrice: "",
    minRooms: "",
    maxRooms: "",
    propertyTypes: [] as string[],
    preferredLocations: [] as string[],
  });

  const [notifications, setNotifications] = useState({
    email: {
      newListings: true,
      priceChanges: true,
      applicationUpdates: true,
    },
    push: {
      newMatches: true,
      messages: true,
      systemUpdates: false,
    },
  });

  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  // Real-time field validation
  const validateField = (fieldName: string, value: string) => {
    let error = "";

    switch (fieldName) {
      case "firstName":
        if (!value.trim()) {
          error = "First name is required";
        }
        break;

      case "lastName":
        if (!value.trim()) {
          error = "Last name is required";
        }
        break;

      case "phoneNumber":
        if (value.trim()) {
          const phoneRegex = /^\+?[1-9]\d{1,14}$/;
          if (!phoneRegex.test(value.replace(/\s/g, ""))) {
            error = "Please enter a valid phone number";
          }
        }
        break;

      case "dateOfBirth":
        if (value.trim()) {
          const dateValidation = validateEuropeanDate(value);
          if (!dateValidation.isValid) {
            error = dateValidation.error || "Invalid date";
          }
        }
        break;

      case "monthlyIncome":
        if (value.trim()) {
          const income = parseFloat(value);
          if (isNaN(income) || income < 0) {
            error = "Must be a valid positive number";
          }
        }
        break;

      case "minPrice":
      case "maxPrice":
        if (value.trim()) {
          const price = parseFloat(value);
          if (isNaN(price) || price < 0) {
            error = "Must be a valid positive number";
          }
        }
        break;

      case "minRooms":
      case "maxRooms":
        if (value.trim()) {
          const rooms = parseInt(value);
          if (isNaN(rooms) || rooms < 1) {
            error = "Must be at least 1";
          }
        }
        break;
    }

    setFieldErrors((prev) => ({
      ...prev,
      [fieldName]: error,
    }));
  };

  // Load current profile data
  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const response = await userProfileService.getProfile();

      if (response.success && response.data) {
        const userData = response.data;

        // Set profile data
        setProfile({
          firstName: userData.profile?.firstName || "",
          lastName: userData.profile?.lastName || "",
          phoneNumber: userData.profile?.phoneNumber || "",
          dateOfBirth: userData.profile?.dateOfBirth
            ? formatDateToEuropean(userData.profile.dateOfBirth)
            : "",
          nationality: userData.profile?.nationality || "",
          userType: userData.profile?.userType || [],
          employment: {
            occupation: userData.profile?.employment?.occupation || "",
            employmentType: userData.profile?.employment?.employmentType || "",
            contractType: userData.profile?.employment?.contractType || "",
            employer: userData.profile?.employment?.employer || "",
            workLocation: userData.profile?.employment?.workLocation || "",
            monthlyIncome:
              userData.profile?.employment?.monthlyIncome?.toString() || "",
          },
        });

        // Set preferences
        setPreferences({
          minPrice: userData.preferences?.minPrice?.toString() || "",
          maxPrice: userData.preferences?.maxPrice?.toString() || "",
          minRooms: userData.preferences?.minRooms?.toString() || "",
          maxRooms: userData.preferences?.maxRooms?.toString() || "",
          propertyTypes: userData.preferences?.propertyTypes || [],
          preferredLocations: userData.preferences?.preferredLocations || [],
        });

        // Set notifications
        setNotifications({
          email: {
            newListings: userData.notifications?.email?.newListings ?? true,
            priceChanges: userData.notifications?.email?.priceChanges ?? true,
            applicationUpdates:
              userData.notifications?.email?.applicationUpdates ?? true,
          },
          push: {
            newMatches: userData.notifications?.push?.newMatches ?? true,
            messages: userData.notifications?.push?.messages ?? true,
            systemUpdates: userData.notifications?.push?.systemUpdates ?? false,
          },
        });
      }
    } catch (error) {
      console.error("Error loading profile:", error);
      Alert.alert("Error", "Failed to load profile data");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Comprehensive form validation
      const validation = validateProfile(profile, preferences);
      if (!validation.isValid) {
        Alert.alert(
          "Please Fix the Following Issues",
          validation.errors.join("\n\n"),
          [{ text: "OK", style: "default" }]
        );
        return;
      }

      // Clean and prepare data for API
      const { profile: cleanedProfile, preferences: cleanedPreferences } =
        cleanProfileData(profile, preferences);

      const updateData = {
        profile: {
          ...cleanedProfile,
          dateOfBirth: cleanedProfile.dateOfBirth
            ? parseEuropeanDate(cleanedProfile.dateOfBirth)
            : undefined,
        },
        preferences: cleanedPreferences,
        notifications,
      };

      const response = await userProfileService.updateProfile(updateData);

      if (response.success) {
        // Clear any field errors on successful save
        setFieldErrors({});

        // Update the auth store with new user data
        const updatedUser = response.data;
        updateUser(updatedUser);

        // Also sync updated personal info into Auto-Application settings (best-effort)
        try {
          const userId = updatedUser?._id || updatedUser?.id;
          if (userId) {
            // Map employment type to auto-application employmentStatus
            const mapEmployment = (val?: string) => {
              switch ((val || "").toLowerCase()) {
                case "freelancer":
                  return "self-employed";
                case "student":
                  return "student";
                case "unemployed":
                  return "unemployed";
                case "full-time":
                case "part-time":
                  return "employed";
                default:
                  return undefined as any;
              }
            };

            const personalInfoUpdate: Partial<
              AutoApplicationSettings["personalInfo"]
            > = {
              fullName:
                [profile.firstName?.trim(), profile.lastName?.trim()]
                  .filter(Boolean)
                  .join(" ") ||
                [updatedUser?.firstName, updatedUser?.lastName]
                  .filter(Boolean)
                  .join(" "),
              email: updatedUser?.email,
              phone: profile.phoneNumber?.trim() || "+31 6 00000000",
              dateOfBirth: profile.dateOfBirth
                ? parseEuropeanDate(profile.dateOfBirth) ||
                  new Date("1990-01-01")
                : new Date("1990-01-01"),
              nationality: profile.nationality || "Dutch",
              occupation: profile.employment.occupation || "Professional",
              employer: profile.employment.employer || "To be specified",
              monthlyIncome: profile.employment.monthlyIncome
                ? parseFloat(profile.employment.monthlyIncome)
                : 3000,
              moveInDate: new Date(),
              leaseDuration: 12,
              emergencyContact: {
                name: "Emergency Contact",
                phone: "+31 6 00000000",
                relationship: "Family",
              },
              employmentStatus: mapEmployment(
                profile.employment.employmentType
              ),
            } as any;

            // Remove undefined keys to avoid overwriting with undefined
            Object.keys(personalInfoUpdate).forEach((k) => {
              const key = k as keyof typeof personalInfoUpdate;
              if (
                personalInfoUpdate[key] === undefined ||
                personalInfoUpdate[key] === null
              ) {
                delete personalInfoUpdate[key];
              }
            });

            if (Object.keys(personalInfoUpdate).length > 0) {
              // Fetch current settings, merge, then PUT full document to avoid PUT-vs-PATCH issues
              try {
                const current = await autoApplicationService.getSettings(
                  userId
                );
                if (current.success && current.data) {
                  const mergedDoc: AutoApplicationSettings = {
                    ...current.data,
                    personalInfo: {
                      ...current.data.personalInfo,
                      ...personalInfoUpdate,
                    } as any,
                    userId: userId,
                  } as any;
                  await autoApplicationService.updateSettings(
                    userId,
                    mergedDoc
                  );
                } else {
                  // Fallback: construct a minimal complete document if server requires full PUT body
                  const fallbackDoc: Partial<AutoApplicationSettings> = {
                    userId: userId,
                    enabled: false,
                    settings: {
                      maxApplicationsPerDay: 3,
                      applicationTemplate: "professional",
                      autoSubmit: false,
                      requireManualReview: true,
                      notificationPreferences: {
                        immediate: true,
                        daily: true,
                        weekly: false,
                      },
                      language: "english",
                    },
                    criteria: {
                      maxPrice: 2000,
                      minRooms: 1,
                      maxRooms: 5,
                      propertyTypes: ["apartment", "house"],
                      locations: [],
                      excludeKeywords: [],
                      includeKeywords: [],
                      minSize: 30,
                      maxSize: 150,
                    },
                    personalInfo: personalInfoUpdate as any,
                  };
                  await autoApplicationService.updateSettings(
                    userId,
                    fallbackDoc as any
                  );
                }
              } catch (putErr) {
                console.warn("Auto-Application settings PUT failed:", putErr);
              }
            }
          }
        } catch (syncErr) {
          console.warn("Auto-Application personal info sync failed:", syncErr);
        }

        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        Alert.alert("Success", "Profile updated successfully", [
          { text: "OK", onPress: () => router.back() },
        ]);
      } else {
        throw new Error(response.error || "Failed to update profile");
      }
    } catch (error: any) {
      console.error("Error saving profile:", error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);

      const errorMessage = parseApiError(error);
      Alert.alert("Unable to Save Profile", errorMessage, [
        { text: "OK", style: "default" },
      ]);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <Header />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={THEME.primary} />
          <Text style={styles.loadingText}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header />

      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Personal Information Section */}
          <Animated.View
            style={styles.section}
            entering={FadeInUp.duration(600).delay(200)}
          >
            <Text style={styles.sectionTitle}>Personal Information</Text>

            <InputField
              label="First Name"
              value={profile.firstName}
              onChangeText={(text) => {
                setProfile({ ...profile, firstName: text });
                validateField("firstName", text);
              }}
              placeholder="Enter your first name"
              error={fieldErrors.firstName}
            />

            <InputField
              label="Last Name"
              value={profile.lastName}
              onChangeText={(text) => {
                setProfile({ ...profile, lastName: text });
                validateField("lastName", text);
              }}
              placeholder="Enter your last name"
              error={fieldErrors.lastName}
            />

            <InputField
              label="Phone Number"
              value={profile.phoneNumber}
              onChangeText={(text) => {
                setProfile({ ...profile, phoneNumber: text });
                validateField("phoneNumber", text);
              }}
              placeholder="+31 6 12345678"
              keyboardType="phone-pad"
              error={fieldErrors.phoneNumber}
            />

            <InputField
              label="Date of Birth"
              value={profile.dateOfBirth}
              onChangeText={(text) => {
                setProfile({ ...profile, dateOfBirth: text });
                validateField("dateOfBirth", text);
              }}
              placeholder="DD/MM/YYYY"
              error={fieldErrors.dateOfBirth}
            />
            <Text style={styles.helperText}>
              Enter your date of birth in European format (DD/MM/YYYY)
            </Text>

            <InputField
              label="Nationality"
              value={profile.nationality}
              onChangeText={(text) =>
                setProfile({ ...profile, nationality: text })
              }
              placeholder="e.g., Dutch, German, American"
            />
          </Animated.View>

          {/* Employment Section */}
          <Animated.View
            style={styles.section}
            entering={FadeInUp.duration(600).delay(400)}
          >
            <Text style={styles.sectionTitle}>Employment</Text>

            <InputField
              label="Occupation"
              value={profile.employment.occupation}
              onChangeText={(text) =>
                setProfile({
                  ...profile,
                  employment: { ...profile.employment, occupation: text },
                })
              }
              placeholder="e.g., Software Engineer, Student"
            />

            <PickerField
              label="Employment Type"
              value={profile.employment.employmentType}
              onValueChange={(value) =>
                setProfile({
                  ...profile,
                  employment: { ...profile.employment, employmentType: value },
                })
              }
              items={[
                { label: "Full-time", value: "full-time" },
                { label: "Part-time", value: "part-time" },
                { label: "Student", value: "student" },
                { label: "Freelancer", value: "freelancer" },
                { label: "Unemployed", value: "unemployed" },
              ]}
            />

            <PickerField
              label="Contract Type"
              value={profile.employment.contractType}
              onValueChange={(value) =>
                setProfile({
                  ...profile,
                  employment: { ...profile.employment, contractType: value },
                })
              }
              items={[
                { label: "Permanent", value: "permanent" },
                { label: "Temporary", value: "temporary" },
                { label: "Student", value: "student" },
                { label: "Freelancer", value: "freelancer" },
              ]}
            />

            <InputField
              label="Employer"
              value={profile.employment.employer}
              onChangeText={(text) =>
                setProfile({
                  ...profile,
                  employment: { ...profile.employment, employer: text },
                })
              }
              placeholder="Company name"
            />

            <InputField
              label="Work Location"
              value={profile.employment.workLocation}
              onChangeText={(text) =>
                setProfile({
                  ...profile,
                  employment: { ...profile.employment, workLocation: text },
                })
              }
              placeholder="City or remote"
            />

            <InputField
              label="Monthly Income (€)"
              value={profile.employment.monthlyIncome}
              onChangeText={(text) => {
                setProfile({
                  ...profile,
                  employment: { ...profile.employment, monthlyIncome: text },
                });
                validateField("monthlyIncome", text);
              }}
              placeholder="3000"
              keyboardType="numeric"
              error={fieldErrors.monthlyIncome}
            />
          </Animated.View>

          {/* Housing Preferences Section */}
          <Animated.View
            style={styles.section}
            entering={FadeInUp.duration(600).delay(600)}
          >
            <Text style={styles.sectionTitle}>Housing Preferences</Text>

            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <InputField
                  label="Min Price (€)"
                  value={preferences.minPrice}
                  onChangeText={(text) => {
                    setPreferences({ ...preferences, minPrice: text });
                    validateField("minPrice", text);
                  }}
                  placeholder="800"
                  keyboardType="numeric"
                  error={fieldErrors.minPrice}
                />
              </View>
              <View style={styles.halfWidth}>
                <InputField
                  label="Max Price (€)"
                  value={preferences.maxPrice}
                  onChangeText={(text) => {
                    setPreferences({ ...preferences, maxPrice: text });
                    validateField("maxPrice", text);
                  }}
                  placeholder="1500"
                  keyboardType="numeric"
                  error={fieldErrors.maxPrice}
                />
              </View>
            </View>

            <View style={styles.row}>
              <View style={styles.halfWidth}>
                <InputField
                  label="Min Rooms"
                  value={preferences.minRooms}
                  onChangeText={(text) => {
                    setPreferences({ ...preferences, minRooms: text });
                    validateField("minRooms", text);
                  }}
                  placeholder="1"
                  keyboardType="numeric"
                  error={fieldErrors.minRooms}
                />
              </View>
              <View style={styles.halfWidth}>
                <InputField
                  label="Max Rooms"
                  value={preferences.maxRooms}
                  onChangeText={(text) => {
                    setPreferences({ ...preferences, maxRooms: text });
                    validateField("maxRooms", text);
                  }}
                  placeholder="3"
                  keyboardType="numeric"
                  error={fieldErrors.maxRooms}
                />
              </View>
            </View>
          </Animated.View>

          {/* Save Button */}
          <Animated.View
            style={styles.saveButtonContainer}
            entering={FadeInDown.duration(600).delay(800)}
          >
            <TouchableOpacity
              style={[styles.saveButton, saving && styles.saveButtonDisabled]}
              onPress={handleSave}
              disabled={saving}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={
                  saving
                    ? [THEME.gray, THEME.gray]
                    : [THEME.primary, THEME.secondary]
                }
                style={styles.saveButtonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                {saving ? (
                  <ActivityIndicator size="small" color={THEME.light} />
                ) : (
                  <Ionicons name="checkmark" size={20} color={THEME.light} />
                )}
                <Text style={styles.saveButtonText}>
                  {saving ? "Saving..." : "Save Profile"}
                </Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>

          {/* Bottom Spacing */}
          <View style={styles.bottomSpacing} />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  keyboardAvoid: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  backButton: {
    marginRight: 16,
  },
  backButtonInner: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerCenter: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  logoContainer: {
    width: 40,
    height: 40,
    backgroundColor: THEME.light,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoText: {
    fontSize: 18,
    fontWeight: "bold",
    color: THEME.primary,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: THEME.light,
  },
  headerSpacer: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: THEME.gray,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
  },
  section: {
    backgroundColor: THEME.light,
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: THEME.dark,
    marginBottom: 20,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: THEME.dark,
    marginBottom: 8,
  },
  helperText: {
    fontSize: 12,
    color: THEME.gray,
    marginTop: -12,
    marginBottom: 16,
    fontStyle: "italic",
  },
  errorText: {
    fontSize: 12,
    color: THEME.accent,
    marginTop: 4,
    marginBottom: 8,
    fontWeight: "500",
  },
  textInputError: {
    borderColor: THEME.accent,
    borderWidth: 1,
  },
  textInput: {
    backgroundColor: THEME.lightGray,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: THEME.dark,
    borderWidth: 1,
    borderColor: "transparent",
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: "top",
  },
  disabledInput: {
    backgroundColor: "#f0f0f0",
    color: THEME.gray,
  },
  pickerContainer: {
    backgroundColor: THEME.lightGray,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "transparent",
  },
  picker: {
    height: 50,
  },
  row: {
    flexDirection: "row",
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  saveButtonContainer: {
    marginTop: 20,
  },
  saveButton: {
    borderRadius: 25,
    overflow: "hidden",
    shadowColor: THEME.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonGradient: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 32,
    paddingVertical: 16,
    gap: 8,
  },
  saveButtonText: {
    color: THEME.light,
    fontSize: 18,
    fontWeight: "bold",
  },
  bottomSpacing: {
    height: 40,
  },
});
