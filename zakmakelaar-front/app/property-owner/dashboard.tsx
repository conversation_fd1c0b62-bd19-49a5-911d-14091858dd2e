import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, Alert, TouchableOpacity, RefreshControl } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useRouter, useNavigation } from 'expo-router';
import type { NavigationProp } from '@react-navigation/native';
import { propertyOwnerService } from '../../services/propertyOwnerService';
import { PropertyCard } from '../../components/property-owner/PropertyCard';

// Global type declaration for __DEV__
declare const __DEV__: boolean;

// Type guard function to check if data is BackendProperty array
const isBackendPropertyArray = (data: unknown): data is BackendProperty[] => {
  return Array.isArray(data) && data.every(item =>
    typeof item === 'object' &&
    item !== null &&
    '_id' in item &&
    'title' in item &&
    'address' in item
  );
};

// Define types for backend property data
interface BackendProperty {
  _id: string;
  title: string;
  address: {
    street: string;
    houseNumber: string;
    postalCode: string;
    city: string;
    province?: string;
    country: string;
  };
  propertyType: string;
  bedrooms: number;
  bathrooms: number;
  rent: {
    amount: number;
    currency: string;
  };
  size: number;
  images: Array<{
    url: string;
    caption?: string;
    isPrimary: boolean;
  }>;
  status: 'draft' | 'active' | 'rented' | 'maintenance' | 'inactive';
  metrics: {
    views: number;
    applications: number;
    viewings: number;
  };
}

// Define types for frontend property data (after transformation)
interface Property {
  _id: string;
  id: string;
  title: string;
  address: string; // Transformed to string
  city: string;
  type: string;
  bedrooms: number;
  bathrooms: number;
  price: number;
  size: number;
  images: string[]; // Transformed to string array
  status: 'draft' | 'active' | 'rented' | 'maintenance' | 'inactive';
  occupancyStatus: string;
  applicationsCount: number;
  imageUrl: string;
}

export default function PropertyOwnerDashboard() {
  // We'll use router later for navigation between property owner screens
  const router = useRouter();
  const navigation = useNavigation<NavigationProp<any>>();
  const [loading, setLoading] = useState(true);
  const [properties, setProperties] = useState<Property[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Configure navigation options
  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
    });
  }, [navigation]);

  useEffect(() => {
    fetchProperties();
  }, []);

  const fetchProperties = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('🔄 Fetching properties from backend...');
      const response = await propertyOwnerService.getProperties();

      console.log('📊 Properties response:', {
        success: response.success,
        dataLength: Array.isArray(response.data) ? response.data.length : 0,
        hasData: !!response.data
      });

      if (response.success && response.data && isBackendPropertyArray(response.data)) {
        // Transform backend data to match frontend expectations
        const transformedProperties: Property[] = response.data.map((backendProperty: BackendProperty) => {
          console.log('🏠 Transforming property:', backendProperty.title);

          // Create address string from address object
          const addressString = backendProperty.address ?
            `${backendProperty.address.street} ${backendProperty.address.houseNumber}, ${backendProperty.address.city}` :
            'Address not available';

          const transformedProperty: Property = {
            _id: backendProperty._id,
            id: backendProperty._id,
            title: backendProperty.title,
            address: addressString, // Convert address object to string
            city: backendProperty.address?.city || 'Unknown City',
            type: backendProperty.propertyType ?
              backendProperty.propertyType.charAt(0).toUpperCase() + backendProperty.propertyType.slice(1) :
              'Unknown Type',
            bedrooms: backendProperty.bedrooms,
            bathrooms: backendProperty.bathrooms,
            price: backendProperty.rent?.amount || 0,
            size: backendProperty.size,
            status: backendProperty.status,
            occupancyStatus: getOccupancyStatus(backendProperty.status),
            applicationsCount: backendProperty.metrics?.applications || 0,
            images: backendProperty.images?.map(img => img.url) || [], // Convert image objects to URL array
            imageUrl: backendProperty.images?.find(img => img.isPrimary)?.url ||
              backendProperty.images?.[0]?.url ||
              'https://via.placeholder.com/300x200?text=No+Image'
          };

          return transformedProperty;
        });

        console.log('✅ Properties transformed:', transformedProperties.length);
        setProperties(transformedProperties);
      } else {
        const errorMessage = response.error || response.message || 'Failed to fetch properties';
        console.error('❌ Properties fetch failed:', errorMessage);
        setError(errorMessage);
      }
    } catch (err: any) {
      console.error('❌ Error fetching properties:', err);
      setError(err.message || 'Failed to fetch properties');
    } finally {
      setLoading(false);
    }
  };

  const getOccupancyStatus = (status: string) => {
    switch (status) {
      case 'active':
        return 'Vacant';
      case 'rented':
        return 'Rented';
      case 'maintenance':
        return 'Under Maintenance';
      case 'draft':
        return 'Draft';
      case 'inactive':
        return 'Inactive';
      default:
        return 'Unknown';
    }
  };



  const handlePropertyPress = (propertyId: string) => {
    // Navigate to property details screen
    router.push(`/property-owner/property-details?propertyId=${propertyId}`);
  };

  const handleActivateProperty = async (propertyId: string) => {
    setActionLoading(propertyId);

    try {
      console.log('🟢 Activating property:', propertyId);

      const response = await propertyOwnerService.activateProperty(propertyId);

      if (response.status === 'success') {
        console.log('✅ Property activated successfully');

        // Update the property status in local state
        setProperties(prevProperties =>
          prevProperties.map(property =>
            property.id === propertyId
              ? { ...property, status: 'active', occupancyStatus: 'Vacant' }
              : property
          )
        );

        // Show success message with more details
        Alert.alert(
          'Property Published! 🎉',
          'Your property is now live and available for applications. Tenants can find and apply to your property.',
          [{ text: 'Great!' }]
        );
      } else {
        throw new Error(response.message || 'Failed to activate property');
      }
    } catch (error: any) {
      console.error('❌ Error activating property:', error);

      let errorMessage = 'Failed to activate property. Please try again.';
      let showEditOption = false;

      if (error.code === 'VALIDATION_ERROR') {
        errorMessage = error.message || 'Property information is incomplete. Please review and update your property details.';
        showEditOption = true;
      } else if (error.code === 'UNAUTHORIZED') {
        errorMessage = 'Authentication required. Please log in again.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      const buttons: Array<{ text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }> = [{ text: 'OK' }];
      if (showEditOption) {
        buttons.unshift({
          text: 'Edit Property',
          onPress: () => router.push(`/property-owner/edit-property?propertyId=${propertyId}`)
        });
      }

      Alert.alert('Activation Failed', errorMessage, buttons);
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeactivateProperty = async (propertyId: string) => {
    Alert.alert(
      'Pause Property Listing',
      'This will temporarily remove your property from public listings. You can reactivate it anytime.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Pause Listing',
          style: 'destructive',
          onPress: async () => {
            setActionLoading(propertyId);

            try {
              console.log('🟡 Deactivating property:', propertyId);

              const response = await propertyOwnerService.deactivateProperty(propertyId);

              if (response.status === 'success') {
                console.log('✅ Property deactivated successfully');

                // Update the property status in local state
                setProperties(prevProperties =>
                  prevProperties.map(property =>
                    property.id === propertyId
                      ? { ...property, status: 'inactive', occupancyStatus: 'Inactive' }
                      : property
                  )
                );

                // Show success message
                Alert.alert(
                  'Property Paused',
                  'Your property has been removed from public listings. You can reactivate it anytime.',
                  [{ text: 'OK' }]
                );
              } else {
                throw new Error(response.message || 'Failed to deactivate property');
              }
            } catch (error: any) {
              console.error('❌ Error deactivating property:', error);
              Alert.alert(
                'Deactivation Failed',
                error.message || 'Failed to pause property listing. Please try again.',
                [{ text: 'OK' }]
              );
            } finally {
              setActionLoading(null);
            }
          }
        }
      ]
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    console.log('🔄 Refreshing properties...');
    await fetchProperties();
    setRefreshing(false);
  };

  const handleRetry = () => {
    console.log('🔄 Retrying property fetch...');
    fetchProperties();
  };

  const handleAddProperty = () => {
    // Navigate to add property tab
    router.push('/property-owner/add-property');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Ionicons name="home" size={26} color="#007AFF" />
          <Text style={styles.title}>Dashboard</Text>
        </View>
        {__DEV__ && (
          <View style={styles.headerButtons}>
            <TouchableOpacity style={styles.headerButton} onPress={handleRetry}>
              <Ionicons name="refresh" size={20} color="#007AFF" />
              <Text style={styles.buttonText}>Refresh</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Owner status card */}
      <View style={styles.statusCard}>
        <View style={styles.statusHeader}>
          <Ionicons name="person-circle" size={28} color="#007AFF" />
          <Text style={styles.welcomeText}>Welcome, Property Owner</Text>
        </View>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{properties.length}</Text>
            <Text style={styles.statLabel}>Total</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{properties.filter(p => p.status === 'active').length}</Text>
            <Text style={styles.statLabel}>Active</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{properties.filter(p => p.status === 'draft').length}</Text>
            <Text style={styles.statLabel}>Draft</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{properties.reduce((sum, p) => sum + (p.applicationsCount || 0), 0)}</Text>
            <Text style={styles.statLabel}>Applications</Text>
          </View>
        </View>
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading properties...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={64} color="#FF3B30" />
          <Text style={styles.errorText}>{error}</Text>
          <Text style={styles.errorSubtext}>Please check your internet connection and try again</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRetry}>
            <Ionicons name="refresh" size={20} color="#FFFFFF" />
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      ) : properties.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Ionicons name="home-outline" size={64} color="#CCCCCC" />
          <Text style={styles.emptyText}>No properties found</Text>
          <Text style={styles.emptySubtext}>Add your first property to get started</Text>
          <TouchableOpacity style={styles.addPropertyButton} onPress={handleAddProperty}>
            <Text style={styles.addPropertyButtonText}>Add Property</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.propertiesContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          <Text style={styles.sectionTitle}>Your Properties</Text>
          {properties.map(property => (
            <PropertyCard
              key={property.id}
              property={property}
              onPress={() => handlePropertyPress(property.id)}
              onEdit={() => router.push(`/property-owner/edit-property?propertyId=${property.id}`)}
              onActivate={() => handleActivateProperty(property.id)}
              onDeactivate={() => handleDeactivateProperty(property.id)}
              isLoading={actionLoading === property.id}
            />
          ))}


        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
    backgroundColor: '#FFFFFF',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    minHeight: 60,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1A1A1A',
    marginLeft: 10,
    letterSpacing: 0.3,
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerButton: {
    flexDirection: 'column',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#F8F9FA',
    minWidth: 70,
  },
  buttonText: {
    marginTop: 4,
    color: '#007AFF',
    fontWeight: '600',
    fontSize: 12,
    textAlign: 'center',
  },
  debugCard: {
    margin: 16,
    marginBottom: 8,
    padding: 12,
    backgroundColor: '#FFF3CD',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FFEAA7',
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#856404',
    marginBottom: 4,
  },
  debugText: {
    fontSize: 12,
    color: '#856404',
    fontFamily: 'monospace',
  },
  statusCard: {
    margin: 16,
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  welcomeText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 8,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  statLabel: {
    fontSize: 14,
    color: '#666666',
    marginTop: 4,
  },
  scrollContainer: {
    flex: 1,
  },
  propertiesContainer: {
    padding: 16,
    paddingBottom: 100, // Extra padding for tab bar
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '600',
  },
  errorSubtext: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#007AFF',
    borderRadius: 8,
    gap: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666666',
    marginTop: 8,
    textAlign: 'center',
  },
  addPropertyButton: {
    marginTop: 24,
    paddingHorizontal: 20,
    paddingVertical: 12,
    backgroundColor: '#007AFF',
    borderRadius: 8,
  },
  addPropertyButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },

});
