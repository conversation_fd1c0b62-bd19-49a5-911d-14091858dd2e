import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  Switch,
  ActivityIndicator,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { Ionicons } from '@expo/vector-icons';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { propertyOwnerService, PropertyData } from '../../services/propertyOwnerService';
import { authService } from '../../services/authService';
import { API_BASE_URL } from '../../config/api';
import { PropertyStatusBadge } from '../../components/property-owner/PropertyStatusBadge';
import { PropertyValidation } from '../../components/property-owner/PropertyValidation';
import { PropertyFormSteps } from '../../components/property-owner/PropertyFormSteps';

// Form data interface (same as add-property)
interface PropertyFormData {
  title: string;
  description: string;
  address: {
    street: string;
    houseNumber: string;
    postalCode: string;
    city: string;
    province: string;
  };
  propertyType: 'apartment' | 'house' | 'studio' | 'room';
  size: string;
  rooms: string;
  bedrooms: string;
  bathrooms: string;
  rent: {
    amount: string;
    deposit: string;
    utilities: string;
    serviceCharges: string;
  };
  features: {
    furnished: boolean;
    interior: 'kaal' | 'gestoffeerd' | 'gemeubileerd';
    parking: boolean;
    balcony: boolean;
    garden: boolean;
    elevator: boolean;
    energyLabel: string;
  };
  policies: {
    petsAllowed: boolean;
    smokingAllowed: boolean;
    studentsAllowed: boolean;
    expatFriendly: boolean;
    minimumIncome: string;
    maximumOccupants: string;
  };
  photos: {
    uri: string;
    name: string;
    type: string;
    isPrimary: boolean;
  }[];
}

export default function EditPropertyScreen() {
  const router = useRouter();
  const { propertyId } = useLocalSearchParams<{ propertyId: string }>();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  const [resetSteps, setResetSteps] = useState(false);
  const totalSteps = 5;
  const [formData, setFormData] = useState<PropertyFormData>({
    title: '',
    description: '',
    address: {
      street: '',
      houseNumber: '',
      postalCode: '',
      city: '',
      province: '',
    },
    propertyType: 'apartment',
    size: '',
    rooms: '',
    bedrooms: '',
    bathrooms: '',
    rent: {
      amount: '',
      deposit: '',
      utilities: '',
      serviceCharges: '',
    },
    features: {
      furnished: false,
      interior: 'kaal',
      parking: false,
      balcony: false,
      garden: false,
      elevator: false,
      energyLabel: 'C',
    },
    policies: {
      petsAllowed: false,
      smokingAllowed: false,
      studentsAllowed: true,
      expatFriendly: true,
      minimumIncome: '',
      maximumOccupants: '',
    },
    photos: [],
  });

  // Reset property edit steps to step 1
  useEffect(() => {
    setCurrentStep(1);
  }, [resetSteps])
  // Load existing property data
  useEffect(() => {

    const loadPropertyData = async () => {
      if (!propertyId) {
        Alert.alert('Error', 'Property ID is required');
        router.back();
        return;
      }
      setInitialLoading(true);
      try {
        const response = await propertyOwnerService.getPropertyDetails(propertyId);

        if (response.success && response.data) {
          const property = response.data as PropertyData;

          // Transform property data to form data format
          setFormData({
            title: property.title || '',
            description: property.description || '',
            address: {
              street: property.address?.street || '',
              houseNumber: property.address?.houseNumber || '',
              postalCode: property.address?.postalCode || '',
              city: property.address?.city || '',
              province: property.address?.province || '',
            },
            propertyType: property.propertyType as any || 'apartment',
            size: property.size?.toString() || '',
            rooms: property.rooms?.toString() || '',
            bedrooms: property.bedrooms?.toString() || '',
            bathrooms: property.bathrooms?.toString() || '',
            rent: {
              amount: property.rent?.amount?.toString() || '',
              deposit: property.rent?.deposit?.toString() || '',
              utilities: property.rent?.additionalCosts?.utilities?.toString() || '',
              serviceCharges: property.rent?.additionalCosts?.serviceCharges?.toString() || '',
            },
            features: {
              furnished: property.features?.furnished || false,
              interior: property.features?.interior as any || 'kaal',
              parking: property.features?.parking || false,
              balcony: property.features?.balcony || false,
              garden: property.features?.garden || false,
              elevator: property.features?.elevator || false,
              energyLabel: property.features?.energyLabel || 'C',
            },
            policies: {
              petsAllowed: property.policies?.petsAllowed || false,
              smokingAllowed: property.policies?.smokingAllowed || false,
              studentsAllowed: property.policies?.studentsAllowed || true,
              expatFriendly: property.policies?.expatFriendly || true,
              minimumIncome: property.policies?.minimumIncome?.toString() || '',
              maximumOccupants: property.policies?.maximumOccupants?.toString() || '',
            },
            photos: property.images?.map((img: any, index: number) => ({
              uri: img.url,
              name: `existing_photo_${index}.jpg`,
              type: 'image/jpeg',
              isPrimary: img.isPrimary || false,
            })) || [],
          });
        } else {
          // Fallback to mock data for demonstration
          setFormData({
            title: 'Modern Apartment in Utrecht',
            description: 'Beautiful modern apartment in the heart of Utrecht with excellent public transport connections.',
            address: {
              street: 'Main Street',
              houseNumber: '123',
              postalCode: '3511AB',
              city: 'Utrecht',
              province: 'Utrecht',
            },
            propertyType: 'apartment',
            size: '75',
            rooms: '3',
            bedrooms: '2',
            bathrooms: '1',
            rent: {
              amount: '1200',
              deposit: '2400',
              utilities: '150',
              serviceCharges: '50',
            },
            features: {
              furnished: false,
              interior: 'gestoffeerd',
              parking: false,
              balcony: true,
              garden: false,
              elevator: true,
              energyLabel: 'B',
            },
            policies: {
              petsAllowed: false,
              smokingAllowed: false,
              studentsAllowed: true,
              expatFriendly: true,
              minimumIncome: '3600',
              maximumOccupants: '2',
            },
            photos: [
              {
                uri: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                name: 'existing_photo_0.jpg',
                type: 'image/jpeg',
                isPrimary: true,
              },
            ],
          });
        }
      } catch (error: any) {
        Alert.alert('Error', 'Failed to load property data: ' + error.message);
        router.back();
      } finally {
        setInitialLoading(false);
      }
    };

    loadPropertyData();
    debugAuth();
  }, [propertyId, router]);

  // Debug authentication status
  const debugAuth = async () => {
    console.log('=== AUTH DEBUG ===');

    try {
      // Check if user is authenticated
      const isAuth = await authService.isAuthenticated();
      console.log('Is Authenticated:', isAuth);

      // Get current user
      const userResponse = await authService.getCurrentUser();
      console.log('Current User Response:', userResponse);

      // Get auth token
      const token = await authService.getAuthToken();
      console.log('Auth Token:', token ? `${token.substring(0, 20)}...` : 'No token');

      // Extract user data from response
      const userData = userResponse?.success ? userResponse.data : null;
      if (userData) {
        console.log('User Role:', userData.role);
        console.log('Property Owner Object:', userData.propertyOwner);
        console.log('Is Property Owner:', userData.role === 'owner' && userData.propertyOwner?.isPropertyOwner);
      }

      if (!isAuth) {
        console.log('❌ User not authenticated - redirecting to login');
        Alert.alert(
          'Authentication Required',
          'Please log in to edit properties.',
          [{ text: 'Login', onPress: () => router.push('/login') }]
        );
      } else if (!userData || userData.role !== 'owner' || !userData.propertyOwner?.isPropertyOwner) {
        console.log('❌ User not a property owner - redirecting to registration');
        Alert.alert(
          'Property Owner Registration Required',
          'You need to register as a property owner to edit properties.',
          [{ text: 'Register', onPress: () => router.push('/property-owner/verification') }]
        );
      } else {
        console.log('✅ User authenticated and authorized');
      }

    } catch (error) {
      console.log('Auth debug error:', error);
    }
  };

  const updateFormData = (section: keyof PropertyFormData, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...(prev[section] as any),
        [field]: value,
      },
    }));
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1: // Basic Information
        return !!(formData.title && formData.description && formData.propertyType);
      case 2: // Address & Details
        return !!(
          formData.address.street &&
          formData.address.houseNumber &&
          formData.address.postalCode &&
          formData.address.city &&
          formData.size &&
          formData.rooms &&
          formData.bedrooms &&
          formData.bathrooms
        );
      case 3: // Rental Information
        return !!(formData.rent.amount && formData.rent.deposit);
      case 4: // Features & Policies
        return true; // Optional fields
      case 5: // Photos
        return true; // Photos are optional but recommended
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    } else {
      Alert.alert('Incomplete Information', 'Please fill in all required fields before continuing.');
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Photo handling functions (same as add-property)
  const requestPermissions = async () => {
    const { status: cameraStatus } = await ImagePicker.requestCameraPermissionsAsync();
    const { status: mediaStatus } = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (cameraStatus !== 'granted' || mediaStatus !== 'granted') {
      Alert.alert(
        'Permissions Required',
        'We need camera and photo library permissions to add property photos.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const takePhoto = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newPhoto = {
          uri: result.assets[0].uri,
          name: `property_photo_${Date.now()}.jpg`,
          type: 'image/jpeg',
          isPrimary: formData.photos.length === 0, // First photo is primary
        };

        setFormData(prev => ({
          ...prev,
          photos: [...prev.photos, newPhoto],
        }));
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    }
  };

  const selectFromGallery = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
        allowsMultipleSelection: true,
      });

      if (!result.canceled && result.assets) {
        const newPhotos = result.assets.map((asset, index) => ({
          uri: asset.uri,
          name: `property_photo_${Date.now()}_${index}.jpg`,
          type: 'image/jpeg',
          isPrimary: formData.photos.length === 0 && index === 0, // First photo is primary if no photos exist
        }));

        setFormData(prev => ({
          ...prev,
          photos: [...prev.photos, ...newPhotos],
        }));
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select photos. Please try again.');
    }
  };

  const removePhoto = (index: number) => {
    Alert.alert(
      'Remove Photo',
      'Are you sure you want to remove this photo?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            setFormData(prev => {
              const newPhotos = prev.photos.filter((_, i) => i !== index);
              // If we removed the primary photo, make the first remaining photo primary
              if (prev.photos[index].isPrimary && newPhotos.length > 0) {
                newPhotos[0].isPrimary = true;
              }
              return { ...prev, photos: newPhotos };
            });
          },
        },
      ]
    );
  };

  const setPrimaryPhoto = (index: number) => {
    setFormData(prev => ({
      ...prev,
      photos: prev.photos.map((photo, i) => ({
        ...photo,
        isPrimary: i === index,
      })),
    }));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      Alert.alert('Incomplete Information', 'Please fill in all required fields.');
      return;
    }

    // Check authentication before proceeding
    const isAuth = await authService.isAuthenticated();
    const userResponse = await authService.getCurrentUser();
    const userData = userResponse?.success ? userResponse.data : null;

    if (!isAuth || !userData || userData.role !== 'owner' || !userData.propertyOwner?.isPropertyOwner) {
      Alert.alert(
        'Authentication Required',
        'You must be logged in as a property owner to update properties.',
        [{ text: 'OK', onPress: () => router.push('/login') }]
      );
      return;
    }

    setLoading(true);
    try {
      let imageUrls: { url: string; caption: string; isPrimary: boolean }[] = [];

      // Upload new images if any
      const newImages = formData.photos.filter(photo => !photo.uri.startsWith('http'));
      if (newImages.length > 0) {
        console.log('Uploading', newImages.length, 'new images...');
        const uploadResponse = await propertyOwnerService.uploadPropertyImages(
          propertyId || 'new',
          newImages
        );
        
        if (uploadResponse.status === 'success') {
          // Map uploaded images to the expected format
          const uploadedImages = uploadResponse.data.images.map((img: any, index: number) => ({
            url: `${API_BASE_URL}${img.url}`, // Full URL for the uploaded image
            caption: '',
            isPrimary: newImages[index].isPrimary
          }));
          imageUrls.push(...uploadedImages);
        }
      }

      // Keep existing images (those with http URLs)
      const existingImages = formData.photos
        .filter(photo => photo.uri.startsWith('http'))
        .map(photo => ({
          url: photo.uri,
          caption: '',
          isPrimary: photo.isPrimary,
        }));
      
      imageUrls.push(...existingImages);

      // Transform form data to match backend expectations
      const propertyData: PropertyData = {
        title: formData.title,
        description: formData.description,
        address: formData.address,
        propertyType: formData.propertyType,
        size: parseInt(formData.size) || 0,
        rooms: parseInt(formData.rooms) || 0,
        bedrooms: parseInt(formData.bedrooms) || 0,
        bathrooms: parseInt(formData.bathrooms) || 0,
        rent: {
          amount: parseFloat(formData.rent.amount) || 0,
          currency: 'EUR',
          deposit: parseFloat(formData.rent.deposit) || 0,
          additionalCosts: {
            utilities: parseFloat(formData.rent.utilities) || 0,
            serviceCharges: parseFloat(formData.rent.serviceCharges) || 0,
            parking: 0,
            other: 0,
          },
        },
        features: formData.features,
        policies: {
          ...formData.policies,
          minimumIncome: parseFloat(formData.policies.minimumIncome) || 0,
          maximumOccupants: parseInt(formData.policies.maximumOccupants) || 1,
        },
        status: 'draft',
        images: imageUrls,
        availabilityDate: new Date().toISOString().split('T')[0],
      };

      console.log('Updating property with', imageUrls.length, 'images...');

      const response = await propertyOwnerService.updateProperty(propertyId!, propertyData);

      if (response.data.success) {
        setResetSteps(prevStep => !prevStep);
        Alert.alert(
          'Success! 🎉',
          'Your property has been updated successfully.',
          [
            {
              text: 'OK',
              onPress: () => router.push('/property-owner/dashboard'),
            },
          ]
        );
      } else {
        Alert.alert('Error', response.error || 'Failed to update property. Please try again.');
      }
    } catch (error: any) {
      console.log('Update error caught:', error);
      console.log('Error details:', {
        message: error.message,
        code: error.code,
        status: error.status,
        stack: error.stack
      });

      // Provide more specific error messages
      let errorMessage = 'Failed to update property. Please try again.';

      if (error.code === 'UNAUTHORIZED' || error.status === 401) {
        errorMessage = 'Authentication required. Please log in again.';
      } else if (error.code === 'FORBIDDEN' || error.status === 403) {
        errorMessage = 'You do not have permission to update this property.';
      } else if (error.code === 'NOT_FOUND' || error.status === 404) {
        errorMessage = 'Property not found. It may have been deleted.';
      } else if (error.code === 'VALIDATION_ERROR' || error.status === 400) {
        errorMessage = 'Invalid property data. Please check all fields.';
      } else if (error.code === 'NETWORK_ERROR') {
        errorMessage = 'Network error. Please check your internet connection.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Render functions (same as add-property but with different title)
  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {Array.from({ length: totalSteps }, (_, index) => (
        <View key={index} style={styles.stepContainer}>
          <View
            style={[
              styles.stepCircle,
              index + 1 <= currentStep ? styles.stepCircleActive : styles.stepCircleInactive,
            ]}
          >
            <Text
              style={[
                styles.stepText,
                index + 1 <= currentStep ? styles.stepTextActive : styles.stepTextInactive,
              ]}
            >
              {index + 1}
            </Text>
          </View>
          {index < totalSteps - 1 && (
            <View
              style={[
                styles.stepLine,
                index + 1 < currentStep ? styles.stepLineActive : styles.stepLineInactive,
              ]}
            />
          )}
        </View>
      ))}
    </View>
  );

  const renderCurrentStep = () => {
    const stepProps = {
      formData,
      setFormData,
      updateFormData,
      takePhoto,
      selectFromGallery,
      removePhoto,
      setPrimaryPhoto,
    };

    switch (currentStep) {
      case 1:
        return <PropertyFormSteps.Step1 {...stepProps} />;
      case 2:
        return <PropertyFormSteps.Step2 {...stepProps} />;
      case 3:
        return <PropertyFormSteps.Step3 {...stepProps} />;
      case 4:
        return <PropertyFormSteps.Step4 {...stepProps} />;
      case 5:
        return <PropertyFormSteps.Step5 {...stepProps} />;
      default:
        return <PropertyFormSteps.Step1 {...stepProps} />;
    }
  };

  if (initialLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color="#007AFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Property</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading property data...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Property</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Step Indicator */}
      {renderStepIndicator()}

      {/* Form Content */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderCurrentStep()}
      </ScrollView>

      {/* Navigation Buttons */}
      <View style={styles.navigationContainer}>
        {currentStep > 1 && (
          <TouchableOpacity style={styles.secondaryButton} onPress={prevStep}>
            <Text style={styles.secondaryButtonText}>Previous</Text>
          </TouchableOpacity>
        )}

        <View style={styles.buttonSpacer} />

        {currentStep < totalSteps ? (
          <TouchableOpacity style={styles.primaryButton} onPress={nextStep}>
            <Text style={styles.primaryButtonText}>Next</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.primaryButton, loading && styles.primaryButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <Text style={styles.primaryButtonText}>Update Property</Text>
            )}
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
}

// Styles (same as add-property)
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666666',
  },
  stepIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
    marginBottom: 1,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stepCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
  },
  stepCircleActive: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  stepCircleInactive: {
    backgroundColor: '#FFFFFF',
    borderColor: '#E5E5E5',
  },
  stepText: {
    fontSize: 14,
    fontWeight: '600',
  },
  stepTextActive: {
    color: '#FFFFFF',
  },
  stepTextInactive: {
    color: '#999999',
  },
  stepLine: {
    width: 40,
    height: 2,
    marginHorizontal: 8,
  },
  stepLineActive: {
    backgroundColor: '#007AFF',
  },
  stepLineInactive: {
    backgroundColor: '#E5E5E5',
  },
  scrollView: {
    flex: 1,
  },
  stepContent: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    margin: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: 8,
  },
  stepSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 20,
  },
  placeholderText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    lineHeight: 24,
  },
  navigationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E5E5',
  },
  buttonSpacer: {
    flex: 1,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  primaryButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    minWidth: 100,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '600',
  },
});