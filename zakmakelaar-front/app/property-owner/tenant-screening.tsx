import React from 'react';
import { View, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { TenantScreeningInterface } from '../../components/property-owner/TenantScreeningInterface';

export default function TenantScreeningScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <TenantScreeningInterface />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8F8',
  },
});
