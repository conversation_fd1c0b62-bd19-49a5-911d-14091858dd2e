import React from 'react';
import {
  StyleSheet,
  SafeAreaView,
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { SafetyControlsPanel } from '../components/autonomous/SafetyControlsPanel';

// Define theme colors
const THEME = {
  primary: '#4361ee',
  secondary: '#7209b7',
  accent: '#f72585',
  dark: '#0a0a18',
  light: '#ffffff',
  gray: '#6b7280',
  lightGray: '#f3f4f6',
  danger: '#ef4444',
};

// Header Component
const Header = ({
  title,
  onBackPress,
}: {
  title: string;
  onBackPress: () => void;
}) => {
  const insets = useSafeAreaInsets();
  
  return (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <View style={styles.headerContent}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={onBackPress}
        >
          <Ionicons name="chevron-back" size={24} color={THEME.dark} />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>{title}</Text>
        
        <View style={styles.headerSpacer} />
      </View>
      
      <View style={styles.headerWarning}>
        <Ionicons name="warning" size={16} color={THEME.danger} />
        <Text style={styles.headerWarningText}>
          Safety controls for autonomous operations
        </Text>
      </View>
    </View>
  );
};

export default function SafetyControlsScreen() {
  const router = useRouter();
  
  const handleBackPress = () => {
    router.back();
  };
  
  return (
    <SafeAreaView style={styles.container}>
      <Header
        title="Safety Controls"
        onBackPress={handleBackPress}
      />
      
      <SafetyControlsPanel />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.lightGray,
  },
  header: {
    backgroundColor: THEME.light,
    borderBottomWidth: 1,
    borderBottomColor: THEME.lightGray,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerButton: {
    padding: 8,
    width: 40,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: THEME.dark,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  headerWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  headerWarningText: {
    fontSize: 14,
    color: THEME.danger,
    marginLeft: 8,
    fontWeight: '500',
  },
});