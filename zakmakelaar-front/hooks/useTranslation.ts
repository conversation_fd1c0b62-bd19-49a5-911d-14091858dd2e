import { useTranslation as useI18nTranslation } from 'react-i18next';

// Custom hook that wraps react-i18next's useTranslation
// This allows us to add any app-specific translation logic if needed
export const useTranslation = () => {
  const { t, i18n } = useI18nTranslation();

  const changeLanguage = async (language: string) => {
    try {
      await i18n.changeLanguage(language);
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  const getCurrentLanguage = () => i18n.language;

  const isLanguageLoaded = () => i18n.isInitialized;

  return {
    t,
    i18n,
    changeLanguage,
    getCurrentLanguage,
    isLanguageLoaded,
  };
};

export default useTranslation;