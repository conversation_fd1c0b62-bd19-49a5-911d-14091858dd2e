import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authService, User, UserPreferences } from '../services/authService';
import { queryKeys, cacheUtils } from '../services/queryClient';

// Auth queries
export const useCurrentUser = () => {
  return useQuery({
    queryKey: queryKeys.auth.user(),
    queryFn: async () => {
      const response = await authService.getCurrentUser();
      if (!response.success) {
        throw new Error(response.message || 'Failed to get user data');
      }
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on 401 errors
      if (error?.status === 401) {
        return false;
      }
      return failureCount < 2;
    },
  });
};

export const useUserPreferences = () => {
  return useQuery({
    queryKey: queryKeys.auth.preferences(),
    queryFn: async () => {
      const userResponse = await authService.getCurrentUser();
      if (!userResponse.success) {
        throw new Error(userResponse.message || 'Failed to get user preferences');
      }
      return userResponse.data?.preferences;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: true, // Always enabled since preferences are critical
  });
};

// Auth mutations
export const useLogin = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ email, password }: { email: string; password: string }) => {
      const response = await authService.login({ email, password });
      if (!response.success) {
        throw new Error(response.message || 'Login failed');
      }
      return response.data;
    },
    onSuccess: (data) => {
      // Update user cache
      queryClient.setQueryData(queryKeys.auth.user(), data?.user);
      
      // Invalidate and refetch user-related queries
      cacheUtils.invalidateAuth();
      
      // Prefetch user-specific data
      queryClient.prefetchQuery({
        queryKey: queryKeys.auth.preferences(),
        queryFn: () => authService.getCurrentUser().then(r => r.data?.preferences),
      });
    },
    onError: (error) => {
      console.error('Login error:', error);
      // Clear any stale auth data
      queryClient.removeQueries({ queryKey: queryKeys.auth.all });
    },
  });
};

export const useRegister = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({
      email,
      password,
      firstName,
      lastName,
    }: {
      email: string;
      password: string;
      firstName?: string;
      lastName?: string;
    }) => {
      const response = await authService.register({
        email,
        password,
        firstName,
        lastName,
      });
      if (!response.success) {
        throw new Error(response.message || 'Registration failed');
      }
      return response.data;
    },
    onSuccess: (data) => {
      // Update user cache
      queryClient.setQueryData(queryKeys.auth.user(), data?.user);
      
      // Invalidate auth queries
      cacheUtils.invalidateAuth();
    },
    onError: (error) => {
      console.error('Registration error:', error);
    },
  });
};

export const useLogout = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      await authService.logout();
    },
    onSuccess: () => {
      // Clear all cached data on logout
      cacheUtils.clearAll();
    },
    onError: (error) => {
      console.error('Logout error:', error);
      // Even if logout fails, clear local cache
      cacheUtils.clearAll();
    },
  });
};

export const useUpdatePreferences = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (preferences: Partial<UserPreferences>) => {
      const response = await authService.updatePreferences(preferences);
      if (!response.success) {
        throw new Error(response.message || 'Failed to update preferences');
      }
      return response.data;
    },
    onMutate: async (newPreferences) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.auth.preferences() });
      
      // Snapshot previous value
      const previousPreferences = queryClient.getQueryData(queryKeys.auth.preferences());
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.auth.preferences(), (old: UserPreferences | undefined) => ({
        ...old,
        ...newPreferences,
      } as UserPreferences));
      
      return { previousPreferences };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousPreferences) {
        queryClient.setQueryData(queryKeys.auth.preferences(), context.previousPreferences);
      }
      console.error('Update preferences error:', error);
    },
    onSuccess: (data) => {
      // Update user cache with new data
      queryClient.setQueryData(queryKeys.auth.user(), data);
      
      // Invalidate related queries that depend on preferences
      cacheUtils.invalidateMatches();
      cacheUtils.invalidateListings();
    },
    onSettled: () => {
      // Always refetch after mutation
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.preferences() });
    },
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (profileData: Partial<User>) => {
      const response = await authService.updateProfile(profileData);
      if (!response.success) {
        throw new Error(response.message || 'Failed to update profile');
      }
      return response.data;
    },
    onMutate: async (newProfileData) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.auth.user() });
      
      // Snapshot previous value
      const previousUser = queryClient.getQueryData(queryKeys.auth.user());
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.auth.user(), (old: User | undefined) => ({
        ...old,
        ...newProfileData,
      } as User));
      
      return { previousUser };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousUser) {
        queryClient.setQueryData(queryKeys.auth.user(), context.previousUser);
      }
      console.error('Update profile error:', error);
    },
    onSuccess: (data) => {
      // Update user cache
      queryClient.setQueryData(queryKeys.auth.user(), data);
    },
    onSettled: () => {
      // Always refetch after mutation
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() });
    },
  });
};

// Utility hooks
export const useAuthStatus = () => {
  const { data: user, isLoading, error } = useCurrentUser();
  
  return {
    user,
    isAuthenticated: !!user,
    isLoading,
    error,
  };
};

export const useIsAuthenticated = () => {
  const { data: user } = useCurrentUser();
  return !!user;
};