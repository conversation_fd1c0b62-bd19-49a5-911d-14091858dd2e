import { create } from "zustand";
import {
  Listing,
  ListingFilters,
  ListingQuery,
  listingsService,
} from "../services/listingsService";

interface ListingsState {
  // State
  listings: Listing[];
  currentListing: Listing | null;
  savedListings: Listing[];
  recentListings: Listing[];
  recommendedListings: Listing[];
  filters: ListingFilters;
  searchQuery: string;
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
  fallbackMessage: string | null;

  // Pagination
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasMore: boolean;

  // Actions
  fetchListings: (query?: ListingQuery, append?: boolean) => Promise<void>;
  fetchListing: (id: string) => Promise<void>;
  fetchSavedListings: () => Promise<void>;
  fetchRecentListings: (limit?: number) => Promise<void>;
  fetchPreferenceBasedListings: (limit?: number) => Promise<void>;
  searchListings: (
    searchTerm: string,
    filters?: ListingFilters
  ) => Promise<void>;
  saveListing: (listingId: string) => Promise<boolean>;
  unsaveListing: (listingId: string) => Promise<boolean>;
  setFilters: (filters: ListingFilters) => void;
  setSearchQuery: (query: string) => void;
  clearListings: () => void;
  clearError: () => void;
  clearFallbackMessage: () => void;
  loadMore: () => Promise<void>;
}

export const useListingsStore = create<ListingsState>((set, get) => ({
  // Initial state
  listings: [],
  currentListing: null,
  savedListings: [],
  recentListings: [],
  recommendedListings: [],
  filters: {},
  searchQuery: "",
  isLoading: false,
  isLoadingMore: false,
  error: null,
  currentPage: 1,
  totalPages: 1,
  totalCount: 0,
  hasMore: false,
  fallbackMessage: null,

  // Actions
  fetchListings: async (query?: ListingQuery, append = false) => {
    const state = get();

    if (append) {
      set({ isLoadingMore: true, error: null });
    } else {
      set({ isLoading: true, error: null });
    }

    try {
      const finalQuery: ListingQuery = {
        ...state.filters,
        ...query,
        page: append ? state.currentPage + 1 : 1,
        limit: query?.limit || 20,
      };

      if (state.searchQuery) {
        finalQuery.search = state.searchQuery;
      }

      const response = await listingsService.getListings(finalQuery);

      if (response.success && response.data) {
        const newListings = response.data.listings;

        set({
          listings: append ? [...state.listings, ...newListings] : newListings,
          currentPage: finalQuery.page || 1,
          totalPages: response.pagination?.totalPages || 1,
          totalCount: response.pagination?.total || 0,
          hasMore:
            (finalQuery.page || 1) < (response.pagination?.totalPages || 1),
          isLoading: false,
          isLoadingMore: false,
        });
      } else {
        set({
          error: response.message || "Failed to fetch listings",
          isLoading: false,
          isLoadingMore: false,
        });
      }
    } catch (error: any) {
      set({
        error: error.message || "Failed to fetch listings",
        isLoading: false,
        isLoadingMore: false,
      });
    }
  },

  fetchListing: async (id: string) => {
    console.log("🏪 Store: fetchListing called with ID:", id);
    set({ isLoading: true, error: null });

    try {
      console.log("📡 Store: Calling listingsService.getListing...");
      const response = await listingsService.getListing(id);
      console.log("📊 Store: API response:", {
        success: response.success,
        hasData: !!response.data,
        message: response.message,
      });

      if (response.success && response.data) {
        console.log("✅ Store: Setting currentListing:", {
          id: response.data._id,
          title: response.data.title,
          price: response.data.price,
          location: response.data.location,
        });
        set({
          currentListing: response.data,
          isLoading: false,
        });
      } else {
        console.log("❌ Store: API response failed:", response.message);
        set({
          error: response.message || "Failed to fetch listing",
          isLoading: false,
        });
      }
    } catch (error: any) {
      console.log("💥 Store: Exception in fetchListing:", error.message);
      set({
        error: error.message || "Failed to fetch listing",
        isLoading: false,
      });
    }
  },

  fetchSavedListings: async () => {
    set({ isLoading: true, error: null });

    try {
      const response = await listingsService.getSavedListings();

      if (response.success && response.data) {
        set({
          savedListings: response.data.listings,
          isLoading: false,
        });
      } else {
        set({
          error: response.message || "Failed to fetch saved listings",
          isLoading: false,
        });
      }
    } catch (error: any) {
      set({
        error: error.message || "Failed to fetch saved listings",
        isLoading: false,
      });
    }
  },

  fetchRecentListings: async (limit = 10) => {
    set({ isLoading: true, error: null });

    try {
      const response = await listingsService.getRecentListings(limit);

      if (response.success && response.data) {
        set({
          recentListings: response.data.listings,
          isLoading: false,
        });
      } else {
        set({
          error: response.message || "Failed to fetch recent listings",
          isLoading: false,
        });
      }
    } catch (error: any) {
      set({
        error: error.message || "Failed to fetch recent listings",
        isLoading: false,
      });
    }
  },

  fetchPreferenceBasedListings: async (limit = 10) => {
    set({ isLoading: true, error: null, fallbackMessage: null });

    try {
      const response = await listingsService.getPreferenceBasedListings(limit);

      if (response.success && response.data) {
        set({
          recommendedListings: response.data.listings,
          isLoading: false,
          fallbackMessage: (response as any).fallbackUsed ? response.message : null,
        });
      } else {
        set({
          error: response.message || "Failed to fetch preference-based listings",
          isLoading: false,
        });
      }
    } catch (error: any) {
      set({
        error: error.message || "Failed to fetch preference-based listings",
        isLoading: false,
      });
    }
  },

  searchListings: async (searchTerm: string, filters?: ListingFilters) => {
    set({
      searchQuery: searchTerm,
      filters: filters || get().filters,
      isLoading: true,
      error: null,
    });

    try {
      const response = await listingsService.searchListings(
        searchTerm,
        filters
      );

      if (response.success && response.data) {
        set({
          listings: response.data.listings,
          currentPage: 1,
          totalPages: response.pagination?.totalPages || 1,
          totalCount: response.pagination?.total || 0,
          hasMore: 1 < (response.pagination?.totalPages || 1),
          isLoading: false,
        });
      } else {
        set({
          error: response.message || "Search failed",
          isLoading: false,
        });
      }
    } catch (error: any) {
      set({
        error: error.message || "Search failed",
        isLoading: false,
      });
    }
  },

  saveListing: async (listingId: string) => {
    try {
      const response = await listingsService.saveListing(listingId);

      if (response.success) {
        // Update the listing in the current listings array
        const state = get();
        const updatedListings = state.listings.map((listing) =>
          listing._id === listingId ? { ...listing, isSaved: true } : listing
        );

        set({ listings: updatedListings });

        // Refresh saved listings
        await get().fetchSavedListings();

        return true;
      } else {
        set({ error: response.message || "Failed to save listing" });
        return false;
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to save listing" });
      return false;
    }
  },

  unsaveListing: async (listingId: string) => {
    try {
      const response = await listingsService.unsaveListing(listingId);

      if (response.success) {
        // Update the listing in the current listings array
        const state = get();
        const updatedListings = state.listings.map((listing) =>
          listing._id === listingId ? { ...listing, isSaved: false } : listing
        );

        // Remove from saved listings
        const updatedSavedListings = state.savedListings.filter(
          (listing) => listing._id !== listingId
        );

        set({
          listings: updatedListings,
          savedListings: updatedSavedListings,
        });

        return true;
      } else {
        set({ error: response.message || "Failed to unsave listing" });
        return false;
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to unsave listing" });
      return false;
    }
  },

  setFilters: (filters: ListingFilters) => {
    set({ filters });
  },

  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
  },

  clearListings: () => {
    set({
      listings: [],
      currentListing: null,
      currentPage: 1,
      totalPages: 1,
      totalCount: 0,
      hasMore: false,
      searchQuery: "",
      error: null,
    });
  },

  clearError: () => {
    set({ error: null });
  },

  clearFallbackMessage: () => {
    set({ fallbackMessage: null });
  },

  loadMore: async () => {
    const state = get();

    if (state.hasMore && !state.isLoadingMore) {
      await state.fetchListings(undefined, true);
    }
  },
}));
